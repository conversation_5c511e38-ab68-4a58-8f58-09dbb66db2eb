// Utility for memory monitoring and timeouts
export const TIMEOUT = 30000; // 30 seconds
export const MEMORY_LIMIT = 500 * 1024 * 1024; // 500MB

export interface MemoryMonitorOptions {
  timeout?: number;
  memoryLimit?: number;
  onTimeout?: () => void;
  onMemoryLimit?: () => void;
}

export class MemoryMonitor {
  private timeoutId: NodeJS.Timeout | null = null;
  private memoryIntervalId: NodeJS.Timeout | null = null;
  private options: Required<MemoryMonitorOptions>;

  constructor(options: MemoryMonitorOptions = {}) {
    this.options = {
      timeout: options.timeout || TIMEOUT,
      memoryLimit: options.memoryLimit || MEMORY_LIMIT,
      onTimeout: options.onTimeout || (() => { throw new Error('Operation timeout'); }),
      onMemoryLimit: options.onMemoryLimit || (() => { throw new Error('Memory limit exceeded'); }),
    };
  }

  start(): void {
    // Set up timeout
    this.timeoutId = setTimeout(() => {
      this.options.onTimeout();
    }, this.options.timeout);

    // Set up memory monitoring
    this.memoryIntervalId = setInterval(() => {
      const memoryUsage = process.memoryUsage();
      if (memoryUsage.heapUsed > this.options.memoryLimit) {
        this.options.onMemoryLimit();
      }
    }, 5000); // Check every 5 seconds
  }

  stop(): void {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }
    if (this.memoryIntervalId) {
      clearInterval(this.memoryIntervalId);
      this.memoryIntervalId = null;
    }
  }
}

export function withMemoryMonitor<T>(
  fn: () => Promise<T>,
  options?: MemoryMonitorOptions
): Promise<T> {
  return new Promise(async (resolve, reject) => {
    const monitor = new MemoryMonitor({
      ...options,
      onTimeout: () => {
        monitor.stop();
        reject(new Error('Operation timeout'));
      },
      onMemoryLimit: () => {
        monitor.stop();
        reject(new Error('Memory limit exceeded'));
      },
    });

    try {
      monitor.start();
      const result = await fn();
      monitor.stop();
      resolve(result);
    } catch (error) {
      monitor.stop();
      reject(error);
    }
  });
}