// Simulated AI service for TipTap editor commands
export interface AICommandOptions {
  text?: string;
  tone?: 'formal' | 'casual' | 'professional' | 'friendly';
  length?: 'shorter' | 'longer';
  style?: 'academic' | 'creative' | 'business' | 'simple';
  fullDocument?: string; // 完整文档内容（markdown 格式）
  documentContext?: {
    wordCount: number;
    characterCount: number;
    paragraphCount: number;
    headingCount: number;
  };
  cursorPosition?: number; // 光标位置
}

export interface CustomPromptOptions {
  cursorPosition?: number; // 光标位置
  documentLength?: number; // 文档长度
  hasStructure?: boolean; // 是否有结构
}

export interface AIResponse {
  success: boolean;
  content: string;
  error?: string;
}

// Simulate API delay
const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

// Mock responses for different commands
const mockResponses = {
  continue: {
    templates: [
      '接下来，我们可以从以下几个方面来深入探讨：',
      '基于上述内容，建议进一步研究：',
      '为了更好地理解这个主题，让我们思考：',
      '这个观点很有趣，我们可以继续分析：',
    ],
  },
  summarize: {
    templates: [
      '总的来说，本文主要讨论了{key_points}。核心观点是{main_idea}。',
      '综上所述，我们可以得出以下结论：{conclusions}',
      '本文的重点在于{focus}，通过{method}的方式进行了阐述。',
    ],
  },
  polish: {
    templates: [
      '经过优化，表达更加清晰流畅，逻辑结构更加合理。',
      '调整后的文本更加精炼，用词更加准确。',
      '优化后的内容更具可读性，表达更加专业。',
    ],
  },
  expand: {
    templates: [
      '此外，还需要考虑{additional_point}。',
      '从另一个角度来看，{perspective}。',
      '值得注意的是，{note}。',
      '具体来说，{detail}。',
    ],
  },
  shorten: {
    templates: [
      '精简后保留了核心内容：{core_content}。',
      '简化版本重点突出了{key_focus}。',
      '压缩后仍包含主要信息：{main_info}。',
    ],
  },
  adjustTone: {
    formal: '调整为正式语气后，用词更加规范，表达更加严谨。',
    casual: '调整为轻松语气后，文字更加亲切自然。',
    professional: '调整为专业语气后，术语使用更加准确。',
    friendly: '调整为友好语气后，表达更加温暖亲切。',
  },
};

// Extract key points from text for summarization
const extractKeyPoints = (text: string): string[] => {
  const sentences = text
    .split(/[。！？.!?]/)
    .filter((s) => s.trim().length > 0);
  return sentences.slice(0, 3).map((s) => s.trim());
};

// Analyze document content to determine context and style
const analyzeDocumentContent = (
  markdown: string
): {
  documentType: 'technical' | 'business' | 'academic' | 'creative' | 'general';
  topics: string[];
  tone: 'formal' | 'casual' | 'professional' | 'friendly';
  hasStructure: boolean;
} => {
  const lowerContent = markdown.toLowerCase();

  // Determine document type
  let documentType:
    | 'technical'
    | 'business'
    | 'academic'
    | 'creative'
    | 'general' = 'general';
  if (
    lowerContent.includes('api') ||
    lowerContent.includes('代码') ||
    lowerContent.includes('技术') ||
    lowerContent.includes('开发')
  ) {
    documentType = 'technical';
  } else if (
    lowerContent.includes('业务') ||
    lowerContent.includes('商业') ||
    lowerContent.includes('市场') ||
    lowerContent.includes('销售')
  ) {
    documentType = 'business';
  } else if (
    lowerContent.includes('研究') ||
    lowerContent.includes('分析') ||
    lowerContent.includes('理论') ||
    lowerContent.includes('学术')
  ) {
    documentType = 'academic';
  } else if (
    lowerContent.includes('故事') ||
    lowerContent.includes('创意') ||
    lowerContent.includes('想象')
  ) {
    documentType = 'creative';
  }

  // Extract potential topics
  const topics: string[] = [];
  const topicKeywords = [
    '项目',
    '产品',
    '服务',
    '技术',
    '方案',
    '策略',
    '计划',
    '目标',
    '问题',
    '解决',
  ];
  topicKeywords.forEach((keyword) => {
    if (lowerContent.includes(keyword)) {
      topics.push(keyword);
    }
  });

  // Determine tone
  let tone: 'formal' | 'casual' | 'professional' | 'friendly' = 'professional';
  if (
    lowerContent.includes('您') ||
    lowerContent.includes('请') ||
    lowerContent.includes('建议')
  ) {
    tone = 'formal';
  } else if (
    lowerContent.includes('我们') ||
    lowerContent.includes('大家') ||
    lowerContent.includes('一起')
  ) {
    tone = 'friendly';
  } else if (
    lowerContent.includes('哈哈') ||
    lowerContent.includes('呵呵') ||
    lowerContent.includes('😊')
  ) {
    tone = 'casual';
  }

  // Check if document has structure
  const hasStructure =
    markdown.includes('#') ||
    markdown.includes('##') ||
    markdown.includes('-') ||
    markdown.includes('1.');

  return {
    documentType,
    topics,
    tone,
    hasStructure,
  };
};

// Simulate AI command execution
export const simulateAICommand = async (
  command: string,
  selectedText: string,
  options: AICommandOptions = {}
): Promise<AIResponse> => {
  await delay(1000 + Math.random() * 2000); // Simulate 1-3 second delay

  try {
    let result = '';

    switch (command) {
      case 'continue': {
        const continueTemplate =
          mockResponses.continue.templates[
            Math.floor(Math.random() * mockResponses.continue.templates.length)
          ];

        // 如果有完整文档内容，生成更智能的续写
        if (options.fullDocument) {
          const documentLength = options.fullDocument.length;
          const lastParagraph = options.fullDocument.split('\n\n').pop() || '';
          const analysis = analyzeDocumentContent(options.fullDocument);

          // 根据文档类型和内容生成更相关的续写
          if (
            lastParagraph.includes('总结') ||
            lastParagraph.includes('结论')
          ) {
            if (analysis.documentType === 'technical') {
              result =
                '基于以上技术分析，接下来我们需要考虑具体的实现方案。在技术选型方面，建议重点关注性能优化、可扩展性和维护成本...';
            } else if (analysis.documentType === 'business') {
              result =
                '综合上述商业分析，下一步应该制定详细的执行计划。从市场策略角度来看，需要重点关注用户需求、竞争优势和盈利模式...';
            } else {
              result =
                '基于以上分析，我们可以进一步探讨实际应用场景。首先，在实践中需要注意的关键要素包括...';
            }
          } else if (
            lastParagraph.includes('问题') ||
            lastParagraph.includes('挑战')
          ) {
            result =
              '针对这些挑战，我们可以采取以下解决策略：\n\n1. **系统性方法论**：建立标准化的问题解决流程\n2. **团队协作机制**：加强跨部门沟通与协调\n3. **持续优化**：定期评估和改进现有方案\n4. **风险管控**：提前识别潜在风险并制定应对措施';
          } else if (documentLength < 500) {
            if (analysis.documentType === 'technical') {
              result =
                '让我们深入探讨这个技术话题的核心要点。从架构设计角度来看，需要考虑以下几个关键维度：系统性能、数据安全、用户体验和可维护性...';
            } else if (analysis.documentType === 'creative') {
              result =
                '这个创意很有趣！让我们继续发挥想象力，从不同的角度来丰富这个故事。我们可以考虑角色的背景、情节的发展以及可能的转折点...';
            } else {
              result =
                '让我们深入探讨这个话题的核心要点。从理论基础来看，需要考虑以下几个关键维度...';
            }
          } else {
            // 根据文档结构和类型生成续写
            if (analysis.hasStructure) {
              result =
                continueTemplate +
                '\n\n## 深入分析\n\n具体而言，我们可以从以下几个方面继续深化讨论：\n\n- **理论基础**：进一步阐述相关概念和原理\n- **实践应用**：结合具体案例进行分析\n- **未来展望**：探讨发展趋势和可能性';
            } else {
              result =
                continueTemplate +
                '具体而言，我们可以从以下几个方面继续深化讨论...';
            }
          }
        } else {
          result = continueTemplate;
        }
        break;
      }

      case 'summarize': {
        if (options.fullDocument && options.fullDocument.length > 100) {
          // 使用完整文档进行总结
          const analysis = analyzeDocumentContent(options.fullDocument);
          const stats = options.documentContext;

          result = `## 文档总结\n\n**核心内容**：本文档主要围绕${analysis.topics.length > 0 ? analysis.topics.join('、') : '相关主题'}展开讨论。\n\n**文档类型**：${analysis.documentType === 'technical' ? '技术文档' : analysis.documentType === 'business' ? '商业文档' : analysis.documentType === 'academic' ? '学术文档' : analysis.documentType === 'creative' ? '创意文档' : '通用文档'}\n\n**主要观点**：\n- 文档结构${analysis.hasStructure ? '清晰' : '相对简单'}，共包含${stats?.paragraphCount || 0}个段落\n- 内容风格偏向${analysis.tone === 'formal' ? '正式' : analysis.tone === 'casual' ? '轻松' : analysis.tone === 'professional' ? '专业' : '友好'}表达\n- 总字数约${stats?.wordCount || 0}字，信息密度适中\n\n**建议**：可以考虑在关键部分增加更多细节说明，以提升内容的完整性。`;
        } else {
          // 使用选中文本进行总结
          const keyPoints = extractKeyPoints(selectedText);
          const summaryTemplate = mockResponses.summarize.templates[0];
          result = summaryTemplate
            .replace('{key_points}', keyPoints.join('、'))
            .replace('{main_idea}', keyPoints[0] || '核心观点');
        }
        break;
      }

      case 'polish': {
        const polishTemplate =
          mockResponses.polish.templates[
            Math.floor(Math.random() * mockResponses.polish.templates.length)
          ];
        result = polishTemplate;
        break;
      }

      case 'expand': {
        if (options.fullDocument && selectedText.length > 20) {
          // 基于完整文档上下文进行扩写
          const analysis = analyzeDocumentContent(options.fullDocument);

          if (analysis.documentType === 'technical') {
            result = `${selectedText}\n\n### 技术细节\n\n从技术实现角度来看，这个方案需要考虑以下几个关键要素：\n\n**架构设计**：采用模块化设计思路，确保系统的可扩展性和可维护性。\n\n**性能优化**：通过合理的缓存策略和数据库优化，提升系统响应速度。\n\n**安全考虑**：实施多层安全防护机制，包括数据加密、访问控制和审计日志。\n\n**监控运维**：建立完善的监控体系，及时发现和解决潜在问题。`;
          } else if (analysis.documentType === 'business') {
            result = `${selectedText}\n\n### 商业价值分析\n\n从商业角度深入分析，这个观点具有重要的战略意义：\n\n**市场机会**：当前市场环境为此类方案提供了良好的发展机遇。\n\n**竞争优势**：通过差异化定位，可以在竞争中占据有利地位。\n\n**风险评估**：需要关注市场变化、技术风险和资源投入等因素。\n\n**实施路径**：建议采用分阶段实施策略，逐步验证和优化方案。`;
          } else {
            result = `${selectedText}\n\n### 深入探讨\n\n基于以上内容，我们可以从多个维度进行更深入的分析：\n\n**理论基础**：相关理论为我们提供了重要的指导框架。\n\n**实践经验**：结合实际案例，可以更好地理解和应用这些概念。\n\n**发展趋势**：未来的发展方向值得我们持续关注和研究。\n\n**应用前景**：在不同领域都有广阔的应用空间和发展潜力。`;
          }
        } else {
          // 使用原有的扩写逻辑
          const expandPoints = [
            '实际应用场景',
            '相关理论基础',
            '未来发展趋势',
            '潜在挑战与解决方案',
          ];
          const expandTemplate =
            mockResponses.expand.templates[
              Math.floor(Math.random() * mockResponses.expand.templates.length)
            ];
          result = expandTemplate.replace(
            '{additional_point}',
            expandPoints[Math.floor(Math.random() * expandPoints.length)]
          );
        }
        break;
      }

      case 'shorten': {
        const shortTemplate =
          mockResponses.shorten.templates[
            Math.floor(Math.random() * mockResponses.shorten.templates.length)
          ];
        result = shortTemplate.replace(
          '{core_content}',
          selectedText.substring(0, 50) + '...'
        );
        break;
      }

      case 'adjustTone': {
        const toneKey = options.tone || 'formal';
        const toneResponse =
          mockResponses.adjustTone[
            toneKey as keyof typeof mockResponses.adjustTone
          ];
        result = toneResponse || mockResponses.adjustTone.formal;
        break;
      }

      default:
        return {
          success: false,
          error: 'Unknown command',
        };
    }

    return {
      success: true,
      content: result,
    };
  } catch (error) {
    return {
      success: false,
      error: 'AI service temporarily unavailable',
    };
  }
};

// Simulate custom prompt
export const simulateCustomPrompt = async (
  prompt: string,
  context = '',
  options: CustomPromptOptions = {}
): Promise<AIResponse> => {
  await delay(1500 + Math.random() * 2500);

  try {
    // 分析文档上下文
    const hasContext = context && context.trim().length > 0;
    const contextLength = context.length;
    const contextWords = hasContext ? context.split(/\s+/).length : 0;
    const hasCursorInfo =
      context.includes('[在此处补充内容]') ||
      context.includes('[在文档开头续写内容]') ||
      context.includes('[在文档末尾续写内容]') ||
      context.includes('[在光标当前位置续写内容]');

    // 根据提示词和上下文生成更智能的回复
    let response = '';

    if (prompt.includes('总结') || prompt.includes('概括')) {
      if (hasContext) {
        response = `基于您的文档内容（约${contextWords}字）${hasCursorInfo ? '，我注意到您指定了光标位置' : ''}，我为您总结如下要点：\n\n1. 核心主题：从文档结构来看，主要围绕...\n2. 关键观点：文中提到的重要概念包括...\n3. 结论建议：综合分析后，建议...`;
      } else {
        response = '请提供需要总结的内容，我将为您提取关键要点和核心观点。';
      }
    } else if (
      prompt.includes('扩展') ||
      prompt.includes('详细') ||
      prompt.includes('展开')
    ) {
      if (hasContext) {
        if (hasCursorInfo) {
          response =
            '我看到您在文档中标记了需要续写的位置，基于标记位置的上下文，建议从以下几个维度进行扩展：\n\n**接下来的内容**\n根据前文脉络，可以继续阐述相关要点\n\n**深入分析**\n在标记位置补充更多细节和案例说明\n\n**逻辑拓展**\n从这里可以自然地过渡到下一个相关话题';
        } else {
          response =
            '基于您现有的内容，我建议从以下几个维度进行扩展：\n\n**理论基础**\n从学术角度来看，这个话题涉及...\n\n**实践应用**\n在实际操作中，需要考虑...\n\n**案例分析**\n相关的成功案例包括...';
        }
      } else {
        response =
          '请告诉我您希望扩展哪个具体话题，我将为您提供详细的分析和补充内容。';
      }
    } else if (
      prompt.includes('改进') ||
      prompt.includes('优化') ||
      prompt.includes('建议')
    ) {
      if (hasContext) {
        response = `针对您的文档内容${hasCursorInfo ? '以及您标记的续写位置' : ''}，我提出以下改进建议：\n\n**结构优化**\n- 建议调整段落逻辑顺序\n- 增加小标题提升可读性\n\n**内容完善**\n- 补充具体案例和数据支撑\n- 加强论证的逻辑性\n\n**表达优化**\n- 使用更精准的专业术语\n- 增强语言的感染力${hasCursorInfo ? '\n\n**位置建议**\n根据标记的位置，建议在此处增加承上启下的过渡内容' : ''}`;
      } else {
        response = '请分享您希望改进的内容，我将为您提供具体的优化建议。';
      }
    } else if (prompt.includes('续写') || prompt.includes('继续')) {
      if (hasContext && hasCursorInfo) {
        response =
          '我看到您在文档中标记了"[在此处补充内容]"的位置，我将基于该位置的上下文为您续写：\n\n根据前文的逻辑脉络和后续内容的需要，我建议在标记位置添加以下内容来承上启下，保持文章的连贯性和逻辑性...';
      } else if (hasContext) {
        response =
          '基于您的文档内容，我将为您续写。建议从以下方向继续发展内容...\n\n结合现有的讨论基础，我们可以深入探讨...';
      } else {
        response =
          '请提供需要续写的文档内容，我将根据上下文为您生成合适的续写内容。';
      }
    } else {
      // 通用回复，根据上下文调整
      const responses = [
        `关于"${prompt}"${hasContext ? `，结合您的文档内容（${contextWords}字）` : ''}${hasCursorInfo ? '以及您标记的续写位置' : ''}，我的建议是：这个问题需要从多个维度来分析。${hasContext ? '从您提供的背景信息来看，' : ''}建议采用系统性的方法来解决。`,
        `针对"${prompt}"这个需求${hasContext ? `，基于您现有的${Math.ceil(contextWords / 100)}百字文档` : ''}${hasCursorInfo ? '和您标记的具体位置' : ''}，我认为可以从以下几个方面入手：首先明确目标，然后制定具体的实施方案。`,
        `您提到的"${prompt}"很有意思${hasContext ? '，从您的文档结构来看' : ''}${hasCursorInfo ? '，结合您标记的续写位置' : ''}，这个话题值得深入探讨。建议我们先梳理核心要点，再逐步展开分析。`,
      ];
      response = responses[Math.floor(Math.random() * responses.length)];
    }

    return {
      success: true,
      content: response,
    };
  } catch (error) {
    return {
      success: false,
      error: 'AI service temporarily unavailable',
    };
  }
};
