import api from '@/lib/api';

export const getProjectList = (data: any) => {
  return api.get('/app/v1/pbl/projects', {
    params: data,
  });
};

// delete project
export const deleteProject = (projectId: string) => {
  return api.delete(`/app/v1/pbl/projects/${projectId}`);
};

// create project
export const createProject = (data: any) => {
  return api.post('/app/v1/pbl/projects', data);
};

// update project
export const updateProject = (projectId: string, data: any) => {
  return api.put(`/app/v1/pbl/projects/${projectId}`, data);
};

// get project detail
export const getProjectDetail = (projectId: string) => {
  return api.get(`/app/v1/pbl/projects/${projectId}`);
};

// 获取 pbl 内容
export const getPblContent = (projectId: string) => {
  return api.get(`/app/v1/pbl/projects/${projectId}/contents`);
};

// 更新 pbl 内容
export const updatePblContent = (projectId: string, data: any) => {
  return api.put(`/app/v1/pbl/projects/${projectId}/contents`, data);
};

// 生成菜单
export const generateMenu = (data: any) => {
  return api.post('/app/v1/pbl/projects/suggest', data);
};

// 获取菜单
export const getMenu = (projectId: string) => {
  return api.get('/app/v1/pbl/project-nodes', {
    params: {
      projectId,
    },
  });
};

// 更新整个菜单
export const updateMenu = (projectId: string, data: any) => {
  return api.put(`/app/v1/pbl/projects/${projectId}/nodes`, data);
};

// 新增菜单
export const addMenu = (data: {
  projectId: string;
  parentId: string;
  title: string;
  description: string;
}) => {
  return api.post('/app/v1/pbl/project-nodes', data);
};

// 更新单个菜单
export const updateMenuNode = (nodeId: string, data: any) => {
  return api.put(`/app/v1/pbl/project-nodes/${nodeId}`, data);
};

// 删除菜单
export const deleteMenu = (nodeId: string) => {
  return api.delete(`/app/v1/pbl/project-nodes/${nodeId}`);
};

// 项目节点内容更新
export const updateMenuContent = (data: {
  nodeId: string;
  recordData: any;
}) => {
  return api.post('/app/v1/pbl/project-records', data);
};

// 智能助手生成内容
export const aiGenerateContent = (data: any): any => {
  // 如果是流式模式，添加流式请求头
  // if (data.response_mode === 'streaming') {
  //   return api.post('/app/v1/pbl/projects/assistant', data, {
  //     headers: {
  //       'Content-Type': 'application/octet-stream',
  //     },
  //   });
  // }

  // 非流式模式，使用原有的请求
  return api.post('/app/v1/pbl/projects/assistant', data);
};

// 素材列表
export const getMaterialList = (data?: any) => {
  return api.get('/app/v1/observe/media', {
    params: data,
  });
};

// 素材删除
export const deleteMaterial = (mediaId: string) => {
  return api.delete(`/app/v1/observe/media/${mediaId}`);
};

// 素材详情
export const getMaterialDetail = (mediaId: string) => {
  return api.get(`/app/v1/observe/media/${mediaId}`);
};

// 素材上传
export const uploadMaterial = (data: any) => {
  return api.post('/app/v1/observe/media', data);
};

// 观察记录班级列表
export const getObservationClassList = (data: any) => {
  return api.get('/app/v1/observe/observations/by-classes', { params: data });
};

// 观察记录列表
export const getObservationList = (data: any) => {
  return api.get('/app/v1/observe/observations', {
    params: data,
  });
};
// 获取学校启用量表
export const getObservationDimensions = () => {
  return api.get('/app/v1/observe/observations/dimensions', {});
};
// 班级观察记录列表，学生列表
export const getObservationStudentList = (data: any) => {
  return api.get('/app/v1/observe/observations/by-students', {
    params: data,
  });
};

// 观察记录详情
export const getObservationDetail = (observationId: string) => {
  return api.get(`/app/v1/observe/observations/${observationId}`);
};

// 观察记录删除
export const deleteObservation = (observationId: string) => {
  return api.delete(`/app/v1/observe/observations/${observationId}`);
};

// 观察记录创建
export const createObservation = (data: any) => {
  return api.post('/app/v1/observe/observations', data);
};

// 观察记录更新
export const updateObservation = (observationId: string, data: any) => {
  return api.put(`/app/v1/observe/observations/${observationId}`, data);
};

// 一日观察记录列表
export const getDailyObservationList = (projectId: string) => {
  return api.get('/app/v1/pbl/observations/daily-summary', {
    params: {
      projectId,
    },
  });
};

// 一日观察记录详情
export const getDailyObservationDetail = (summaryId: string) => {
  return api.get(`/app/v1/pbl/observations/daily-summary/${summaryId}`, {
    params: {
      summaryId,
    },
  });
};

// 一日观察记录更新
export const updateDailyObservation = (summaryId: string, data: any) => {
  return api.put(`/app/v1/pbl/observations/daily-summary/${summaryId}`, data);
};

// 获取观察评价指标
export const getObservationEvaluation = (data: any) => {
  return api.get('/app/v1/observe/observations/abilities', {
    params: data,
  });
};

// 获取学校启用的量表
export const getSchoolScales = () => {
  return api.get('/app/v1/observe/observations/dimensions');
};

// 学生能力评估记录新增
export const createObservationAbility = (data: any) => {
  return api.post('/app/v1/observe/student-evaluations', data);
};

// 学生能力评估记录更新
export const updateObservationAbility = (evaluationId: string, data: any) => {
  return api.put(`/app/v1/observe/student-evaluations/${evaluationId}`, data);
};

// 学生能力评估记录删除
export const deleteObservationAbility = (evaluationId: string) => {
  return api.delete(`/app/v1/observe/student-evaluations/${evaluationId}`);
};

// --------观察记录报告 start--------
export const getStudentReportList = (data: any) => {
  return api.get('/app/v1/observe/student-reports', {
    params: data,
  });
};
export const getClassReportList = (data: any) => {
  return api.get('/app/v1/observe/class-reports', {
    params: data,
  });
};
export const createStudentReport = (data: any) => {
  return api.post('/app/v1/observe/student-reports', data);
};
// 幼儿个人观察报告详情
export const getStudentReportDetail = (data: any) => {
  return api.get(`/app/v1/observe/student-reports/${data.reportId}`, {
    params: data,
  });
};
// 幼儿个人观察报告编辑
export const updateStudentReport = (data: {
  /**
   * 亮点（优势）
   */
  advantages: string;
  /**
   * 不足（劣势）
   */
  disadvantages: string;
  /**
   * 总体评价
   */
  evaluation: string;
  /**
   * 评估人
   */
  evaluator: string;
  /**
   * 家园共育合作
   */
  homeSchoolCooperation: string;
  /**
   * 个性化教学策略
   */
  personalizedTeachingStrategy: string;
  reportId: string;
  /**
   * 老师寄语
   */
  teacherNote: string;
  /**
   * 教师支持策略
   */
  teacherSupportStrategy: string;
  title: string;
}) => {
  return api.put(`/app/v1/observe/student-reports/${data.reportId}`, data);
};
// 班级观察报告详情
export const getClassReportDetail = (data: any) => {
  return api.get(`/app/v1/observe/class-reports/${data.reportId}`, {
    params: data,
  });
};
// 班级观察报告编辑
export const updateClassReport = (data: {
  classReportDomainList: {
    domain: string;
    highlights: string;
    issues: string;
    supportCases: string;
  }[];
  evaluator: string;
  title: string;
  reportId: string;
}) => {
  return api.put(`/app/v1/observe/class-reports/${data.reportId}`, data);
};
// 删除班级观察报告
export const deleteClassReport = (reportId: string) => {
  return api.delete(`/app/v1/observe/class-reports/${reportId}`);
};
// 个人报告删除
export const deleteStudentReport = (reportId: string) => {
  return api.delete(`/app/v1/observe/student-reports/${reportId}`);
};
// 创建班级观察报告
export const createClassReport = (data: any) => {
  return api.post('/app/v1/observe/class-reports', data);
};
// 观察报告标签列表
export const getReportTags = () => {
  return api.get('/app/v1/observe/tags');
};
// 观察报告标签创建
export const createReportTag = (data: any) => {
  return api.post('/app/v1/observe/tags', data);
};
// 观察报告标签详情
export const getReportTagDetail = (tagId: string) => {
  return api.get(`/app/v1/observe/tags/${tagId}`);
};
// 观察报告标签更新
export const updateReportTag = (tagId: string, data: any) => {
  return api.put(`/app/v1/observe/tags/${tagId}`, data);
};
// 观察报告标签删除
export const deleteReportTag = (tagId: string) => {
  return api.delete(`/app/v1/observe/tags/${tagId}`);
};
// 观察报告区域列表
export const getReportAreas = () => {
  return api.get('/app/v1/observe/zones');
};
// 观察报告区域创建
export const createReportArea = (data: any) => {
  return api.post('/app/v1/observe/zones', data);
};
// 观察报告区域详情
export const getReportAreaDetail = (zoneId: string) => {
  return api.get(`/app/v1/observe/zones/${zoneId}`);
};
// 观察报告区域更新
export const updateReportArea = (zoneId: string, data: any) => {
  return api.put(`/app/v1/observe/zones/${zoneId}`, data);
};
// 观察报告区域删除
export const deleteReportArea = (zoneId: string) => {
  return api.delete(`/app/v1/observe/zones/${zoneId}`);
};
// 设置学校启用的量表
export const setObservationDimensions = (data: any) => {
  return api.put('/app/v1/observe/observations/dimensions', data);
};
// --------观察记录报告 end--------

// 语音转文字
export const transcribeAudio = (audio_url: string) => {
  return api.post('/v1/speech/speechToText', {
    audio_url,
  });
};

// --------阶段性总结 (Periodic Summary) start--------
// 获取阶段性总结列表
export const getPeriodicSummaryList = (data: any) => {
  return api.get('/app/v1/pbl/observations/stage-summary', { params: data });
};

// 创建阶段性总结
export const createPeriodicSummary = (data: any) => {
  return api.post('/app/v1/pbl/observations/stage-summary', data);
};

// 获取阶段性总结详情
export const getPeriodicSummaryDetail = (summaryId: string) => {
  return api.get(`/app/v1/pbl/observations/stage-summary/${summaryId}`);
};

// 删除阶段性总结
export const deletePeriodicSummary = (summaryId: string) => {
  return api.delete(`/app/v1/pbl/observations/stage-summary/${summaryId}`);
};

// 更新阶段性总结
export const updatePeriodicSummary = (summaryId: string, data: any) => {
  return api.put(`/app/v1/pbl/observations/stage-summary/${summaryId}`, data);
};

// --------阶段性总结 (Periodic Summary) end--------
