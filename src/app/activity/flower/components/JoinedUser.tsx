'use client';

import Image from 'next/image';
import React, { memo, useEffect, useState } from 'react';
import { joinUsersList } from '@/api/flowerActivity';
import { PiDotsThreeBold } from '@/components/Icons';

function JoinedUsers() {
  const [joinUsers, setJoinUsers] = useState([]); // 参与用户列表
  const [joinUsersCount, setJoinUsersCount] = useState(0); // 参与用户数量

  useEffect(() => {
    getJoinUsersList();
  }, []);

  const getJoinUsersList = async () => {
    joinUsersList().then((res) => {
      console.log('获取到参与用户列表', res);
      // 取前3个用户
      const users = Array.isArray(res.data) ? res.data.slice(0, 3) : [];
      setJoinUsers(users);
      setJoinUsersCount(res.count || 0);
    });
  };

  return (
    <div className="mx-auto mb-3 flex h-[64px] flex-row items-center justify-center">
      <div className="-space-x-2 flex rtl:space-x-reverse">
        {joinUsers.map((user, index) => {
          return (
            <Image
              alt=""
              className="h-[64px] w-[64px] rounded-full border-2 border-white"
              height={100}
              key={index}
              src={
                user.avatar ||
                'https://mediatx.ancda.com/app_avatar_teacher_man.png'
              }
              width={100}
            />
          );
        })}

        <div className="flex h-[64px] w-[64px] items-center justify-center rounded-full border-2 border-white bg-gray-200">
          <PiDotsThreeBold color="#999" fontSize={32} />
        </div>
      </div>
      <span className="ml-2 text-base text-white">
        {joinUsersCount}人已参与
      </span>
    </div>
  );
}

export default memo(JoinedUsers);
