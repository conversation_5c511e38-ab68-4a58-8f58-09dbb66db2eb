'use client';

import { <PERSON><PERSON>, <PERSON>alog, Mask, Popup, SpinLoading, Toast } from 'antd-mobile';
import clsx from 'clsx';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import React, { useEffect, useRef, useState } from 'react';
import {
  activityInfo,
  finishTask,
  subscribeNotification,
  totalFlowers,
} from '@/api/flowerActivity';
import awardTitle from '@/assets/images/flower/award-title.png';
import awardTitle2 from '@/assets/images/flower/award-title2.png';
import bg from '@/assets/images/flower/bg.jpg';
import boxLarge from '@/assets/images/flower/box-large.png';
import boxSmall from '@/assets/images/flower/box-small.png';
import envelope from '@/assets/images/flower/envelope.png';
import envelopeBg from '@/assets/images/flower/envelope-bg.png';
import flower from '@/assets/images/flower/flower.png';
import notice from '@/assets/images/flower/notice.png';
import title1 from '@/assets/images/flower/title1.png';
import title2 from '@/assets/images/flower/title2.png';
import { PiXCircleFill } from '@/components/Icons';
import {
  getMessage,
  getNotificationsEnabled,
  openNotificationsSetting,
} from '@/utils';
import CountdownTimer from './components/CountdownTimer';
import JoinedUsers from './components/JoinedUser';

// import Script from 'next/script';

const buttonStyle = {
  '--background-color': '#FCF9E7',
  '--border-color': '#FFA781',
  '--border-width': '2px',
  '--text-color': '#D06B31',
};

const buttonStyle2 = {
  background: 'linear-gradient(180deg, #FFFAE3 0%, #FFB141 100%)',
  '--border-color': '#FFC86F',
  '--border-width': '2.5px',
  '--text-color': '#874911',
};

if (typeof document !== 'undefined') {
  document.title = '瓜分小红花';
}

function secondsUntilTomorrowMidnight() {
  const now = new Date();
  const tomorrow = new Date(now);
  tomorrow.setDate(tomorrow.getDate() + 1);
  tomorrow.setHours(0, 0, 0, 0);

  const diffInSeconds = Math.floor((tomorrow - now) / 1000);
  return diffInSeconds;
}
// 报名截止时间
const signEndTime = secondsUntilTomorrowMidnight();

const MINE_FLOWER = 100;
let checkInterval: ReturnType<typeof setInterval>;

export default function Flower() {
  const router = useRouter();
  const [isFirst, setIsFirst] = useState(false); // 是否是第一次参与
  const [isJoin, setIsJoin] = useState(false); // 今天是否参与
  const [isEnabledNotification, setIsEnabledNotification] = useState(false); // 是否开启通知
  const [isSubscribe, setIsSubscribe] = useState(false); // 是否订阅
  const [isJoinPopupVisible, setIsJoinPopupVisible] = useState(false); // 参与底部弹窗
  const [myFlower, setMyFlower] = useState(0); // 我的小红花
  const [awardPopupVisible, setAwardPopupVisible] = useState(false); // 获奖弹窗
  const [winFlowerPopupVisible, setWinFlowerPopupVisible] = useState(false); // 获取到小红花弹窗
  const [winFlowerCount, setWinFlowerCount] = useState(0); // 获取到小红花数量
  const [enabledNotificationPopupVisible, setEnabledNotificationPopupVisible] =
    useState(false); // 设置通知弹窗
  const [isShowBottomNotification, setIsShowBottomNotification] =
    useState(false); // 是否显示底部领奖弹窗

  const [awardTime, setAwardTime] = useState(0); // 开奖时间

  const hasGotoNotification = useRef(false); // 是否已经去设置通知,开始监听通知开启状态

  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    if (!isEnabledNotification) {
      getNotificationsEnabled();
    }
  }, [isEnabledNotification]);

  useEffect(() => {
    getMessage(onMessage);
    getActivityInfo();
    getMyFlower();
  }, []);

  const getMyFlower = async () => {
    totalFlowers().then((res) => {
      console.log('获取到我的小红花', res);
      setMyFlower(res.totalPointUser);
    });
  };

  const getActivityInfo = async () => {
    activityInfo()
      .then((res: any) => {
        console.log('获取到活动信息', res);
        setIsFirst(res.todayIsBase === 1);
        setIsJoin(res.isSignToday === 1);
        const isJoinYesterday =
          res.isSignYesterday === 1 &&
          res.awardYesterday === 0 &&
          res.openPrizeGapTime <= 0;
        setIsSubscribe(res.isOpenNotify === 1);
        if (isJoinYesterday) {
          setAwardPopupVisible(true);
        }
        setAwardTime(res.openPrizeGapTime || 0);
      })
      .finally(() => {
        setIsLoaded(true);
      });
  };

  // 获取到RN的通知
  const onMessage = (event: any) => {
    try {
      const data = JSON.parse(event.data);
      console.log('🚀 ~ data:', data);
      if ('areNotificationsEnabled' in data) {
        setIsEnabledNotification(data.areNotificationsEnabled);
        if (data.areNotificationsEnabled) {
          clearInterval(checkInterval);
          if (hasGotoNotification.current) {
            confirmJoinTask('open_notify');
            hasGotoNotification.current = false;
            confirmJoinTask('point_activity_sign');
          }
        }
      }
      console.log('获取到RN的通知 onMessage ', JSON.stringify(data));
    } catch (error) {}
  };

  // 订阅领奖提醒
  const subscribe = () => {
    Dialog.confirm({
      content: '订阅领奖提醒',
      onConfirm: async () => {
        subscribeNotification({ isOpenNotify: 1 }).then(() => {
          Toast.show({
            icon: 'success',
            content: '订阅成功',
          });
          getActivityInfo();
        });
      },
    });
  };

  const handleJoin = () => {
    if (isEnabledNotification) {
      if (isFirst) {
        confirmJoinTask('point_activity_sign');
      } else {
        setIsJoinPopupVisible(true);
      }
    } else {
      setEnabledNotificationPopupVisible(true);
    }
  };

  const handleOpenNotification = () => {
    setEnabledNotificationPopupVisible(false);
    hasGotoNotification.current = true;
    openNotificationsSetting();
    checkInterval = setInterval(() => {
      getNotificationsEnabled();
    }, 2000);
  };

  const handleJoinNext = () => {
    setWinFlowerPopupVisible(false);
    setIsJoinPopupVisible(true);
  };

  const confirmJoin = () => {
    if (!isFirst && myFlower < MINE_FLOWER) {
      Toast.show({
        icon: 'fail',
        content: '小红花数量不足',
      });
      return;
    }
    confirmJoinTask('point_activity_sign');
  };

  const confirmJoinTask = (taskId: string) => {
    finishTask({ taskId }).then((res: any) => {
      console.log('完成任务', res);
      if (res.score > 0) {
        setWinFlowerCount(res.score);
        if (taskId === 'open_notify') {
          // 开启通知
          setWinFlowerPopupVisible(true);
        } else if (taskId === 'point_activity_add') {
          // 瓜分小红花
          setWinFlowerPopupVisible(true);
          getMyFlower();
        }
      }
      if (taskId === 'point_activity_sign') {
        // 报名
        setIsJoin(true);
        setIsJoinPopupVisible(false);
        if (!isFirst) {
          Toast.show({
            icon: 'success',
            content: '参与成功',
          });
        }
        setAwardTime(0);
        getActivityInfo();
      }
    });
  };

  const handleWinFlower = () => {
    confirmJoinTask('point_activity_add');
    setAwardPopupVisible(false);
    setWinFlowerPopupVisible(true);
  };

  if (!isLoaded) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <SpinLoading />
      </div>
    );
  }

  return (
    <div className="relative bg-[#E82A3F]">
      <Image
        alt=""
        className="absolute top-0 right-0 z-0 w-screen object-cover"
        height={0}
        sizes="100vw"
        src={bg}
        width={0}
      />
      <div className="relative z-10 flex min-h-screen flex-col">
        <div className="flex flex-row justify-end space-x-2 pt-2 pr-2">
          {/* <Button shape="rounded" style={buttonStyle}>
            我参与的
          </Button>
          <Button shape="rounded" style={buttonStyle}>
            活动规则
          </Button> */}
          <Button
            onClick={() => {
              router.push('/activity/flower/record');
            }}
            shape="rounded"
            size="small"
            style={buttonStyle}
          >
            我参与的
          </Button>
          <Button
            onClick={() => {
              router.push('/activity/flower/rules');
            }}
            shape="rounded"
            size="small"
            style={buttonStyle}
          >
            活动规则
          </Button>
        </div>
        {isJoin ? (
          <div className="absolute top-[386px] right-0 left-0 mx-auto h-[832px] w-[672px]">
            <Image
              alt=""
              className="absolute right-0 left-0 mx-auto h-[832px] w-[672px]"
              height={0}
              src={boxLarge}
              width={0}
            />
            <Image
              alt=""
              className="absolute top-[140px] right-0 left-0 mx-auto h-[291px] w-[438px]"
              height={0}
              src={envelope}
              width={0}
            />
            <div className="absolute top-[74px] right-0 left-0 mx-auto w-[460px] rounded-full bg-[#FF6670] py-1 text-center text-white">
              您已获得小红花红包，等待开奖
            </div>
            {awardTime > 0 ? (
              <div className="absolute top-[340px] right-0 left-0 mx-auto text-center text-[#D06B31]">
                距离开奖还有{' '}
                <CountdownTimer
                  onComplete={() => {
                    confirmJoinTask('point_activity_add');
                  }}
                  seconds={awardTime}
                />
              </div>
            ) : null}

            <div className="absolute top-[366px] right-0 left-0 mx-auto text-center text-[#F74D4F]">
              <span className="text-xl">瓜分</span>
              <span className="text-5xl">19999</span>
              <span className="text-xl">朵</span>
            </div>
            <div className="absolute right-0 bottom-[90px] left-0 mx-auto w-[540px] text-center">
              <JoinedUsers />
              {!isSubscribe && (
                <div
                  className="text-base text-white"
                  onClick={() => {
                    subscribe();
                  }}
                >
                  明天10点开奖，
                  <span className="underline underline-offset-8">
                    去订阅领奖提醒
                  </span>
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className="absolute top-[386px] right-0 left-0 mx-auto h-[733px] w-[672px]">
            <Image
              alt=""
              className="absolute right-0 left-0 mx-auto h-[733px] w-[672px]"
              height={0}
              src={boxSmall}
              width={0}
            />
            <div className="absolute top-[120px] right-0 left-0 mx-auto text-center text-[#D06B31] text-base">
              --- <CountdownTimer onComplete={() => {}} seconds={signEndTime} />{' '}
              后截止报名 ---
            </div>
            <div className="absolute top-[200px] right-0 left-0 mx-auto text-center text-[#F74D4F]">
              <span className="text-xl">瓜分</span>
              <span className="text-5xl">19999</span>
              <span className="text-xl">朵</span>
            </div>
            <div className="absolute right-0 bottom-[60px] left-0 mx-auto w-[520px] text-center">
              <JoinedUsers />
              <div className="flex justify-center">
                <Button
                  className="mx-auto animate-pulse"
                  onClick={() => {
                    handleJoin();
                  }}
                  shape="rounded"
                  style={{
                    ...buttonStyle2,
                    width: '240px',
                  }}
                >
                  {isFirst ? '立即参与' : `${MINE_FLOWER}小红花参与`}
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
      <Popup
        bodyStyle={{
          borderTopLeftRadius: '16px',
          borderTopRightRadius: '16px',
        }}
        closeOnMaskClick
        onClose={() => {
          setIsJoinPopupVisible(false);
        }}
        showCloseButton
        visible={isJoinPopupVisible}
      >
        <div className="flex flex-col items-center justify-center p-6 pt-2">
          <div className="text-base">用小红花参与瓜分</div>
          <div className="flex flex-col items-center justify-center p-4 text-stone-500">
            <div>
              需消耗：
              <span className="text-2xl text-red-500">{MINE_FLOWER}朵</span>
            </div>
            <div>我的小红花：{myFlower}</div>
            {myFlower < MINE_FLOWER && (
              <div className="mt-2 text-red-500">小红花数量不足</div>
            )}
          </div>
          <div className="flex justify-center">
            <Button
              block
              disabled={myFlower < MINE_FLOWER}
              onClick={() => {
                confirmJoin();
              }}
              shape="rounded"
              style={{
                width: '240px',
                '--background-color': '#FFF4C4',
                '--border-width': '1',
                '--text-color': '#874911',
              }}
            >
              {myFlower >= MINE_FLOWER ? '立即参与' : '去做任务赚取小红花吧'}
            </Button>
          </div>
        </div>
      </Popup>
      <Mask
        onMaskClick={() => {}}
        opacity={0.7}
        visible={enabledNotificationPopupVisible}
      >
        <div className="absolute top-[20%] right-0 bottom-0 left-0">
          <Image
            alt=""
            className="mx-auto mb-4 h-[156px] w-[473px]"
            height={0}
            src={title2}
            width={0}
          />
          <div className="relative mx-auto h-[415px] w-[319px]">
            <Image
              alt=""
              className="mx-auto h-[415px] w-[319px]"
              height={0}
              src={envelopeBg}
              width={0}
            />
            <Image
              alt=""
              className=" absolute top-[60px] right-0 left-0 mx-auto h-[160px] w-[160px]"
              height={0}
              src={flower}
              width={0}
            />
            <div className="absolute right-0 bottom-[60px] left-0 mx-auto w-[240px] text-center">
              <Button
                block
                onClick={() => handleOpenNotification()}
                shape="rounded"
                size="small"
                style={buttonStyle2}
              >
                去开启通知
              </Button>
            </div>
          </div>
        </div>
      </Mask>
      <Mask
        onMaskClick={() => {
          setAwardPopupVisible(false);
          setIsShowBottomNotification(true);
        }}
        opacity={0.7}
        visible={awardPopupVisible}
      >
        <div className="absolute top-[20%] right-0 bottom-0 left-0">
          <Image
            alt=""
            className="mx-auto mb-4 h-[164px] w-[473px]"
            height={0}
            src={title1}
            width={0}
          />
          <div className="relative mx-auto h-[415px] w-[319px]">
            <Image
              alt=""
              className="mx-auto h-[415px] w-[319px]"
              height={0}
              src={envelopeBg}
              width={0}
            />
            <Image
              alt=""
              className=" absolute top-[60px] right-0 left-0 mx-auto h-[160px] w-[160px]"
              height={0}
              src={flower}
              width={0}
            />
            <div className="absolute right-0 bottom-[60px] left-0 mx-auto w-[200px] text-center">
              <Button
                block
                onClick={() => handleWinFlower()}
                shape="rounded"
                size="small"
                style={buttonStyle2}
              >
                开心收下
              </Button>
            </div>
          </div>
          <div
            className="mt-4 flex items-center justify-center"
            onClick={() => {
              setAwardPopupVisible(false);
              setIsShowBottomNotification(true);
            }}
          >
            <PiXCircleFill color="#FFFFFF" fontSize={36} />
          </div>
        </div>
      </Mask>
      <Mask
        onMaskClick={() => {}}
        opacity={0.7}
        visible={winFlowerPopupVisible}
      >
        <div className="absolute top-[20%] right-0 bottom-0 left-0">
          <Image
            alt=""
            className={clsx('mx-auto mb-4 h-[70px]', {
              'w-[272px]': !isFirst,
              'w-[488px]': isFirst,
            })}
            height={0}
            src={isFirst ? awardTitle2 : awardTitle}
            width={0}
          />
          <div className="relative mx-auto h-[482px] w-[423px]">
            <Image
              alt=""
              className="mx-auto h-[482åpx] w-[423px]"
              height={0}
              src={boxSmall}
              width={0}
            />
            <div className="absolute top-[100px] right-0 left-0 mx-auto w-[300px] text-center">
              <div className="mb-2 text-lg">
                {isFirst ? '恭喜获得' : '恭喜分到'}
              </div>
              <div className="text-[#F74D4F] text-xl">
                {winFlowerCount} 朵小红花
              </div>
            </div>
            <div className="absolute right-0 bottom-[80px] left-0 mx-auto w-[240px] text-center">
              <Button
                block
                onClick={() => {
                  if (isFirst) {
                    setWinFlowerPopupVisible(false);
                  } else {
                    handleJoinNext();
                  }
                }}
                shape="rounded"
                style={buttonStyle2}
              >
                {isFirst ? '我知道了' : '参与下一期'}
              </Button>
            </div>
          </div>
          {!isFirst && (
            <div
              className="mt-4 flex items-center justify-center"
              onClick={() => {
                setWinFlowerPopupVisible(false);
              }}
            >
              <PiXCircleFill color="#FFFFFF" fontSize={36} />
            </div>
          )}
        </div>
      </Mask>
      {isShowBottomNotification && (
        <div
          className="absolute right-0 bottom-[40px] left-0 z-10 mx-auto h-[128px] w-[616px]"
          onClick={() => {
            confirmJoinTask('point_activity_add');
          }}
        >
          <Image
            alt=""
            className="absolute right-0 left-0 mx-auto h-[128px] w-[616px]"
            height={0}
            src={notice}
            width={0}
          />
          <div
            className="absolute top-0 right-0 z-20 flex h-[128px] w-[100px] items-center justify-center"
            onClick={(event) => {
              console.log('点击了关闭');
              event.stopPropagation();
              event.preventDefault();
              setIsShowBottomNotification(false);
            }}
          >
            <PiXCircleFill color="#aaa" fontSize={32} />
          </div>
        </div>
      )}
      {/* <Script
        src="//cdn.jsdelivr.net/npm/eruda"
        onLoad={() => {
          const { eruda } = window as any;
          eruda.init();
          eruda.position({ x: 20, y: 240 });
        }}
      /> */}
    </div>
  );
}
