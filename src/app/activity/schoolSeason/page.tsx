'use client';
import Image from 'next/image';
import { useEffect, useState } from 'react';
import { hinaTrack, navigationToNativePage } from '@/utils';

export default function SchoolSeason() {
  const [images] = useState([
    'https://unicorn-media.ancda.com/production/app/activity/schoolSeason/schoolSeason1.jpg',
    'https://unicorn-media.ancda.com/production/app/activity/schoolSeason/schoolSeason2.jpg',
    'https://unicorn-media.ancda.com/production/app/activity/schoolSeason/schoolSeason3.jpg',
    'https://unicorn-media.ancda.com/production/app/activity/schoolSeason/schoolSeason4.jpg',
    'https://unicorn-media.ancda.com/production/app/activity/schoolSeason/schoolSeason5.jpg',
    'https://unicorn-media.ancda.com/production/app/activity/schoolSeason/schoolSeason6.jpg',
    'https://unicorn-media.ancda.com/production/app/activity/schoolSeason/schoolSeason7.jpg',
    'https://unicorn-media.ancda.com/production/app/activity/schoolSeason/schoolSeason8.jpg',
    'https://unicorn-media.ancda.com/production/app/activity/schoolSeason/schoolSeason9.jpg',
    'https://unicorn-media.ancda.com/production/app/activity/schoolSeason/schoolSeason10.jpg',
  ]);
  const [typeText] = useState<{ [key: number]: string }>({
    6: '加赠1个月会员时长',
    8: '1元7天体验卡',
    10: '开学教育大礼包',
  });
  // 设置页面标题
  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = '开学季 - 新学期新征程';
    }
    hinaTrack('begins_sellVIP_Pageview');
  }, []);
  useEffect(() => {
    const preventZoom = (e: TouchEvent) => {
      if (e.touches.length > 1) {
        e.preventDefault();
      }
    };

    document.addEventListener('touchmove', preventZoom, { passive: false });

    return () => {
      document.removeEventListener('touchmove', preventZoom);
    };
  }, []);

  // 处理"去开通"按钮点击事件
  const handleGoToMember = (index: number) => {
    hinaTrack('begins_sellVIP_open_click', {
      promoteVIP_type: typeText[index],
    });
    navigationToNativePage('rn://MemberStack?initialRoute=ParentsIndexScreen');
  };

  // 判断是否为需要添加点击事件的图片（第7、9、11张图片）
  const shouldAddClickable = (index: number) => {
    return [6, 8, 10].includes(index); // 对应第7、9、11张图片
  };

  return (
    <div className="w-full">
      {images.map((imageUrl, index) => (
        <div className="relative w-full" key={`school-season-${index + 1}`}>
          <Image
            alt={`schoolSeason${index + 1}`}
            className={'block h-auto w-full'}
            height={1334}
            loading={index < 2 ? 'eager' : 'lazy'}
            priority={index < 2}
            src={imageUrl}
            width={750}
          />
          {/* 为第7、9、11张图片添加"去开通"按钮热区 */}
          {shouldAddClickable(index) && (
            <button
              aria-label="去开通会员"
              className="absolute right-[8%] bottom-[0%] h-[50%] w-[25%] cursor-pointer bg-transparent"
              onClick={() => handleGoToMember(index)}
              type="button"
            />
          )}
        </div>
      ))}
    </div>
  );
}
