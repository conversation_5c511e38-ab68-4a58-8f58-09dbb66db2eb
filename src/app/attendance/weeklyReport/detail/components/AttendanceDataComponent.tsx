import { useEffect, useRef, useState } from 'react';

interface AttendanceDataComponentProps {
  reportDetail: any;
  processedData: any[];
}

export default function AttendanceDataComponent({
  reportDetail,
  processedData,
}: AttendanceDataComponentProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [containerHeight, setContainerHeight] = useState(260);

  useEffect(() => {
    if (containerRef.current) {
      setContainerHeight(containerRef.current.offsetHeight);
    }
  }, [reportDetail]);

  const timeToSeconds = (time: string) => {
    if (!time) {
      return 0;
    }
    const [hours, minutes, seconds] = time.split(':').map(Number);
    return (hours ?? 0) * 3600 + (minutes ?? 0) * 60 + (seconds ?? 0);
  };

  return (
    <div className="mb-6 rounded-2xl bg-white/50 p-4 font-sans shadow-lg">
      <h2 className="mb-4 font-semibold text-base text-gray-800">
        宝贝本周考勤数据
      </h2>

      <div className="flex">
        {/* 左侧日期/天气列 - 固定宽度 */}
        <div className="w-1/4 flex-shrink-0" />

        {/* 右侧时间轴列 - 占满剩余空间 */}
        <div className="relative w-3/4 flex-grow">
          {/* 表头 - 绝对定位以对齐虚线 */}
          <div className="absolute top-0 h-12 w-full">
            <div className="-translate-x-1/2 -translate-y-1/2 absolute top-1/2 left-[20%] text-center">
              <p className="text-gray-500 text-xs">应上学时间</p>
              <p className="font-bold text-sm">
                {reportDetail?.timeSlot?.[0]?.slice(0, 5)}
              </p>
            </div>
            <div className="-translate-x-1/2 -translate-y-1/2 absolute top-1/2 left-[70%] text-center">
              <p className="text-gray-500 text-xs">应放学时间</p>
              <p className="font-bold text-sm">
                {reportDetail?.timeSlot?.[1]?.slice(0, 5)}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* 记录和虚线的容器 */}
      <div className="relative" ref={containerRef}>
        {/* 垂直虚线 - 与表头对齐 */}
        <div className="absolute top-0 bottom-0 left-[calc(23%)] w-[1px] bg-gray-500" />
        <div className="absolute top-0 bottom-0 left-[calc(20%+25%*0.75)] w-[1px]">
          <svg
            className="styles__StyledLineSvg-sc-8i3v39-0 geXqWT filter-none"
            height={containerHeight}
            xmlns="http://www.w3.org/2000/svg"
          >
            <title>Vertical line</title>
            <path
              d={`M 0.5 0 L 0.5 ${containerHeight}`}
              stroke="rgba(0,0,0,0.3)"
              strokeDasharray="3 2"
              strokeWidth="1"
            />
            <path
              className="line-svg-patch"
              d={`M 0.5 0 L 0.5 ${containerHeight}`}
              stroke="transparent"
              strokeWidth="10"
            />
          </svg>
        </div>
        <div className="absolute top-0 bottom-0 left-[calc(20%+75%*0.75)] w-[1px]">
          <svg
            className="styles__StyledLineSvg-sc-8i3v39-0 geXqWT filter-none"
            height={containerHeight}
            xmlns="http://www.w3.org/2000/svg"
          >
            <title>Vertical line</title>
            <path
              d={`M 0.5 0 L 0.5 ${containerHeight}`}
              stroke="rgba(0,0,0,0.3)"
              strokeDasharray="3 2"
              strokeWidth="1"
            />
            <path
              className="line-svg-patch"
              d={`M 0.5 0 L 0.5 ${containerHeight}`}
              stroke="transparent"
              strokeWidth="10"
            />
          </svg>
        </div>

        {/* 记录列表 */}
        <div className="mt-12 space-y-2">
          {processedData.map((record, _index) => {
            // 可视化时间轴范围
            const timelineStartSeconds = timeToSeconds(
              reportDetail?.timeSlot?.[0] || '8:00:00'
            );
            const timelineEndSeconds = timeToSeconds(
              reportDetail?.timeSlot?.[1] || '17:00:00'
            );
            const timelineDuration = timelineEndSeconds - timelineStartSeconds;

            return (
              <div className="flex h-10 items-center" key={record.weekday}>
                {/* 左侧日期/天气 */}
                <div className="flex w-1/4 items-center justify-start space-x-2">
                  <span className="whitespace-nowrap font-medium text-gray-700 text-sm">
                    {record.weekday}
                  </span>
                  <span className="text-sm">{record.weather}</span>
                </div>

                {/* 右侧时间轴 - 修改为与表头一致的宽度 */}
                <div className="relative left-[13.5%] flex w-[calc(51%*0.75)] items-center">
                  {(() => {
                    if (
                      record.isRestDay ||
                      record.isLeave === 2 ||
                      record.isAbsence
                    ) {
                      let displayText: string;
                      if (record.isRestDay) {
                        displayText = '休息';
                      } else if (record.isLeave === 2) {
                        displayText = '请假';
                      } else {
                        displayText = '缺勤';
                      }

                      return (
                        <div className="w-full text-center text-gray-600 text-sm">
                          {displayText}
                        </div>
                      );
                    }
                    const arrivalSeconds = timeToSeconds(record.arrivalTime);
                    const leaveSeconds = timeToSeconds(record.leaveTime);
                    const startPercent =
                      ((arrivalSeconds - timelineStartSeconds) /
                        timelineDuration) *
                      100;
                    const widthPercent =
                      ((leaveSeconds - arrivalSeconds) / timelineDuration) *
                      100;

                    let arrivalTextColor: string;
                    if (record.isLate || record.isMissingArrival) {
                      arrivalTextColor = 'text-red-500';
                    } else {
                      arrivalTextColor = 'text-gray-800';
                    }

                    let arrivalDisplayText: string;
                    if (record.isMissingArrival) {
                      if (record.isLeave === 1) {
                        arrivalDisplayText = '请假';
                      } else {
                        arrivalDisplayText = '缺卡';
                      }
                    } else {
                      arrivalDisplayText = record.arrivalTime;
                    }

                    let leaveTextColor: string;
                    if (record.isEarlyLeave || record.isMissingLeave) {
                      leaveTextColor = 'text-red-500';
                    } else {
                      leaveTextColor = 'text-gray-800';
                    }

                    let leaveDisplayText: string;
                    if (record.isMissingLeave) {
                      if (record.isLeave === 1) {
                        leaveDisplayText = '请假';
                      } else {
                        leaveDisplayText = '缺卡';
                      }
                    } else {
                      leaveDisplayText = record.leaveTime;
                    }

                    return (
                      <>
                        {/* 上学时间 */}
                        <span
                          className={`absolute z-10 whitespace-nowrap font-medium text-xs ${arrivalTextColor}`}
                          style={{
                            left: 0,
                            top: '50%',
                            transform: 'translate(-120%, -50%)',
                          }}
                        >
                          {arrivalDisplayText}
                        </span>
                        {/* 在校时间条 */}
                        <div
                          className="z-4 h-4 rounded-full"
                          style={{
                            position: 'absolute',
                            left: `${startPercent}%`,
                            width: `${widthPercent}%`,
                            backgroundColor: '#A5D8FF',
                          }}
                        />
                        {/* 放学时间 */}
                        <span
                          className={`absolute z-10 whitespace-nowrap font-medium text-xs ${leaveTextColor}`}
                          style={{
                            left: '100%',
                            top: '50%',
                            transform: 'translate(20%, -50%)',
                          }}
                        >
                          {leaveDisplayText}
                        </span>
                      </>
                    );
                  })()}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* 考勤小结 */}
      {reportDetail?.summary && (
        <div className="mt-6 rounded-lg bg-gray-100/80 p-3">
          <p className="text-gray-600 text-xs leading-relaxed">
            <span className="font-semibold text-[12] text-gray-800">
              考勤小结：
            </span>
          </p>
          <p className="text-gray-600 text-sm leading-relaxed">
            {reportDetail?.summary || '暂无考勤数据'}
          </p>
        </div>
      )}
    </div>
  );
}
