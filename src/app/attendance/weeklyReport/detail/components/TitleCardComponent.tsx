interface TitleCardComponentProps {
  reportDetail: any;
  getCardBackground: (keyWord: string) => string;
}

export default function TitleCardComponent({
  reportDetail,
  getCardBackground,
}: TitleCardComponentProps) {
  return (
    <div
      className="relative z-10 mb-6 overflow-hidden rounded-xl"
      style={{
        backgroundImage: `url('${getCardBackground(reportDetail?.title || '')}')`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        opacity: 0.7,
      }}
    >
      {/* 内容区域 */}
      <div className="relative z-10 p-2">
        {/* 主题标签 */}
        <div className="mb-3">
          <span className="font-medium text-blue-700 text-xs">关键词</span>
        </div>

        {/* 标题 */}
        <h3 className="mb-2 font-bold text-gray-800 text-xl">
          {reportDetail?.title || '考勤周报'}
        </h3>
        <p className="mb-4 pr-28 text-gray-700 text-sm leading-relaxed">
          {reportDetail?.daySentence || ''}
        </p>
      </div>
    </div>
  );
}
