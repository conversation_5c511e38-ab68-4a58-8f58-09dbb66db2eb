interface HeaderComponentProps {
  onGoBack: () => void;
  getCurrentWeekDisplay: (startDate: string, endDate: string) => string;
  reportDetail: any;
  handlePreviousWeek: () => void;
  handleNextWeek: () => void;
  isLoading: boolean;
  isFailed: boolean;
}

export default function HeaderComponent({
  onGoBack,
  getCurrentWeekDisplay,
  reportDetail,
  handlePreviousWeek,
  handleNextWeek,
  isLoading,
  isFailed,
}: HeaderComponentProps) {
  return (
    <header className="fixed top-0 right-0 left-0 z-20 w-full backdrop-blur-sm">
      {/* 导航栏 - 固定在顶部 */}
      <div className="relative right-0 left-0 z-40 mt-10 flex items-center justify-between bg-transparent px-4">
        <button className="mt-3 p-2" onClick={onGoBack} type="button">
          <svg
            className="h-6 w-6 text-gray-700"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <title>返回</title>
            <path
              d="M15 19l-7-7 7-7"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
            />
          </svg>
        </button>

        {/* 周报切换控件 */}
        {!(isLoading || isFailed) && (
          <div className="mt-3 flex items-center space-x-2 rounded-full bg-white/60 px-1 py-1 shadow-md backdrop-blur-sm">
            <button
              className="flex items-center justify-center rounded-full p-2 transition-colors"
              disabled={true}
              onClick={handlePreviousWeek}
              type="button"
            />

            <div className="min-w-[60px] text-center">
              <div className="font-medium text-gray-700 text-xs">
                {getCurrentWeekDisplay(
                  reportDetail?.startDate ?? '',
                  reportDetail?.endDate ?? ''
                )}
              </div>
            </div>

            <button
              className="flex items-center justify-center rounded-full p-2 transition-colors disabled:cursor-not-allowed disabled:opacity-50"
              disabled={true}
              onClick={handleNextWeek}
              type="button"
            />
          </div>
        )}

        {/* 占位元素保持布局平衡 */}
        <div className="w-10" />
      </div>
    </header>
  );
}
