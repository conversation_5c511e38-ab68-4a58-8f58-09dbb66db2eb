/** biome-ignore-all lint/nursery/noNoninteractiveElementInteractions: <explanation> */
/** biome-ignore-all lint/nursery/noAwaitInLoop: <explanation> */

import { Toast } from 'antd-mobile';
import { t } from 'framer-motion/dist/types.d-B_QPEvFK';
import { X } from 'lucide-react';
import { useState } from 'react';

interface DocumentPreviewProps {
  type: 'document' | 'txt' | 'pdf' | 'zip';
  src: string;
  title: string;
  onClose: () => void;
}

export function DocumentPreview({
  type,
  src,
  title,
  onClose,
}: DocumentPreviewProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [isFail, setIsFail] = useState(false);

  // 构建微软Office Online Viewer的URL
  const getOfficeViewerUrl = (fileUrl: string) => {
    if (type === 'zip' || type === 'txt') {
      return fileUrl;
    }
    if (type === 'pdf') {
      return `https://mozilla.github.io/pdf.js/web/viewer.html?file=${encodeURIComponent(fileUrl)}`;
    }
    // 微软Office Online Viewer - 设置宽度为页面宽度
    return `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(fileUrl)}&wdAr=0.46&wdEmbedCode=0`;
  };

  const handleIframeLoad = () => {
    setIsLoading(false);
  };

  const handleIframeError = () => {
    setIsLoading(false);
    setIsFail(true);
    Toast.show({ content: '文档加载失败，请检查文件链接是否有效' });
  };

  return (
    <div className="fixed inset-0 z-50 flex flex-col bg-gray-900">
      {/* 工具栏 */}
      <div className="flex-shrink-0 border-gray-200 border-b bg-white px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <button
              className="rounded-lg p-2 transition-colors hover:bg-gray-100"
              onClick={onClose}
              type="button"
            >
              <X className="h-5 w-5 text-gray-600" />
            </button>
            <div className="relative flex-1 cursor-pointer pr-12 text-center">
              {title}
            </div>
          </div>
        </div>
      </div>
      {/* 文档内容区域 */}
      <div className="relative h-full flex-1 overflow-auto bg-gray-100">
        {/* 加载状态 */}
        {isLoading && (
          <div className="absolute inset-0 z-10 flex items-center justify-center bg-white">
            <div className="text-center">
              <div className="mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-4 border-orange-500 border-t-transparent" />
              <p className="text-gray-600">正在加载文档...</p>
            </div>
          </div>
        )}

        {/* /* 其他文档类型使用 iframe */}
        <iframe
          className="h-full w-full p-2"
          onError={handleIframeError}
          onLoad={handleIframeLoad}
          sandbox="allow-scripts allow-same-origin allow-popups allow-forms allow-downloads"
          src={getOfficeViewerUrl(src)}
          title={title}
        />

        {/* 错误提示 */}
        {!isLoading && isFail && (
          <div className="absolute right-4 bottom-4 left-4 z-20">
            <div className="rounded-lg border border-yellow-200 bg-yellow-50 p-3">
              <p className="text-sm text-yellow-800">
                <strong>提示：</strong>如果文档无法正常显示，请确保：
              </p>
              <ul className="mt-1 ml-4 list-disc text-xs text-yellow-700">
                <li>文件链接可以公开访问</li>
                <li>
                  文件格式为：
                  {type === 'pdf'
                    ? 'PDF'
                    : 'Word、Excel、PowerPoint等Office文档'}
                </li>
                {type === 'pdf' && <li>PDF文件没有密码保护</li>}
              </ul>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
