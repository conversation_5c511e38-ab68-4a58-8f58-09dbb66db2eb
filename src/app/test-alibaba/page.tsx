import AlibabaChat from '@/components/AlibabaChat';

export default function TestAlibabaPage() {
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="mx-auto max-w-4xl px-4">
        <h1 className="mb-8 text-center font-bold text-3xl text-gray-800">
          阿里百炼 API 测试
        </h1>

        <AlibabaChat />

        <div className="mt-8 rounded-md border border-yellow-200 bg-yellow-50 p-4">
          <h3 className="mb-2 font-medium text-sm text-yellow-800">
            配置说明:
          </h3>
          <ul className="space-y-1 text-sm text-yellow-700">
            <li>• 请在 .env.local 文件中设置 ALIBABA_API_KEY 环境变量</li>
            <li>• API 端点: /api/message</li>
            <li>• 使用模型: qwen-turbo</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
