import { Smartphone } from 'lucide-react';
import { headers } from 'next/headers';
import Image from 'next/image';

import { PiCheckCircleFill } from '@/components/Icons';
import Layout from '@/components/layout/Layout';
import { isPalmBaby } from '@/lib/utils';

// import Download from './Download';

export default async function SuccessPage(props: {
  searchParams: Promise<{ mobile?: string; password?: string }>;
}) {
  const searchParams = await props.searchParams;
  const { mobile, password } = searchParams;

  const headersList = await headers();
  const hostname = headersList.get('host') || '';
  const isPalmBabyApp = isPalmBaby(hostname);
  return (
    <Layout>
      <main className="h-screen">
        <Image
          alt=""
          className="absolute z-0 h-screen w-full object-cover"
          height="0"
          sizes="100vw"
          src="/images/invite/inviteBG.png"
          width="0"
        />
        <section className="relative z-10 flex h-full flex-col items-center ">
          <div
            className="relative mt-8 flex items-center justify-center"
            style={{ width: '60px', height: '60px', borderRadius: '100%' }}
          >
            <div
              className="absolute inset-0 bg-white"
              style={{
                width: '30px',
                height: '30px',
                borderRadius: '100%',
                zIndex: 0,
                top: '15px',
                left: '15px',
              }}
            />
            <PiCheckCircleFill
              color={isPalmBabyApp ? '#17C5A6' : '#4e78ff'}
              fontSize={60}
              style={{ zIndex: 1 }}
            />
          </div>
          <div className="py-5 text-[#333] text-lg">
            恭喜您，已经成功创建幼儿园
          </div>
          <div className="mt-[20px] w-4/5 rounded-[16px] bg-[#FFFFFF6A] px-[70px] py-[25px] text-[#333]">
            <p className="mb-2">登录手机号: {mobile}</p>
            <p>登录密码: {password}</p>
          </div>
          <p className="mt-4 w-4/5 max-w-sm px-4 text-left text-[#333] text-sm">
            (提示:若前面已经创建了园所,则密码还是使用原来的密码)
          </p>
          <div className="mt-8 w-4/5 max-w-sm">
            <div className="rounded-2xl border border-white/20 bg-white/80 p-6 ">
              <div className="mb-4 flex items-center justify-center">
                <div className="relative">
                  <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-indigo-500 shadow-md">
                    <Smartphone className="h-6 w-6 text-white" />
                  </div>
                </div>
              </div>

              <div className="text-center">
                <h3 className="mb-2 font-medium text-base text-gray-800">
                  下载移动端应用
                </h3>
                <p className="mb-4 text-gray-600 text-sm leading-relaxed">
                  请在应用商店搜索
                  <span className="mx-1 font-semibold text-gray-800">
                    {isPalmBabyApp ? '掌心宝贝' : '掌心智校'}园丁
                  </span>
                  APP
                </p>

                <div className="flex items-center justify-center space-x-4 pt-2">
                  <div className="text-gray-500 ">如已安装请忽略</div>
                </div>
              </div>
            </div>
          </div>
          {/* <Download /> */}
        </section>
      </main>
    </Layout>
  );
}
