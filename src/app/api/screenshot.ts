// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import type { NextApiRequest, NextApiResponse } from 'next';
import puppeteer from 'puppeteer';
import fs from 'fs/promises';
import path from 'path';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  let browser = null;
  let page = null;
  const screenshotPath = path.join(__dirname, 'screenshot.jpg');
  
  try {
    const { url } = req.query;
    console.log('🚀 ~ file: screenshot.ts:13 ~ url:', typeof url);
    if (typeof url !== 'string') {
      return res.status(400);
    }
    
    browser = await puppeteer.launch({
      // executablePath: '/usr/lib64/chromium-browser/chromium-browser',
      headless: 'new',
      devtools: false,
      // userDataDir: './tmp',
      args: [
        '--disable-features=IsolateOrigins',
        '--disable-site-isolation-trials',
        '--autoplay-policy=user-gesture-required',
        '--disable-background-networking',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-breakpad',
        '--disable-client-side-phishing-detection',
        '--disable-component-update',
        '--disable-default-apps',
        '--disable-dev-shm-usage',
        '--disable-domain-reliability',
        '--disable-extensions',
        '--disable-features=AudioServiceOutOfProcess',
        '--disable-hang-monitor',
        '--disable-ipc-flooding-protection',
        '--disable-notifications',
        '--disable-offer-store-unmasked-wallet-cards',
        '--disable-popup-blocking',
        '--disable-print-preview',
        '--disable-prompt-on-repost',
        '--disable-renderer-backgrounding',
        '--disable-setuid-sandbox',
        '--disable-speech-api',
        '--disable-sync',
        '--hide-scrollbars',
        '--ignore-gpu-blacklist',
        '--metrics-recording-only',
        '--mute-audio',
        '--no-default-browser-check',
        '--no-first-run',
        '--no-pings',
        '--no-sandbox',
        '--no-zygote',
        '--password-store=basic',
        '--use-gl=swiftshader',
        '--use-mock-keychain',
        '--font-render-hinting=none',
      ],
    });
    page = await browser.newPage();
    await page.setViewport({
      width: 2480,
      height: 3508,
      deviceScaleFactor: 1,
    });

    const html = `<!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>合同详情</title>
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                background-color: #f5f5f5;
                color: #333;
            }
            .container {
                max-width: 800px;
                margin: 0 auto;
                padding: 20px;
                background-color: white;
                box-shadow: 0 0 10px rgba(0,0,0,0.1);
            }
            .header {
                text-align: center;
                margin-bottom: 30px;
                padding-bottom: 20px;
                border-bottom: 2px solid #333;
            }
            .header h1 {
                font-size: 28px;
                margin-bottom: 10px;
            }
            .content {
                margin-bottom: 30px;
            }
            .section {
                margin-bottom: 20px;
            }
            .section h2 {
                font-size: 20px;
                margin-bottom: 10px;
                color: #555;
            }
            .section p {
                line-height: 1.6;
                margin-bottom: 10px;
            }
            .footer {
                display: flex;
                justify-content: space-between;
                margin-top: 40px;
                padding-top: 20px;
                border-top: 1px solid #ddd;
            }
            .footerParty {
                flex: 1;
            }
            .footerParty h3 {
                font-size: 16px;
                margin-bottom: 5px;
            }
            .footerParty span {
                font-weight: bold;
            }
            .stamp {
                width: 150px;
                height: 150px;
                margin-bottom: 10px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>服务合同</h1>
                <p>合同编号：${url}</p>
            </div>
            <div class="content">
                <div class="section">
                    <h2>服务内容</h2>
                    <p>根据甲乙双方友好协商，就乙方向甲方提供相关服务事宜，达成如下协议：</p>
                </div>
                <div class="section">
                    <h2>服务期限</h2>
                    <p>自合同签订之日起，为期一年。</p>
                </div>
                <div class="section">
                    <h2>服务费用</h2>
                    <p>甲方应向乙方支付服务费用共计人民币 XXXX 元整。</p>
                </div>
            </div>
            <div class="footer">
                <div class="footerParty">
                    <h3 class="represent">日期:<span>2023年09月19日</span></h3>
                </div>
                <div class="footerParty">
                    <img src="https://unicorn-media.ancda.com/production/app/contract/seal-full.png" class="stamp" alt="">
                    <h3 class="party"><span>乙方</span>：深圳市云领天下科技有限公司(盖章)<span></span></h3>
                    <h3 class="party"><span>地址</span>：广东省深圳市南山区高新中区科技中二路软件园二期13栋302室掌心宝贝<span></span></h3>
                    <h3 class="party"><span>代表</span>：戴振光 <span></span></h3>
                    <h3 class="party">收款银行：中国建设银行深圳金沙支行<span></span></h3>
                    <h3 class="party"><span>帐号</span>：44201596300052518522<span></span></h3>
                    <h3 class="party"><span>电话</span>：4008879121<span></span></h3>
                    <h3 class="party"><span>日期</span>：<span>2023年09月19日</span></h3>
                </div>
            </div>
        </div>
    </body>
    </html>`;
    
    await page.setContent(html, { waitUntil: 'networkidle0' });
    await page.emulateMediaType('screen');
    
    const screenshot = await page.screenshot({
      path: screenshotPath,
      fullPage: true,
    });
    
    // Clean up browser resources
    if (page) await page.close().catch(console.error);
    if (browser) await browser.close().catch(console.error);
    
    // Clean up temporary file after sending response
    res.on('finish', async () => {
      try {
        await fs.unlink(screenshotPath).catch(console.error);
      } catch (error) {
        console.error('Error cleaning up screenshot file:', error);
      }
    });
    
    return res
      .status(200)
      .setHeader('content-type', 'image/png')
      .send(screenshot);
  } catch (error) {
    console.error('Error generating screenshot:', error);
    
    // Clean up resources in case of error
    if (page) await page.close().catch(console.error);
    if (browser) await browser.close().catch(console.error);
    
    // Clean up temporary file if it exists
    try {
      await fs.unlink(screenshotPath).catch(console.error);
    } catch (cleanupError) {
      // Ignore cleanup errors
    }
    
    return res.status(500).json({ error: 'Internal Server Error' });
  }
}