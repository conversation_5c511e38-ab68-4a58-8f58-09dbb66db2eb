import path from 'node:path';

import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import puppeteer from 'puppeteer';
import fs from 'fs/promises';

import { updatedFile } from '@/utils/updatedFile';

const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));
export async function GET(request: NextRequest) {
  let browser = null;
  let page = null;
  const pdfPath = path.join(__dirname, 'record.pdf');
  
  try {
    const { searchParams } = new URL(request.url);
    const url = searchParams.get('url');
    const reportKey = searchParams.get('reportKey');
    if (typeof url !== 'string' || typeof reportKey !== 'string') {
      return new NextResponse('参数错误', {
        status: 400,
      });
    }
    
    browser = await puppeteer.launch({
      executablePath: '/usr/bin/google-chrome-stable',
      headless: 'new',
      devtools: false,
      args: [
        '--disable-features=IsolateOrigins',
        '--disable-site-isolation-trials',
        '--autoplay-policy=user-gesture-required',
        '--disable-background-networking',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-breakpad',
        '--disable-client-side-phishing-detection',
        '--disable-component-update',
        '--disable-default-apps',
        '--disable-dev-shm-usage',
        '--disable-domain-reliability',
        '--disable-extensions',
        '--disable-features=AudioServiceOutOfProcess',
        '--disable-hang-monitor',
        '--disable-ipc-flooding-protection',
        '--disable-notifications',
        '--disable-offer-store-unmasked-wallet-cards',
        '--disable-popup-blocking',
        '--disable-print-preview',
        '--disable-prompt-on-repost',
        '--disable-renderer-backgrounding',
        '--disable-setuid-sandbox',
        '--disable-speech-api',
        '--disable-sync',
        '--hide-scrollbars',
        '--ignore-gpu-blacklist',
        '--metrics-recording-only',
        '--mute-audio',
        '--no-default-browser-check',
        '--no-first-run',
        '--no-pings',
        '--no-sandbox',
        '--no-zygote',
        '--password-store=basic',
        '--use-gl=swiftshader',
        '--use-mock-keychain',
        '--font-render-hinting=none',
      ],
    });
    page = await browser.newPage();
    await page.goto(url, { waitUntil: 'networkidle0' });
    await page.setViewport({
      width: 2480,
      height: 3508,
      deviceScaleFactor: 1,
    });

    // Wait for content to load
    await delay(2000);

    // Inject font styles
    await page.evaluate(() => {
      const style = document.createElement('style');
      style.textContent = `
        @font-face {
          font-family: 'Alibaba PuHuiTi 3.0';
          src: url('https://example.com/fonts/AlibabaPuHuiTi3.0-Regular.woff2') format('woff2');
          font-weight: normal;
          font-style: normal;
        }
        
        * {
          font-family: 'Alibaba PuHuiTi 3.0', '阿里巴巴普惠体 3.0', 'Noto Sans CJK SC', 'WenQuanYi Micro Hei', sans-serif !important;
        }
      `;
      document.head.appendChild(style);
    });

    // Find all elements with Chinese text and apply font
    await page.evaluate(() => {
      const allElements = document.querySelectorAll('*');
      for (const element of allElements) {
        if (element.textContent && /[\u4e00-\u9fff]/.test(element.textContent)) {
          (element as HTMLElement).style.fontFamily =
            "'Alibaba PuHuiTi 3.0', '阿里巴巴普惠体 3.0', 'Noto Sans CJK SC', 'WenQuanYi Micro Hei', sans-serif";
        }
      }
      document.body.offsetHeight;
    });

    await delay(2000);

    await page.emulateMediaType('screen');
    await page.pdf({
      path: pdfPath,
      margin: { top: '25px', right: '50px', bottom: '50px', left: '50px' },
      printBackground: true,
      format: 'A4',
      width: 2480,
      height: 3508,
    });
    
    // Clean up browser resources
    if (page) await page.close().catch(console.error);
    if (browser) await browser.close().catch(console.error);
    
    const date = new Date();
    const fileKey = `prod/record/pdf/${`${date.getFullYear()}-${
      date.getMonth() + 1
    }-${date.getDate()}`}/`;

    const fileName = `${`${date.getFullYear()}-${
      date.getMonth() + 1
    }-${date.getDate()}`}-${new Date().getTime()}.pdf`;
    const pdfUrl = await updatedFile(fileKey + fileName, pdfPath);
    
    // Clean up temporary file after upload
    try {
      await fs.unlink(pdfPath).catch(console.error);
    } catch (cleanupError) {
      console.error('Error cleaning up PDF file:', cleanupError);
    }
    
    return NextResponse.json({
      url: pdfUrl,
    });
  } catch (error) {
    console.error('Error generating record PDF:', error);
    
    // Clean up resources in case of error
    if (page) await page.close().catch(console.error);
    if (browser) await browser.close().catch(console.error);
    
    // Clean up temporary file if it exists
    try {
      await fs.unlink(pdfPath).catch(console.error);
    } catch (cleanupError) {
      // Ignore cleanup errors
    }
    
    return new NextResponse('Internal Server Error', {
      status: 500,
    });
  }
}