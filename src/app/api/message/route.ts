import type { NextRequest } from 'next/server';
import OpenAI from 'openai';

const openai = new OpenAI({
  apiKey: process.env.DASHSCOPE_API_KEY,
  baseURL: 'https://dashscope.aliyuncs.com/compatible-mode/v1',
});

export async function POST(request: NextRequest) {
  let stream = null;
  let isAborted = false;
  
  try {
    const { message } = await request.json();

    if (!message) {
      return new Response(JSON.stringify({ error: '消息内容不能为空' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // 创建流式响应
    stream = await openai.chat.completions.create({
      model: 'qwen-plus-latest',
      messages: [{ role: 'user', content: message }],
      stream: true,
      temperature: 0.7,
      max_tokens: 5000,
    });

    // 创建可读流
    const encoder = new TextEncoder();
    const readable = new ReadableStream({
      async start(controller) {
        try {
          for await (const chunk of stream) {
            if (isAborted) break;
            
            const content = chunk.choices[0]?.delta?.content || '';
            if (content) {
              const data = `data: ${JSON.stringify({ content })}\n\n`;
              controller.enqueue(encoder.encode(data));
            }
          }
          if (!isAborted) {
            // 发送结束标记
            controller.enqueue(encoder.encode('data: [DONE]\n\n'));
            controller.close();
          }
        } catch (error) {
          console.error('流式响应错误:', error);
          if (!isAborted) {
            controller.error(error);
          }
        }
      },
      
      // Add cleanup when the stream is cancelled
      cancel() {
        isAborted = true;
        console.log('Stream cancelled by client');
      },
    });

    // Create a response that handles client disconnect
    const response = new Response(readable, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        Connection: 'keep-alive',
      },
    });

    // Handle client disconnect
    request.signal.addEventListener('abort', () => {
      console.log('Client disconnected, cleaning up stream');
      isAborted = true;
      // The stream will be automatically cleaned up when the request ends
    });

    return response;
  } catch (error) {
    console.error('API调用失败:', error);
    
    // Ensure stream is cleaned up if it exists
    if (stream) {
      try {
        // OpenAI streams don't have explicit close method
        // but we can log the error for debugging
        console.log('Cleaning up stream after error');
      } catch (cleanupError) {
        console.error('Error cleaning up stream:', cleanupError);
      }
    }
    
    return new Response(JSON.stringify({ error: '服务器内部错误' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}