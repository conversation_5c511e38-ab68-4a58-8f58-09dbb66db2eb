import type { NextApiRequest, NextApiResponse } from 'next';
import puppeteer from 'puppeteer';
import fs from 'fs/promises';
import path from 'path';

async function timeout(ms: number) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  let browser = null;
  let page = null;
  const screenshotPath = path.join(__dirname, 'screenshot.jpg');
  
  try {
    const { url } = req.query;
    if (typeof url !== 'string') {
      return res.status(400);
    }
    
    browser = await puppeteer.launch({
      // executablePath: '/usr/lib64/chromium-browser/chromium-browser',
      headless: false,
      devtools: false,
      // defaultViewport: {
      //   width: 2480,
      //   height: 3508,
      //   deviceScaleFactor: 1,
      // },
      // userDataDir: './tmp',
      args: [
        '--disable-features=IsolateOrigins',
        '--disable-site-isolation-trials',
        '--autoplay-policy=user-gesture-required',
        '--disable-background-networking',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-breakpad',
        '--disable-client-side-phishing-detection',
        '--disable-component-update',
        '--disable-default-apps',
        '--disable-dev-shm-usage',
        '--disable-domain-reliability',
        '--disable-extensions',
        '--disable-features=AudioServiceOutOfProcess',
        '--disable-hang-monitor',
        '--disable-ipc-flooding-protection',
        '--disable-notifications',
        '--disable-offer-store-unmasked-wallet-cards',
        '--disable-popup-blocking',
        '--disable-print-preview',
        '--disable-prompt-on-repost',
        '--disable-renderer-backgrounding',
        '--disable-setuid-sandbox',
        '--disable-speech-api',
        '--disable-sync',
        '--hide-scrollbars',
        '--ignore-gpu-blacklist',
        '--metrics-recording-only',
        '--mute-audio',
        '--no-default-browser-check',
        '--no-first-run',
        '--no-pings',
        '--no-sandbox',
        '--no-zygote',
        '--password-store=basic',
        '--use-gl=swiftshader',
        '--use-mock-keychain',
        '--font-render-hinting=none',
      ],
    });
    page = await browser.newPage();
    await page.setViewport({
      width: 2480,
      height: 3508,
      deviceScaleFactor: 1,
    });
    // await page.goto(url, { waitUntil: 'networkidle0' });
    const data = {
      attrs: {
        width: 2480,
        height: 3508,
      },
      className: 'Stage',
      children: [
        {
          attrs: {},
          className: 'Layer',
          children: [
            {
              attrs: {
                width: 2480,
                height: 3508,
                source:
                  'https://edu-media.ancda.com/prod/archives/theme/my_teacher.png',
              },
              className: 'Image',
            },
            {
              attrs: {
                width: 2090,
                height: 2628,
                x: 275,
                y: 300,
                type: 'content',
              },
              className: 'Group',
              children: [
                {
                  attrs: {
                    width: 1460,
                    align: 'center',
                    x: 245,
                    y: 200,
                    fontFamily: 'DingTalk JinBuTi',
                    text: '我的老师',
                    fontSize: 146,
                    fill: '#ed6b6b',
                    editId: '2-1',
                    isEditable: true,
                    maxLength: 10,
                  },
                  className: 'Text',
                },
                {
                  attrs: {
                    source:
                      'https://crm-media.ancda.com/prod/growthArchives/1037/teacher/581336/2023-06-05/xRYVf1mj.jpg',
                    width: 1460,
                    height: 974,
                    y: 446,
                    x: 245,
                    editId: '2-2',
                    isEditable: true,
                  },
                  className: 'Image',
                },
                {
                  attrs: {
                    width: 1460,
                    align: 'center',
                    x: 245,
                    fontSize: 110,
                    fontFamily: 'DingTalk JinBuTi',
                    text: '李老师',
                    fill: '#ed6b6b',
                    y: 1535,
                    editId: '2-3',
                    isEditable: true,
                    maxLength: 10,
                  },
                  className: 'Text',
                },
                {
                  attrs: {
                    width: 1800,
                    height: 1000,
                    fontSize: 64,
                    fill: '#333333',
                    fontFamily: 'Alibaba PuHuiTi 3.0',
                    x: 60,
                    y: 1690,
                    lineHeight: 1.5,
                    text: '2023在这个学期里，你们在校园里度过了快乐的时光，学习了很多新东西，结交了许多好朋友。我相信你们也体验到了成长的快乐，学习的乐趣和友谊的意义。希望你们在未来的学习生活中都能够保持这种积极向上的态度，追求自己的梦想，勇敢面对挑战。祝愿你们在未来的道路上能够取得更大的成就！祝你们健康快乐！😍🌷🌻💯💝💕📍🎈',
                    editId: '2-4',
                    isEditable: true,
                    maxLength: 250,
                  },
                  className: 'Text',
                },
              ],
            },
            {
              attrs: {
                width: 130,
                height: 130,
                x: 2308,
                y: 3338,
                type: 'pageNum',
                name: 'pageNum',
              },
              className: 'Group',
              children: [
                {
                  attrs: {
                    radius: 65,
                    fill: '#fff',
                  },
                  className: 'Circle',
                },
                {
                  attrs: {
                    text: '1',
                    fontSize: 55,
                    width: 130,
                    fill: '#333',
                    align: 'center',
                    x: -65,
                    y: -25,
                  },
                  className: 'Text',
                },
              ],
            },
          ],
        },
      ],
    };
    const html = `<!DOCTYPE html>
    <html>
      <head>
        <script src="https://unicorn-media.ancda.com/production/app/js/konva/konva.min.js"></script>
        <meta charset="utf-8" />
        <title>Konva PDF Demo</title>
        <style>
          body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background-color: #f0f0f0;
          }
        </style>
      </head>
    
      <body>
        <div id="container"></div>
        <script>
          var sceneWidth = 2480;
          var sceneHeight = 3508;
          function imageThumbnail(cover,w,h) {
            if (!cover) {
              return '';
            }
            let newUrl;
            const domain = cover.split('/');
            if (domain[2].includes('media.ancda.com')) {
              newUrl = cover + "?x-image-process=image/resize,m_fill,w_" + w +",h_" + h + ",limit_0/format,jpg";
            }
            return newUrl || cover;
          }
    
          var stage = Konva.Node.create(${JSON.stringify(data)}, "container");
    
          var container = document.querySelector("#container");
    
          // now we need to fit stage into parent container
          var containerWidth = container.offsetWidth;
    
          // but we also make the full scene visible
          // so we need to scale all objects on canvas
          var scale = containerWidth / sceneWidth;
    
          stage.width(sceneWidth * scale);
          stage.height(sceneHeight * scale);
          stage.scale({ x: scale, y: scale });
    
          stage.find("Image").forEach((node) => {
            var imageUrl = node.getAttr("source");
            var width = node.width();
            var height = node.height();
            imageUrl = imageThumbnail(
              imageUrl,
              Math.ceil(width * scale),
              Math.ceil(height * scale)
            );
            var img = new Image();
            img.onload = () => {
              node.image(img);
              node.getLayer().batchDraw();
            };
            img.src = imageUrl;
          });
        </script>
      </body>
    </html>
    `;

    await page.setContent(html, { waitUntil: 'networkidle0' });
    // await page.emulateMediaType('print');
    await page.emulateMediaType('screen');

    // const pdfPath = path.join(__dirname, 'result.pdf');
    // const pdf = await page.pdf({
    //   path: pdfPath,
    //   margin: { top: '0px', right: '0px', bottom: '0px', left: '0px' },
    //   printBackground: true,
    //   // format: 'A4',
    //   width: 2480,
    //   height: 3508,
    // });
    const screenshot = await page.screenshot({
      path: screenshotPath,
      fullPage: true,
    });
    
    // Clean up resources
    if (page) await page.close().catch(console.error);
    if (browser) await browser.close().catch(console.error);
    
    // Clean up temporary file after sending response
    res.on('finish', async () => {
      try {
        await fs.unlink(screenshotPath).catch(console.error);
      } catch (error) {
        console.error('Error cleaning up screenshot file:', error);
      }
    });
    
    // const file = fs.readFileSync(pdfPath);
    // const stat = fs.statSync(pdfPath);
    // res.setHeader('Content-Length', stat.size);
    return (
      res
        .status(200)
        .setHeader('content-type', 'image/png')
        .setHeader('Content-Disposition', 'inline')
        // .setHeader('Access-Control-Allow-Origin', '*')
        // .setHeader('Content-Disposition', 'attachment; filename=dummy.pdf')
        .send(screenshot)
    );
  } catch (error) {
    console.error('Error generating screenshot:', error);
    
    // Clean up resources in case of error
    if (page) await page.close().catch(console.error);
    if (browser) await browser.close().catch(console.error);
    
    // Clean up temporary file if it exists
    try {
      await fs.unlink(screenshotPath).catch(console.error);
    } catch (cleanupError) {
      // Ignore cleanup errors
    }
    
    return res.status(500).json({ error: 'Internal Server Error' });
  }
}