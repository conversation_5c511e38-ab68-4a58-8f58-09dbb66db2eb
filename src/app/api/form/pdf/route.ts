import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import path from 'path';
import puppeteer from 'puppeteer';
import fs from 'fs/promises';

import { updatedFile } from '@/utils/updatedFile';
import { withMemoryMonitor, TIMEOUT } from '@/lib/memory-monitor';

export async function GET(request: NextRequest) {
  return withMemoryMonitor(async () => {
    let browser = null;
    let page = null;
    const pdfPath = path.join(__dirname, 'result.pdf');
    
    try {
      const { searchParams } = new URL(request.url);
      const url = searchParams.get('url');
      if (typeof url !== 'string') {
        return new NextResponse('参数错误', {
          status: 400,
        });
      }
      
      browser = await puppeteer.launch({
        // executablePath: '/usr/lib64/chromium-browser/chromium-browser',
        executablePath: '/usr/bin/google-chrome-stable',
        headless: 'new',
        devtools: false,
        // userDataDir: './tmp',
        args: [
          '--disable-features=IsolateOrigins',
          '--disable-site-isolation-trials',
          '--autoplay-policy=user-gesture-required',
          '--disable-background-networking',
          '--disable-background-timer-throttling',
          '--disable-backgrounding-occluded-windows',
          '--disable-breakpad',
          '--disable-client-side-phishing-detection',
          '--disable-component-update',
          '--disable-default-apps',
          '--disable-dev-shm-usage',
          '--disable-domain-reliability',
          '--disable-extensions',
          '--disable-features=AudioServiceOutOfProcess',
          '--disable-hang-monitor',
          '--disable-ipc-flooding-protection',
          '--disable-notifications',
          '--disable-offer-store-unmasked-wallet-cards',
          '--disable-popup-blocking',
          '--disable-print-preview',
          '--disable-prompt-on-repost',
          '--disable-renderer-backgrounding',
          '--disable-setuid-sandbox',
          '--disable-speech-api',
          '--disable-sync',
          '--hide-scrollbars',
          '--ignore-gpu-blacklist',
          '--metrics-recording-only',
          '--mute-audio',
          '--no-default-browser-check',
          '--no-first-run',
          '--no-pings',
          '--no-sandbox',
          '--no-zygote',
          '--password-store=basic',
          '--use-gl=swiftshader',
          '--use-mock-keychain',
          '--font-render-hinting=none',
        ],
      });
      page = await browser.newPage();
      
      // Set navigation timeout
      await page.setDefaultNavigationTimeout(TIMEOUT);
      
      await page.goto(url, { waitUntil: 'networkidle0' });

      await page.emulateMediaType('screen');

      await page.pdf({
        path: pdfPath,
        margin: { top: '50px', right: '50px', bottom: '50px', left: '50px' },
        printBackground: true,
        format: 'A4',
        width: 2480,
        height: 3508,
      });
      
      // Clean up browser resources immediately after PDF generation
      if (page) {
        await page.close().catch(console.error);
        page = null;
      }
      if (browser) {
        await browser.close().catch(console.error);
        browser = null;
      }
      
      const date = new Date();
      const fileKey = `prod/form/pdf/${`${date.getFullYear()}-${
        date.getMonth() + 1
      }-${date.getDate()}`}/`;

      const fileName = `${`${date.getFullYear()}-${
        date.getMonth() + 1
      }-${date.getDate()}`}-${new Date().getTime()}.pdf`;
      const pdfUrl = await updatedFile(fileKey + fileName, pdfPath);
      
      // Clean up temporary file after upload
      try {
        await fs.unlink(pdfPath).catch(console.error);
      } catch (cleanupError) {
        console.error('Error cleaning up PDF file:', cleanupError);
      }
      
      return NextResponse.json({
        url: pdfUrl,
      });

      // const stat = fs.statSync(pdfPath);
      // res.setHeader('Content-Length', stat.size);

      // return new Response(pdf, {
      //   status: 200,
      //   headers: {
      //     'Content-Type': 'application/pdf',
      //     'Content-Disposition': 'inline',
      //   },
      // });
    } catch (error) {
      console.error('Error generating PDF:', error);
      
      // Clean up resources in case of error
      if (page) {
        try {
          await page.close().catch(console.error);
        } catch (cleanupError) {
          console.error('Error closing page:', cleanupError);
        }
        page = null;
      }
      if (browser) {
        try {
          await browser.close().catch(console.error);
        } catch (cleanupError) {
          console.error('Error closing browser:', cleanupError);
        }
        browser = null;
      }
      
      // Clean up temporary file if it exists
      try {
        await fs.unlink(pdfPath).catch(console.error);
      } catch (cleanupError) {
        // Ignore cleanup errors
      }
      
      return new NextResponse('Internal Server Error', {
        status: 500,
      });
    }
  }, { timeout: TIMEOUT });
}