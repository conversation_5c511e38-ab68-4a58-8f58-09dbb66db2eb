import path from 'node:path';

import Canvas from 'canvas';
import Kon<PERSON> from 'konva';
import { headers } from 'next/headers';
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import QRCode from 'qrcode';

import { babyWebUrlHost, webUrlHost } from '@/constant/env';
import { isPalmBaby } from '@/lib/utils';

export async function GET(request: NextRequest) {
  let stage: Konva.Stage | null = null;
  let layer: Konva.Layer | null = null;

  try {
    const loadImg = (imageUrl: string) =>
      new Promise((resolve, _) => {
        const img = new Canvas.Image();
        img.onload = () => {
          resolve(img);
        };
        img.onerror = () => {
          console.log('图片出现异常');
          resolve({});
        };
        // img.crossOrigin = 'Anonymous'
        img.src = imageUrl;
      });
    const headersList = await headers();
    const hostname = headersList.get('host') || '';
    console.log('hostname', hostname);
    const width = 600;
    const height = 1067;
    const { searchParams } = new URL(request.url);
    const host = isPalmBaby(hostname) ? babyWebUrlHost : webUrlHost;
    console.log('🚀 ~ host:', host);
    const instId = searchParams.get('instId');
    const instName = searchParams.get('instName');
    const classId = searchParams.get('classId');
    const className = searchParams.get('className');
    // 加载字体
    const fontFile = (name: string) => {
      return path.join(__dirname, '../../../../../../src/assets/fonts/', name);
    };
    Canvas.registerFont(fontFile('DingTalkJinBuTi-Regular.ttf'), {
      family: 'DingTalk JinBuTi',
    });
    if (!(instId && instName)) {
      return new NextResponse('参数错误', {
        status: 400,
      });
    }
    let url = `${host}/invite/teacher?instId=${instId}&instName=${instName}`;
    if (classId && className) {
      url = `${host}/invite/student?instId=${instId}&instName=${instName}&classId=${classId}&className=${className}`;
    }
    // 生成二维码
    const opts = {
      errorCorrectionLevel: 'H' as const,
      type: 'image/png' as const,
      quality: 1,
      margin: 2,
      color: {
        dark: classId ? '#26A259' : '#000000',
        light: '#FFFFFF',
      },
    };
    const result = await QRCode.toDataURL(url, opts);
    if (!result) {
      return new NextResponse('生成二维码失败', {
        status: 500,
      });
    }

    // 初始化画布

    stage = new Konva.Stage({ width, height });
    // add canvas element
    layer = new Konva.Layer();
    stage.add(layer);

    // 背景图 start
    const bgImage = new Konva.Image({
      width,
      height,
    });
    layer.add(bgImage);
    const imagePath = path.join(
      __dirname,
      classId
        ? '../../../../../../src/assets/images/invite/invite-bg.jpg'
        : '../../../../../../src/assets/images/invite/invite-bg-teacher.jpg'
    );

    const bgImageObj = await loadImg(imagePath);

    bgImage.image(bgImageObj);

    const qrCodeGroup = new Konva.Group({
      x: 150,
      y: 360,
    });
    const qrCodeRect = new Konva.Rect({
      x: 0,
      y: 0,
      width: 300,
      height: 300,
      fill: classId ? '#e3f7ed' : '#f5f5f5',
      shadowColor: 'black',
      shadowBlur: 50,
      shadowOffset: { x: 0, y: 0 },
      shadowOpacity: 0.1,
    });
    qrCodeGroup.add(qrCodeRect);

    const qrcodeImg = new Konva.Image({
      x: 0,
      y: 0,
      width: 300,
      height: 300,
    });
    qrCodeGroup.add(qrcodeImg);
    layer.add(qrCodeGroup);

    const imageObj = await loadImg(result);

    qrcodeImg.image(imageObj);
    // 二维码 end

    // 标题 start
    const titleGroup = new Konva.Group({
      x: 140,
      y: 200,
      width: 320,
      height: 100,
    });
    layer.add(titleGroup);

    const line = new Konva.Rect({
      x: 0,
      y: 30,
      width: 320,
      height: 20,
      fill: classId ? '#26A259' : '#ffcc00',
      cornerRadius: 10,
    });
    titleGroup.add(line);

    const simpleText = new Konva.Text({
      x: titleGroup.width() / 2,
      y: 0,
      text: `邀请您${classId ? '加入班级' : '关注学校'}`,
      fontSize: 44,
      fontFamily: 'DingTalk JinBuTi',
      fill: classId ? '#26A259' : '#cb6f08',
    });

    simpleText.offsetX(simpleText.width() / 2);
    titleGroup.add(simpleText);
    // 标题 end

    // 班级名称 start
    const classText = new Konva.Text({
      x: stage.width() / 2,
      y: 920,
      text: instName + (classId && className ? `-${className}` : ''),
      fontSize: 32,
      fontFamily: 'DingTalk JinBuTi',
      fill: '#333333',
    });

    classText.offsetX(classText.width() / 2);
    layer.add(classText);
    // 班级名称 end

    // 生成最终图片
    const img = stage.toDataURL({
      mimeType: 'image/png',
      quality: 1,
      pixelRatio: 1,
    });

    // Clean up Konva objects
    if (layer) {
      layer.destroy();
      layer = null;
    }
    if (stage) {
      stage.destroy();
      stage = null;
    }

    // return res.status(200).json({ data: img });
    return NextResponse.json({ data: img });
  } catch (err) {
    console.error(err);

    // Clean up Konva objects in case of error
    if (layer) {
      try {
        layer.destroy();
      } catch (cleanupError) {
        console.error('Error destroying layer:', cleanupError);
      }
      layer = null;
    }
    if (stage) {
      try {
        stage.destroy();
      } catch (cleanupError) {
        console.error('Error destroying stage:', cleanupError);
      }
      stage = null;
    }

    return new NextResponse('生成二维码失败', {
      status: 500,
    });
  }
}
