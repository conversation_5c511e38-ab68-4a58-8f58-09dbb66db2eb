'use client';

import { <PERSON><PERSON>, Checkbox, InfiniteScroll, Skeleton } from 'antd-mobile';
import { Mic, PlayCircle } from 'lucide-react';
import { formatDuration } from '@/utils';
import type { RecordSelectorProps } from '../types';

export default function RecordSelector({
  records,
  selectedIds,
  onSelectionChange,
  isLoading,
  hasMore,
  onLoadMore,
  isLoadingMore,
}: RecordSelectorProps & {
  hasMore?: boolean;
  onLoadMore?: () => void;
  isLoadingMore?: boolean;
}) {
  const handleRecordToggle = (recordId: string) => {
    const newSelectedIds = selectedIds.includes(recordId)
      ? selectedIds.filter((id) => id !== recordId)
      : [...selectedIds, recordId];
    onSelectionChange(newSelectedIds);
  };

  
  const getTagColor = (color: string) => {
    const colorMap: Record<string, string> = {
      violet: 'bg-violet-100 text-violet-600',
      blue: 'bg-blue-100 text-blue-600',
      purple: 'bg-purple-100 text-purple-600',
      yellow: 'bg-yellow-100 text-yellow-600',
      green: 'bg-green-100 text-green-600',
      red: 'bg-red-100 text-red-600',
      indigo: 'bg-indigo-100 text-indigo-600',
      teal: 'bg-teal-100 text-teal-600',
    };
    return colorMap[color] || 'bg-gray-100 text-gray-600';
  };

  const renderMediaThumbnail = (media: any) => {
    if (media.type === 1) {
      // Image
      return (
        <div className="relative h-12 w-12 overflow-hidden rounded-lg bg-slate-100">
          <img
            alt=""
            className="h-full w-full object-cover"
            src={media.url || media.thumbnail}
          />
        </div>
      );
    }

    if (media.type === 2) {
      // Video
      return (
        <div className="relative flex h-12 w-12 items-center justify-center overflow-hidden rounded-lg bg-slate-100">
          <img
            alt=""
            className="h-full w-full object-cover"
            src={
              media.cover ||
              `${media.url}?x-workflow-graph-name=video-thumbnail`
            }
          />
          <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-20">
            <PlayCircle className="h-4 w-4 text-white opacity-80" />
          </div>
        </div>
      );
    }

    if (media.type === 3) {
      // Audio
      return (
        <div className="flex h-12 w-12 flex-col items-center justify-center rounded-lg bg-slate-100">
          <Mic className="h-4 w-4 text-primary" />
          <span className="text-xs">{formatDuration(media.duration)}</span>
        </div>
      );
    }

    return null;
  };

  const renderStudents = (students: any[]) => {
    if (!students || students.length === 0) return null;

    return (
      <div className="flex items-center gap-1">
        {students.slice(0, 3).map((student, index) => (
          <div className="flex items-center" key={student.id}>
            {student.avatar ? (
              <img
                alt={student.name}
                className="h-5 w-5 rounded-full object-cover"
                src={student.avatar}
              />
            ) : (
              <div className="flex h-5 w-5 items-center justify-center rounded-full bg-blue-100 text-blue-600 text-xs">
                {student.name?.[0] || ''}
              </div>
            )}
          </div>
        ))}
        {students.length > 3 && (
          <span className="text-gray-500 text-xs">+{students.length - 3}</span>
        )}
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {Array.from({ length: 5 }).map((_, index) => (
          <div className="rounded-2xl bg-white p-4 shadow-sm" key={index}>
            <Skeleton.Title animated />
            <Skeleton.Paragraph animated lineCount={2} />
          </div>
        ))}
      </div>
    );
  }

  if (records.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-20">
        <div className="text-center">
          <div className="mb-4">
            <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-slate-100">
              <svg className="h-8 w-8 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
          </div>
          <h3 className="mb-2 font-medium text-gray-900 text-lg">暂无观察记录</h3>
          <p className="text-gray-500">请先创建一些观察记录</p>
        </div>
      </div>
    );
  }

  return (
    <div>
      {/* Records list */}
      <div className="space-y-3">
        {records.map((record) => {
          const recordId = record.observationId || record.id;
          const isSelected = selectedIds.includes(recordId);

          return (
            <div
              className={`group relative overflow-hidden rounded-xl bg-white transition-all hover:shadow-md ${
                isSelected ? 'ring-2 ring-blue-500 shadow-lg' : 'shadow-sm'
              }`}
              key={recordId}
            >
              <div className="flex items-start p-4">
                <div className="mr-3 mt-1">
                  <Checkbox
                    checked={isSelected}
                    onChange={() => handleRecordToggle(recordId)}
                  />
                </div>

                <div className="flex-1 min-w-0">
                  {/* Title */}
                  <h3 className="mb-1 line-clamp-2 font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                    {record.title}
                  </h3>

                  {/* Content preview */}
                  <p className="mb-3 line-clamp-2 text-gray-600 text-sm leading-relaxed">
                    {record.content}
                  </p>

                  {/* Tags */}
                  {record.tags && record.tags.length > 0 && (
                    <div className="mb-3 flex flex-wrap gap-1.5">
                      {record.tags.slice(0, 3).map((tag) => (
                        <span
                          className={`rounded-full px-2.5 py-1 text-xs font-medium ${getTagColor(tag.color)}`}
                          key={tag.tagId}
                        >
                          {tag.tagName}
                        </span>
                      ))}
                      {record.tags.length > 3 && (
                        <span className="rounded-full bg-gray-100 px-2.5 py-1 text-gray-600 text-xs font-medium">
                          +{record.tags.length - 3}
                        </span>
                      )}
                    </div>
                  )}

                  {/* Media thumbnails */}
                  {record.medias && record.medias.length > 0 && (
                    <div className="mb-3 flex gap-2">
                      {record.medias.slice(0, 4).map((media, index) => (
                        <div key={media.id || index} className="flex-shrink-0">
                          {renderMediaThumbnail(media)}
                        </div>
                      ))}
                      {record.medias.length > 4 && (
                        <div className="flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-lg bg-gray-100 text-gray-500 text-xs font-medium">
                          +{record.medias.length - 4}
                        </div>
                      )}
                    </div>
                  )}

                  {/* Footer info */}
                  <div className="flex items-center justify-between text-gray-500 text-xs">
                    <div className="flex items-center gap-3">
                      {/* Students */}
                      {renderStudents(record.students)}

                      {/* Zone */}
                      {record.zone && (
                        <span className="rounded-md bg-slate-100 px-2 py-1 font-medium">
                          {record.zone.zoneName}
                        </span>
                      )}
                    </div>

                    {/* Create time */}
                    <time className="text-gray-400">
                      {new Date(record.createTime).toLocaleDateString('zh-CN')}
                    </time>
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Infinite scroll */}
      {hasMore && (
        <InfiniteScroll hasMore={hasMore} loadMore={onLoadMore || (() => {})}>
          {isLoadingMore && (
            <div className="py-4">
              <Skeleton.Title animated />
              <Skeleton.Paragraph animated lineCount={2} />
            </div>
          )}
        </InfiniteScroll>
      )}
    </div>
  );
}
