'use client';

import { useRouter } from 'next/navigation';
import { FaPlus } from 'react-icons/fa';

interface GenerateButtonProps {
  projectId: string;
  isGenerating?: boolean;
}

export default function GenerateButton({
  projectId,
  isGenerating = false,
}: GenerateButtonProps) {
  const router = useRouter();

  const handleClick = () => {
    if (isGenerating) {
      return;
    }

    // Navigate to record selection page
    router.push(`/pbl/summary/create?projectId=${projectId}`);
  };

  return (
    <div className="fixed right-6 bottom-6 z-50">
      <button
        className={`flex h-14 w-14 items-center justify-center rounded-full shadow-lg transition-all duration-200 ${
          isGenerating
            ? 'cursor-not-allowed bg-gray-400'
            : 'bg-blue-500 hover:bg-blue-600 active:scale-95'
        }`}
        disabled={isGenerating}
        onClick={handleClick}
        type="button"
      >
        {isGenerating ? (
          <div className="h-6 w-6 animate-spin rounded-full border-2 border-white border-t-transparent" />
        ) : (
          <FaPlus className="h-6 w-6 text-white" />
        )}
      </button>

      {/* Tooltip */}
      <div className="absolute right-0 bottom-16 mb-2 hidden whitespace-nowrap rounded-lg bg-gray-800 px-3 py-2 text-sm text-white opacity-0 transition-opacity group-hover:block group-hover:opacity-100">
        生成总结
        <div className="absolute top-full right-4 h-0 w-0 border-t-4 border-t-gray-800 border-r-4 border-r-transparent border-l-4 border-l-transparent" />
      </div>
    </div>
  );
}
