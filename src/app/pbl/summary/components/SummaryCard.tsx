'use client';

import { useMutation } from '@tanstack/react-query';
import { format } from 'date-fns';
import { useRouter } from 'next/navigation';
import {
  FaImage,
  FaMicrophone,
  FaRegClock,
  FaTrash,
  FaVideo,
} from 'react-icons/fa';
import { deletePeriodicSummary } from '@/api/pbl';
import type { SummaryCardProps } from '../types';

export default function SummaryCard({ summary, onDelete }: SummaryCardProps) {
  const router = useRouter();

  // Delete mutation
  const deleteMutation = useMutation({
    mutationFn: (summaryId: string) => deletePeriodicSummary(summaryId),
    onSuccess: () => {
      onDelete?.(summary.summaryId);
    },
    onError: (error) => {
      console.error('Failed to delete summary:', error);
      alert('删除总结时出现错误，请稍后重试');
    },
  });

  // Handle card click to navigate to detail
  const handleCardClick = () => {
    if (summary.status === 'generating') {
      return;
    }
    router.push(`/pbl/summary/detail/${summary.summaryId}`);
  };

  // Handle delete with confirmation
  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();

    if (window.confirm('确定要删除这个阶段性总结吗？删除后无法恢复。')) {
      deleteMutation.mutate(summary.summaryId);
    }
  };

  // Get media count by type
  const getMediaCounts = () => {
    const counts = { images: 0, videos: 0, audios: 0 };

    if (summary.mediaFiles) {
      for (const media of summary.mediaFiles) {
        if (media.type === 1) {
          counts.images++;
        } else if (media.type === 2) {
          counts.videos++;
        } else if (media.type === 3) {
          counts.audios++;
        }
      }
    }

    return counts;
  };

  const mediaCounts = getMediaCounts();
  const totalMediaCount = summary.mediaFiles?.length || 0;

  // Format creation date
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'yyyy-MM-dd HH:mm');
    } catch {
      return dateString;
    }
  };

  // Get status display
  const getStatusDisplay = () => {
    switch (summary.status) {
      case 'generating':
        return (
          <div className="flex items-center text-orange-600 text-sm">
            <div className="mr-2 h-2 w-2 animate-pulse rounded-full bg-orange-500" />
            生成中...
          </div>
        );
      case 'failed':
        return (
          <div className="flex items-center text-red-600 text-sm">
            <div className="mr-2 h-2 w-2 rounded-full bg-red-500" />
            生成失败
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <button
      className={`w-full overflow-hidden rounded-2xl bg-white text-left shadow-sm transition-opacity ${
        summary.status === 'generating' ? 'opacity-60' : ''
      }`}
      disabled={summary.status === 'generating'}
      onClick={handleCardClick}
      type="button"
    >
      <div className="p-4">
        {/* Title */}
        <h3 className="mb-2 line-clamp-2 font-semibold text-lg">
          {summary.title}
        </h3>

        {/* Content preview */}
        <p className="mb-3 line-clamp-3 text-gray-700 text-sm">
          {summary.summary}
        </p>

        {/* Media count display */}
        {totalMediaCount > 0 && (
          <div className="mb-3 flex items-center gap-4 text-gray-500 text-xs">
            {mediaCounts.images > 0 && (
              <div className="flex items-center">
                <FaImage className="mr-1" />
                <span>{mediaCounts.images}</span>
              </div>
            )}
            {mediaCounts.videos > 0 && (
              <div className="flex items-center">
                <FaVideo className="mr-1" />
                <span>{mediaCounts.videos}</span>
              </div>
            )}
            {mediaCounts.audios > 0 && (
              <div className="flex items-center">
                <FaMicrophone className="mr-1" />
                <span>{mediaCounts.audios}</span>
              </div>
            )}
          </div>
        )}

        {/* Status display */}
        {getStatusDisplay()}

        {/* Footer with creator and time */}
        <div className="flex items-center justify-between">
          <div className="flex items-center text-gray-600 text-sm">
            <div className="flex items-center">
              {/* For now, show user ID - can be enhanced when user API is available */}
            <span>用户 {summary.createUserId}</span>
            </div>
            <div className="mx-2">•</div>
            <div className="flex items-center">
              <FaRegClock className="mr-1 text-gray-500" />
              <span>{formatDate(summary.createTime)}</span>
            </div>
          </div>

          {/* Delete button */}
          {summary.status === 'completed' && (
            <button
              className="flex h-8 w-8 items-center justify-center rounded-full text-gray-400 transition-colors hover:bg-red-50 hover:text-red-500"
              disabled={deleteMutation.isPending}
              onClick={handleDelete}
              type="button"
            >
              <FaTrash className="h-3 w-3" />
            </button>
          )}
        </div>
      </div>
    </button>
  );
}
