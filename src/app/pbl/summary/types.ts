// TypeScript interfaces for Periodic Summary data models
import type { RecordData } from '../../record/list/mock/recordData';

// Media item interface (extending existing structure)
export interface MediaItem {
  mediaId?: number;
  url: string;
  name?: string;
  duration?: string | number;
  type: number; // 1=image, 2=video, 3=audio
  cover?: string;
  thumbnail?: string;
  // Source information for media aggregation
  sourceObservationId?: string;
  sourceObservationTitle?: string;
}

// User information interface
export interface UserInfo {
  userId: string;
  name: string;
  avatar?: string;
}

// Backend API response structure
export interface BackendPeriodicSummary {
  id: number;
  instId: string;
  projectId: string;
  summaryId: string;
  title: string;
  summary: string;
  observationIds: string[];
  createUserId: string;
  createTime: string;
  updateTime: string;
  delFlag: number;
}

// Frontend Periodic summary data structure (using backend field names directly)
export interface PeriodicSummary {
  id: number;
  instId: string;
  projectId: string;
  summaryId: string;
  title: string;
  summary: string;  // 后端使用 summary 字段，不是 content
  observationIds: string[];
  createUserId: string;
  createTime: string;  // 后端使用 createTime，不是 createdAt
  updateTime: string;  // 后端使用 updateTime，不是 updatedAt
  delFlag: number;
  // UI 需要的额外字段（可选，用于兼容现有组件）
  content?: string;  // 兼容层，指向 summary
  createdAt?: string;  // 兼容层，指向 createTime
  updatedAt?: string;  // 兼容层，指向 updateTime
  createdBy?: UserInfo;  // 用户信息（需要额外获取）
  mediaFiles?: MediaItem[];  // 媒体文件（需要额外获取）
  status?: 'generating' | 'completed' | 'failed';  // 状态（需要额外获取）
}

// Summary creation request
export interface CreateSummaryRequest {
  observationIds: string[];
  title?: string; // Optional, can be generated by LLM
}

// Record selection state
export interface RecordSelectionState {
  selectedRecords: string[];
  isGenerating: boolean;
  error?: string;
}

// Backend API Response types
export interface BackendSummaryListResponse {
  list: BackendPeriodicSummary[];
  total: number;
}

// Frontend API Response types
export interface SummaryListResponse {
  list: PeriodicSummary[];
  total: number;
  page: number;
  perPage: number;
}

export interface CreateSummaryResponse {
  summaryId: string;
  status: 'generating' | 'completed';
  message: string;
}

export interface SummaryDetailResponse extends PeriodicSummary {
  sourceObservations?: RecordData[];
}

// Component Props interfaces
export interface SummaryCardProps {
  summary: PeriodicSummary;
  onDelete?: (summaryId: string) => void;
}

export interface RecordSelectorProps {
  records: RecordData[];
  selectedIds: string[];
  onSelectionChange: (selectedIds: string[]) => void;
  isLoading?: boolean;
}

export interface GenerateButtonProps {
  isGenerating?: boolean;
}

export interface SummaryDetailProps {
  summary: SummaryDetailResponse;
  onEdit?: () => void;
  onDelete?: () => void;
}
