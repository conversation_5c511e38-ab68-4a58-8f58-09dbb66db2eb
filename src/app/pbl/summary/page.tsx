'use client';

import { useInfiniteQuery } from '@tanstack/react-query';
import { InfiniteScroll } from 'antd-mobile';
import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { getPeriodicSummaryList } from '@/api/pbl';
import GenerateButton from './components/GenerateButton';
import SummaryCard from './components/SummaryCard';
import type { PeriodicSummary, SummaryListResponse } from './types';

const pageSize = 10;

export default function PeriodicSummaryPage() {
  const searchParams = useSearchParams();
  const projectId = searchParams?.get('projectId') || '';
  const [total, setTotal] = useState(0);

  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = '阶段性总结';
    }
  }, []);

  const {
    data: infiniteSummaryData,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    refetch,
  } = useInfiniteQuery({
    queryKey: ['periodicSummaryList', projectId],
    queryFn: async ({ pageParam = 1 }) => {
      const params = {
        projectId,
        page: pageParam,
        perPage: pageSize,
      };

      try {
        const response = await getPeriodicSummaryList(params);
        const responseData = response.data as SummaryListResponse;

        const summaries = responseData.list;
        setTotal(responseData.total);

        return {
          data: summaries,
          page: pageParam,
          hasMore: summaries.length >= pageSize,
        };
      } catch (error) {
        console.error('Failed to fetch periodic summaries:', error);
        return {
          data: [],
          page: pageParam,
          hasMore: false,
        };
      }
    },
    getNextPageParam: (lastPage) => {
      if (
        lastPage.hasMore === false ||
        !lastPage.data ||
        lastPage.data.length < pageSize
      ) {
        return;
      }
      return lastPage.page + 1;
    },
  });

  // Extract summaries from infinite query data
  const summariesList = infiniteSummaryData?.pages
    ? (infiniteSummaryData.pages.flatMap((page) => {
        if (page) {
          return page.data || [];
        }
        return [];
      }) as PeriodicSummary[])
    : [];

  // Load more data handler
  const loadMore = async () => {
    if (hasNextPage && !isFetchingNextPage) {
      await fetchNextPage();
    }
  };

  // Handle summary deletion
  const handleDelete = async () => {
    // Refetch data after deletion
    await refetch();
  };

  if (isLoading) {
    return (
      <div className="py-10 text-center">
        <div className="text-gray-500 text-sm">加载中...</div>
      </div>
    );
  }

  return (
    <main className="min-h-screen bg-slate-50">
      {/* Header */}
      <div className="sticky top-0 z-10 mb-2 bg-white p-4">
        <div className="flex items-center justify-between">
          <h1 className="font-semibold text-lg">阶段性总结</h1>
          <div className="flex items-center justify-end font-normal text-gray-500 text-sm">
            <span>{total} 条总结</span>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex flex-1 flex-col overflow-y-auto px-4 py-2">
        {summariesList.length === 0 ? (
          <div className="flex flex-1 items-center justify-center py-20 text-center">
            <div className="text-center">
              <div className="mb-2 text-gray-400 text-lg">暂无阶段性总结</div>
              <div className="text-gray-500 text-sm">
                点击下方按钮生成您的第一个总结
              </div>
            </div>
          </div>
        ) : (
          <div className="mb-20">
            {summariesList.map((summary) => (
              <div className="mb-4" key={summary.summaryId}>
                <SummaryCard onDelete={handleDelete} summary={summary} />
              </div>
            ))}

            {/* Infinite scroll */}
            <InfiniteScroll
              className="!py-2"
              hasMore={!!hasNextPage}
              loadMore={loadMore}
              threshold={250}
            >
              {isFetchingNextPage && (
                <div className="py-3 text-center">
                  <span className="text-gray-500 text-sm">加载更多数据...</span>
                </div>
              )}
              {!isFetchingNextPage && hasNextPage && (
                <div className="py-3 text-center">
                  <span className="text-gray-500 text-sm">上拉加载更多</span>
                </div>
              )}
              {!(isFetchingNextPage || hasNextPage) && (
                <div className="py-3 text-center">
                  <span className="text-gray-500 text-sm">没有更多数据了</span>
                </div>
              )}
            </InfiniteScroll>
          </div>
        )}
      </div>

      <GenerateButton isGenerating={false} projectId={projectId} />
    </main>
  );
}
