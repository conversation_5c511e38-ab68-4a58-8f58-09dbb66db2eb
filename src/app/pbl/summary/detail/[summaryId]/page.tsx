'use client';

import { useQuery } from '@tanstack/react-query';
import { NavBar, SpinLoading } from 'antd-mobile';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { getPeriodicSummaryDetail } from '@/api/pbl';
import type { SummaryDetailResponse } from '../../types';
import RelatedRecordsList from './components/RelatedRecordsList';
import SummaryActionButtons from './components/SummaryActionButtons';
import SummaryDetail from './components/SummaryDetail';

interface SummaryDetailPageProps {
  params: {
    summaryId: string;
  };
}

export default function SummaryDetailPage({ params }: SummaryDetailPageProps) {
  const router = useRouter();
  const { summaryId } = params;
  const [localSummaryData, setLocalSummaryData] =
    useState<SummaryDetailResponse | null>(null);

  const {
    data: summaryData,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['periodicSummary', summaryId],
    queryFn: async () => {
      const raw = await getPeriodicSummaryDetail(summaryId);
      const backend = raw?.data?.summaryId ? raw.data : raw;

      if (!backend?.summaryId) {
        throw new Error('未找到该总结');
      }

      return backend;
    },
    enabled: !!summaryId,
    retry: 2,
    retryDelay: 1000,
  });

  useEffect(() => {
    if (summaryData) {
      setLocalSummaryData(summaryData);
    }
  }, [summaryData]);

  const handleBack = () => {
    router.push('/pbl/summary');
  };

  if (isLoading) {
    return (
      <div className="flex min-h-screen flex-col">
        <NavBar onBack={handleBack}>总结详情</NavBar>
        <div className="flex flex-1 items-center justify-center">
          <SpinLoading color="primary" />
        </div>
      </div>
    );
  }

  if (error || !(summaryData || localSummaryData)) {
    return (
      <div className="flex min-h-screen flex-col">
        <NavBar onBack={handleBack}>总结详情</NavBar>
        <div className="flex flex-1 items-center justify-center p-4">
          <div className="text-center">
            <p className="mb-4 text-gray-500">
              {error instanceof Error ? error.message : '加载失败，请重试'}
            </p>
            <button
              className="rounded-lg bg-blue-500 px-4 py-2 text-white transition-colors hover:bg-blue-600"
              onClick={() => refetch()}
              type="button"
            >
              重新加载
            </button>
          </div>
        </div>
      </div>
    );
  }

  const handleDelete = () => {
    router.push('/pbl/summary');
  };

  const handleUpdate = (updatedSummary: SummaryDetailResponse) => {
    setLocalSummaryData(updatedSummary);
    refetch();
  };

  // Use local data if available, otherwise fall back to API data
  const currentSummaryData = localSummaryData || summaryData;

  if (!currentSummaryData) {
    return null;
  }

  return (
    <div className="flex min-h-screen flex-col bg-gray-50">
      <div
        className="flex-1 space-y-6 overflow-y-auto px-4 py-4"
        style={{ paddingBottom: '80px' }}
      >
        <SummaryDetail summary={currentSummaryData} />

        {currentSummaryData.observationIds &&
          currentSummaryData.observationIds.length > 0 && (
            <RelatedRecordsList
              observationIds={currentSummaryData.observationIds}
              projectId={currentSummaryData.projectId}
            />
          )}
      </div>

      <SummaryActionButtons
        onDelete={handleDelete}
        onUpdate={handleUpdate}
        summary={currentSummaryData}
      />
    </div>
  );
}
