'use client';

import { useQuery } from '@tanstack/react-query';
import { ErrorBlock, Skeleton } from 'antd-mobile';
import { getObservationList } from '@/api/pbl';
import RecordItem from '../../../../record/list/components/RecordItem';
import type { RecordData } from '../../../../record/list/mock/recordData';

interface RelatedRecordsListProps {
  observationIds: string[];
  projectId?: string;
  title?: string;
  className?: string;
}

export default function RelatedRecordsList({
  observationIds,
  projectId,
  title = '相关观察记录',
  className = '',
}: RelatedRecordsListProps) {
  // Fetch related observation records
  const {
    data: records,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['relatedObservationRecords', observationIds],
    queryFn: async () => {
      if (!observationIds || observationIds.length === 0) {
        return [];
      }

      const response = await getObservationList({
        observationIds,
        projectId,
        page: 1,
        perPage: observationIds.length, // Fetch all related records
      });

      // Handle different response formats safely
      const responseData = response as unknown;
      let recordsList: RecordData[] = [];

      if (Array.isArray(responseData)) {
        recordsList = responseData as RecordData[];
      } else if (
        responseData &&
        typeof responseData === 'object' &&
        'list' in responseData &&
        Array.isArray((responseData as { list: RecordData[] }).list)
      ) {
        recordsList = (responseData as { list: RecordData[] }).list;
      }

      return recordsList;
    },
    enabled: !!(observationIds && observationIds.length > 0),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });

  // Don't render if no observation IDs
  if (!observationIds || observationIds.length === 0) {
    return null;
  }

  return (
    <div className={`rounded-xl bg-white p-4 shadow-sm ${className}`}>
      {/* Section title */}
      <div className="mb-4">
        <h3 className="font-semibold text-gray-900 text-lg">{title}</h3>
        <p className="mt-1 text-gray-500 text-sm">
          共 {observationIds.length} 条相关记录
        </p>
      </div>

      {/* Loading state */}
      {isLoading && (
        <div className="space-y-4">
          {Array.from({ length: 3 }).map((_, index) => (
            <div className="rounded-2xl bg-gray-50 p-4" key={index}>
              <Skeleton.Title animated />
              <Skeleton.Paragraph animated lineCount={2} />
            </div>
          ))}
        </div>
      )}

      {/* Error state */}
      {error && (
        <div className="py-8">
          <ErrorBlock
            description="无法加载相关观察记录，请稍后重试"
            status="disconnected"
            title="加载失败"
          />
        </div>
      )}

      {/* Empty state */}
      {!(isLoading || error) && (!records || records.length === 0) && (
        <div className="py-8 text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-gray-100">
            <svg
              className="h-6 w-6 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
              />
            </svg>
          </div>
          <h4 className="mb-1 font-medium text-gray-900">暂无相关记录</h4>
          <p className="text-gray-500 text-sm">未找到对应的观察记录</p>
        </div>
      )}

      {/* Records list */}
      {!(isLoading || error) && records && records.length > 0 && (
        <div className="space-y-3">
          {records.map((record) => (
            <div key={record.id || record.observationId}>
              <RecordItem record={record} />
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
