'use client';

import { format } from 'date-fns';
import { Calendar, FileText, Image as ImageIcon, User } from 'lucide-react';
import type { ReactElement } from 'react';
import Media from '../../../../record/detail/components/Media';
import type { SummaryDetailProps } from '../../../types';
import { processMediaForSummary } from '../../../utils/mediaUtils';

export default function SummaryDetail({
  summary,
}: Omit<SummaryDetailProps, 'onDelete' | 'onEdit'>): ReactElement {
  const processedMedia = processMediaForSummary(
    summary.sourceObservations || [],
    summary.mediaFiles || []
  );

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return format(date, 'yyyy年M月d日 HH:mm');
    } catch {
      return dateString;
    }
  };

  return (
    <div className="bg-white">
      {/* Content Section */}
      <div className="p-4">
        <div className="mb-6 ">
          {/* Title */}
          <h1 className="mb-2 font-bold text-gray-900 text-xl leading-tight">
            {summary.title}
          </h1>
          <div className="rounded-lg bg-blue-50 p-4">
            <div className="whitespace-pre-line text-gray-800 leading-relaxed">
              {summary.summary}
            </div>
          </div>
        </div>

        {/* Media Section */}
        {processedMedia && processedMedia.length > 0 && (
          <div className="mb-6">
            <div className="mb-3 flex items-center gap-2 font-semibold text-gray-900">
              <ImageIcon className="h-5 w-5 text-green-500" />
              <span>相关媒体</span>
              <span className="font-normal text-gray-500 text-sm">
                ({processedMedia.length})
              </span>
            </div>
            <Media media={processedMedia} />
          </div>
        )}

        {/* Source Observations Section */}
        {summary.sourceObservations &&
          summary.sourceObservations.length > 0 && (
            <div className="mb-6">
              <div className="mb-3 flex items-center gap-2 font-semibold text-gray-900">
                <FileText className="h-5 w-5 text-purple-500" />
                <span>来源观察记录</span>
                <span className="font-normal text-gray-500 text-sm">
                  ({summary.sourceObservations.length})
                </span>
              </div>
              <div className="space-y-3">
                {summary.sourceObservations.map((record) => (
                  <div
                    className="rounded-lg border border-gray-200 bg-gray-50 p-3"
                    key={record.observationId}
                  >
                    <div className="mb-2 font-medium text-gray-900 text-sm">
                      {record.title || '观察记录'}
                    </div>
                    <div className="line-clamp-2 overflow-hidden text-gray-600 text-sm">
                      {record.content}
                    </div>
                    <div className="mt-2 flex items-center gap-2 text-gray-500 text-xs">
                      <Calendar className="h-3 w-3" />
                      <span>{formatDate(record.createTime || '')}</span>
                      {record.medias && record.medias.length > 0 && (
                        <>
                          <span>•</span>
                          <ImageIcon className="h-3 w-3" />
                          <span>{record.medias.length} 个媒体文件</span>
                        </>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        <div className="mb-4 flex items-center gap-3">
          <div className="flex items-center">
            {summary.createdBy?.avatar ? (
              <img
                alt="创建者头像"
                className="h-10 w-10 rounded-full object-cover"
                src={summary.createdBy.avatar}
              />
            ) : (
              <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gray-200">
                <User className="h-5 w-5 text-gray-500" />
              </div>
            )}
          </div>
          <div>
            <span className="font-medium text-gray-900">
              {summary.createdBy?.name || '创建者'}
            </span>
            <div className="flex items-center gap-1 text-gray-500 text-sm">
              <Calendar className="h-3 w-3" />
              <span>{formatDate(summary.createTime)}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
