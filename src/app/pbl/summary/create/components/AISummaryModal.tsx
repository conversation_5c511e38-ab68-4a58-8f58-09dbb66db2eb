'use client';

import { <PERSON><PERSON>, <PERSON><PERSON>, TextArea, Toast } from 'antd-mobile';
import { useState } from 'react';
import { aiGenerateContent } from '@/api/pbl';
import type { RecordData } from '../../../record/list/mock/recordData';

// 定义正则表达式在顶层作用域以提高性能
const TITLE_REGEX = /标题[：:]\s*(.+?)(?=\n|总结)/;
const SUMMARY_REGEX = /总结[：:]\s*([\s\S]+?)$/;

interface AISummaryModalProps {
  visible: boolean;
  onClose: () => void;
  selectedRecords: RecordData[];
  onConfirm: (title: string, summary: string) => void;
}

export default function AISummaryModal({
  visible,
  onClose,
  selectedRecords,
  onConfirm,
}: AISummaryModalProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedTitle, setGeneratedTitle] = useState('');
  const [generatedSummary, setGeneratedSummary] = useState('');
  const [step, setStep] = useState<'generating' | 'editing'>('generating');

  const handleGenerateContent = async () => {
    if (selectedRecords.length === 0) {
      Toast.show({
        content: '请至少选择一条观察记录',
        position: 'center',
      });
      return;
    }

    setIsGenerating(true);
    setStep('generating');

    try {
      // 构建 prompt，包含选中记录的内容
      const recordContents = selectedRecords
        .map(
          (record, index) =>
            `${index + 1}. ${record.title}：${record.content || ''}`
        )
        .join('\n');

      const prompt = `请根据以下观察记录生成一个PBL阶段性总结，需要生成标题和总结内容。

要求：
1. 标题要简洁明确，体现主要活动主题
2. 总结内容要包含：
   - 概括孩子们的主要活动和表现
   - 突出体现的能力发展（如探索、合作、语言表达等）
   - 总结学习成果和亮点
   - 提出下一步的教学计划或建议
3. 语言要专业、温暖，适合写入教学报告
4. 请按照以下格式输出：
   标题：[在这里写标题]
   总结：[在这里写详细的总结内容]

观察记录：
${recordContents}`;

      const response = await aiGenerateContent({
        conversation_id: '',
        files: [],
        inputs: [],
        query: prompt,
        response_mode: 'blocking',
      });

      if (response?.answer) {
        // 解析 AI 返回的内容，提取标题和总结
        const content = response.answer;
        const titleMatch = content.match(TITLE_REGEX);
        const summaryMatch = content.match(SUMMARY_REGEX);

        const title = titleMatch?.[1]?.trim() || '阶段性总结';
        const summary = summaryMatch?.[1]?.trim() || content;

        setGeneratedTitle(title);
        setGeneratedSummary(summary);
        setStep('editing');

        Toast.show({
          content: 'AI 总结生成成功，您可以进行修改',
          position: 'center',
        });
      } else {
        throw new Error('AI 返回内容为空');
      }
    } catch (err) {
      console.error('生成总结失败:', err);
      Toast.show({
        content: '生成失败，请重试',
        position: 'center',
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleConfirm = () => {
    if (!(generatedTitle.trim() && generatedSummary.trim())) {
      Toast.show({
        content: '请输入标题和总结内容',
        position: 'center',
      });
      return;
    }

    onConfirm(generatedTitle, generatedSummary);
    handleClose();
  };

  const handleClose = () => {
    setGeneratedTitle('');
    setGeneratedSummary('');
    setStep('generating');
    setIsGenerating(false);
    onClose();
  };

  const handleRegenerateContent = () => {
    setStep('generating');
    handleGenerateContent();
  };

  return (
    <Dialog
      actions={
        step === 'editing'
          ? [
              [
                {
                  key: 'cancel',
                  text: '取消',
                  onClick: handleClose,
                },
                {
                  key: 'confirm',
                  text: '确认并创建',
                  onClick: handleConfirm,
                },
              ],
            ]
          : []
      }
      content={
        <div className="max-h-[70vh] overflow-y-auto">
          {step === 'generating' ? (
            <div className="py-8">
              <div className="text-center">
                <div className="mb-4">
                  <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-blue-100">
                    <span className="text-2xl">🤖</span>
                  </div>
                  <h3 className="mb-2 font-medium text-gray-900 text-lg">
                    AI 智能生成中
                  </h3>
                  <p className="text-gray-500 text-sm">
                    已选择 {selectedRecords.length} 条观察记录，正在生成总结...
                  </p>
                </div>
                {!isGenerating && (
                  <Button
                    block
                    color="primary"
                    onClick={handleGenerateContent}
                    size="large"
                  >
                    开始生成
                  </Button>
                )}
                {isGenerating && (
                  <div className="text-center">
                    <div className="inline-flex items-center text-blue-600">
                      <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-blue-600 border-t-transparent" />
                      正在生成中，请稍候...
                    </div>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <div>
                <div className="mb-2 block font-medium text-gray-700 text-sm">
                  总结标题
                </div>
                <TextArea
                  autoSize={{ minRows: 1, maxRows: 3 }}
                  onChange={setGeneratedTitle}
                  placeholder="请输入总结标题"
                  rows={1}
                  value={generatedTitle}
                />
              </div>

              <div>
                <div className="mb-2 block font-medium text-gray-700 text-sm">
                  总结内容
                </div>
                <TextArea
                  autoSize={{ minRows: 8, maxRows: 15 }}
                  onChange={setGeneratedSummary}
                  placeholder="请输入总结内容"
                  rows={8}
                  value={generatedSummary}
                />
              </div>

              <div className="text-center">
                <Button
                  disabled={isGenerating}
                  fill="outline"
                  onClick={handleRegenerateContent}
                  size="small"
                >
                  重新生成
                </Button>
              </div>
            </div>
          )}
        </div>
      }
      onClose={handleClose}
      title="AI 智能生成总结"
      visible={visible}
    />
  );
}
