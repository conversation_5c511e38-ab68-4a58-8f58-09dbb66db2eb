import type { RecordData } from '../../record/list/mock/recordData';

// Mock student data
const mockStudents = [
  { id: '1', name: '小明', avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face' },
  { id: '2', name: '小红', avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=40&h=40&fit=crop&crop=face' },
  { id: '3', name: '小华', avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=40&h=40&fit=crop&crop=face' },
  { id: '4', name: '小李', avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face' },
  { id: '5', name: '小王', avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=40&h=40&fit=crop&crop=face' },
];

// Mock zone data
const mockZones = [
  { zoneId: '1', zoneName: '阅读区' },
  { zoneId: '2', zoneName: '建构区' },
  { zoneId: '3', zoneName: '美工区' },
  { zoneId: '4', zoneName: '科学区' },
  { zoneId: '5', zoneName: '角色扮演区' },
];

// Mock tag data
const mockTags = [
  { tagId: '1', tagName: '社交能力', color: 'blue' },
  { tagId: '2', tagName: '动手能力', color: 'green' },
  { tagId: '3', tagName: '创造力', color: 'purple' },
  { tagId: '4', tagName: '语言表达', color: 'yellow' },
  { tagId: '5', tagName: '逻辑思维', color: 'red' },
  { tagId: '6', tagName: '团队协作', color: 'indigo' },
  { tagId: '7', tagName: '问题解决', color: 'teal' },
  { tagId: '8', tagName: '专注力', color: 'violet' },
];

// Mock media data
const mockMediaItems = [
  {
    id: '1',
    type: 1,
    url: 'https://images.unsplash.com/photo-1503676260728-1c00da094a0b?w=400&h=300&fit=crop',
    thumbnail: 'https://images.unsplash.com/photo-1503676260728-1c00da094a0b?w=200&h=150&fit=crop',
  },
  {
    id: '2',
    type: 2,
    url: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=300&fit=crop',
    cover: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=200&h=150&fit=crop',
  },
  {
    id: '3',
    type: 3,
    duration: '00:02:30',
  },
];

// Mock observation templates
const observationTemplates = [
  {
    title: '积木搭建活动观察',
    content: '今天小明和小红一起在建构区进行积木搭建活动。他们选择了一起建造一座城堡。小明负责搭建塔楼，小红负责建造城墙。在过程中，两人能够很好地沟通协作，当积木倒塌时，他们没有气馁，而是一起寻找原因并重新搭建。最终成功完成了城堡的建造。',
  },
  {
    title: '绘画活动记录',
    content: '在美工区的绘画活动中，小华表现出了丰富的想象力。他使用多种颜色创作了一幅关于海洋世界的画作。在绘画过程中，他能够专注地完成任务，并且主动与同伴分享自己的创作思路。作品色彩搭配和谐，构图完整。',
  },
  {
    title: '科学探究活动',
    content: '小李在科学区对磁铁产生了浓厚兴趣。他主动探索不同材料的磁性，并记录下自己的发现。在实验过程中，他表现出了良好的观察能力和探究精神，能够提出问题并尝试通过实验来寻找答案。',
  },
  {
    title: '角色扮演游戏',
    content: '小王和小伙伴们在角色扮演区进行了"小医生"的游戏。小王扮演医生的角色，能够认真地为"病人"检查身体。在游戏过程中，他能够运用生活经验，模仿医生的行为举止，表现出较强的角色代入能力。',
  },
  {
    title: '阅读分享活动',
    content: '在阅读区，小明主动与同伴分享自己喜欢的绘本。他能够清楚地讲述故事的主要内容，并回答同伴提出的问题。分享过程中，他表现出了良好的语言表达能力和自信心。',
  },
];

// Generate mock observation records
export function generateMockObservationRecords(count: number = 20): RecordData[] {
  const records: RecordData[] = [];
  const now = new Date();
  
  for (let i = 0; i < count; i++) {
    const template = observationTemplates[i % observationTemplates.length];
    const date = new Date(now);
    date.setDate(date.getDate() - Math.floor(i / 3));
    
    // Randomly select 1-3 students
    const studentCount = Math.floor(Math.random() * 3) + 1;
    const selectedStudents = mockStudents
      .sort(() => 0.5 - Math.random())
      .slice(0, studentCount);
    
    // Randomly select 1-2 media items
    const mediaCount = Math.floor(Math.random() * 2) + 1;
    const selectedMedia = mockMediaItems
      .slice(0, mediaCount)
      .map((media, index) => ({
        ...media,
        id: `${i}_${media.id}`,
      }));
    
    // Randomly select 2-4 tags
    const tagCount = Math.floor(Math.random() * 3) + 2;
    const selectedTags = mockTags
      .sort(() => 0.5 - Math.random())
      .slice(0, tagCount);
    
    // Random zone
    const zone = mockZones[Math.floor(Math.random() * mockZones.length)];
    
    records.push({
      observationId: `obs_${String(i + 1).padStart(6, '0')}`,
      id: `obs_${String(i + 1).padStart(6, '0')}`,
      title: template.title,
      content: template.content,
      createTime: date.toISOString(),
      students: selectedStudents,
      medias: selectedMedia,
      tags: selectedTags,
      zone,
      projectId: 'project_001',
    });
  }
  
  return records;
}

// Simulate API delay
export function simulateApiDelay<T>(data: T, delay: number = 500): Promise<T> {
  return new Promise((resolve) => {
    setTimeout(() => resolve(data), delay);
  });
}

// Mock API response
export function getMockObservationList(page: number, perPage: number = 20) {
  const allRecords = generateMockObservationRecords(100);
  const start = (page - 1) * perPage;
  const end = start + perPage;
  const pageRecords = allRecords.slice(start, end);
  
  return {
    list: pageRecords,
    total: allRecords.length,
    page,
    perPage,
  };
}