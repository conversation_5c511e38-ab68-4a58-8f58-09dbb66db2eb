import type { RecordData } from '../../record/list/mock/recordData';
import type { MediaItem } from '../types';

/**
 * Aggregate media files from multiple observation records
 * @param sourceObservations - Array of observation records
 * @returns Aggregated media files with source information
 */
export function aggregateMediaFromRecords(
  sourceObservations: RecordData[]
): MediaItem[] {
  const allMedia: MediaItem[] = [];

  sourceObservations.forEach((record) => {
    if (record.medias && Array.isArray(record.medias)) {
      record.medias.forEach((media) => {
        allMedia.push({
          ...media,
          // Add source information for attribution
          sourceObservationId: record.observationId,
          sourceObservationTitle:
            record.title || record.content?.substring(0, 50) + '...',
        });
      });
    }
  });

  return allMedia;
}

/**
 * Deduplicate media files based on URL and type
 * @param mediaFiles - Array of media files to deduplicate
 * @returns Deduplicated media files
 */
export function deduplicateMedia(mediaFiles: MediaItem[]): MediaItem[] {
  const seen = new Set<string>();
  const deduplicated: MediaItem[] = [];

  mediaFiles.forEach((media) => {
    // Create a unique key based on URL and type
    const key = `${media.url}-${media.type}`;

    if (!seen.has(key)) {
      seen.add(key);
      deduplicated.push(media);
    }
  });

  return deduplicated;
}

/**
 * Group media files by type for organized display
 * @param mediaFiles - Array of media files to group
 * @returns Object with media files grouped by type
 */
export function groupMediaByType(mediaFiles: MediaItem[]) {
  const grouped = {
    images: [] as MediaItem[],
    videos: [] as MediaItem[],
    audios: [] as MediaItem[],
  };

  mediaFiles.forEach((media) => {
    if (media.type === 1) {
      grouped.images.push(media);
    } else if (media.type === 2) {
      grouped.videos.push(media);
    } else if (media.type === 3) {
      grouped.audios.push(media);
    }
  });

  return grouped;
}

/**
 * Process media files for summary display
 * Aggregates from source observations, deduplicates, and prepares for display
 * @param sourceObservations - Array of source observation records
 * @param existingMedia - Existing media files from summary (if any)
 * @returns Processed media files ready for display
 */
export function processMediaForSummary(
  sourceObservations: RecordData[] = [],
  existingMedia: MediaItem[] = []
): MediaItem[] {
  // Aggregate media from source observations
  const aggregatedMedia = aggregateMediaFromRecords(sourceObservations);

  // Combine with existing media
  const allMedia = [...existingMedia, ...aggregatedMedia];

  // Deduplicate and return
  return deduplicateMedia(allMedia);
}
