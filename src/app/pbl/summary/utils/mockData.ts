import type { PeriodicSummary, MediaItem, UserInfo } from './types';

// Mock user data
const mockUsers: UserInfo[] = [
  {
    userId: '1',
    name: '张老师',
    avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=80&h=80&fit=crop&crop=face',
  },
  {
    userId: '2',
    name: '李老师',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=80&h=80&fit=crop&crop=face',
  },
];

// Mock media items
const mockMediaItems: MediaItem[] = [
  {
    mediaId: 1,
    url: 'https://images.unsplash.com/photo-1503676260728-1c00da094a0b?w=400&h=300&fit=crop',
    name: '项目展示.jpg',
    type: 1,
    thumbnail: 'https://images.unsplash.com/photo-1503676260728-1c00da094a0b?w=200&h=150&fit=crop',
  },
  {
    mediaId: 2,
    url: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=300&fit=crop',
    name: '学生活动.mp4',
    type: 2,
    duration: '00:02:30',
    cover: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=200&h=150&fit=crop',
  },
  {
    mediaId: 3,
    url: 'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=400&h=300&fit=crop',
    name: '课堂记录.jpg',
    type: 1,
    thumbnail: 'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=200&h=150&fit=crop',
  },
];

// Mock summary templates
const summaryTemplates = [
  {
    title: '第一阶段项目总结',
    content: '在第一阶段的项目实施过程中，学生们展现出了极大的学习热情和创造力。通过小组合作，他们不仅掌握了核心知识点，还培养了解决问题的能力。特别是在项目设计环节，学生们能够灵活运用所学知识，提出了许多富有创意的解决方案。观察发现，学生的团队协作能力有明显提升，沟通表达也更加自信。建议在下一阶段加强实践环节，提供更多动手操作的机会。',
  },
  {
    title: '期中学习进展总结',
    content: '经过半个学期的学习，学生们在知识掌握和能力发展方面都取得了显著进步。通过项目式学习的方式，抽象的理论知识变得生动具体，学生的学习兴趣被有效激发。值得关注的是，部分学生在自主学习方面仍需要引导，建议加强学习方法的指导。总体而言，项目式学习模式在本班级的实施效果良好，学生参与度高，学习氛围浓厚。',
  },
  {
    title: '小组合作项目总结',
    content: '本次小组合作项目围绕环保主题展开，学生们通过实地调研、数据收集、方案设计等环节，深入理解了环保的重要性。在项目过程中，学生们展现出了良好的团队合作精神，分工明确，相互配合。每个小组都完成了高质量的成果展示，体现了较强的创新意识和实践能力。通过项目实施，学生们的环保意识得到提升，社会责任感也有所增强。',
  },
  {
    title: '单元学习总结',
    content: '本单元的学习重点在于培养学生的科学探究能力。通过一系列精心设计的活动，学生们学会了如何提出问题、设计方案、收集分析数据并得出结论。从学习成果来看，大部分学生掌握了基本的科学探究方法，能够运用科学思维解决问题。建议后续可以引入更多开放性的探究任务，进一步激发学生的探究欲望。',
  },
  {
    title: '实践活动总结',
    content: '本次社会实践活动让学生走出了教室，在真实的社会环境中学习和体验。通过参观企业、访问社区、参与志愿服务等活动，学生们对课堂所学知识有了更深入的理解。实践活动中，学生们表现出了积极的态度和良好的适应能力。特别值得肯定的是，学生们能够主动思考，将理论知识与实际生活联系起来，体现了较强的知识迁移能力。',
  },
];

// Generate mock summaries
export function generateMockSummaries(count: number = 10): PeriodicSummary[] {
  const summaries: PeriodicSummary[] = [];
  const now = new Date();
  
  for (let i = 0; i < count; i++) {
    const template = summaryTemplates[i % summaryTemplates.length];
    const user = mockUsers[i % mockUsers.length];
    const date = new Date(now);
    date.setDate(date.getDate() - i * 3);
    
    // Make the first summary have "generating" status for testing
    const status = i === 0 ? 'generating' : 'completed';
    
    // Randomly select 1-3 media items (but not for generating status)
    let selectedMedia: MediaItem[] = [];
    if (status !== 'generating') {
      const mediaCount = Math.floor(Math.random() * 3) + 1;
      selectedMedia = mockMediaItems
        .slice(0, mediaCount)
        .map((media, index) => ({
          ...media,
          mediaId: i * 10 + index + 1,
        }));
    }
    
    // Generate random observation IDs
    const observationIds = Array.from(
      { length: Math.floor(Math.random() * 5) + 3 },
      (_, index) => `obs_${i}_${index + 1}`
    );
    
    summaries.push({
      summaryId: `summary_${String(i + 1).padStart(6, '0')}`,
      title: template.title,
      content: template.content,
      createdAt: date.toISOString(),
      updatedAt: date.toISOString(),
      createdBy: user,
      observationIds,
      mediaFiles: selectedMedia,
      status,
    });
  }
  
  return summaries;
}

// Simulate API delay
export function simulateApiDelay<T>(data: T, delay: number = 500): Promise<T> {
  return new Promise((resolve) => {
    setTimeout(() => resolve(data), delay);
  });
}

// Mock API response
export function getMockSummaryList(page: number, perPage: number = 10) {
  const allSummaries = generateMockSummaries(50);
  const start = (page - 1) * perPage;
  const end = start + perPage;
  const pageSummaries = allSummaries.slice(start, end);
  
  return {
    list: pageSummaries,
    total: allSummaries.length,
    page,
    perPage,
  };
}