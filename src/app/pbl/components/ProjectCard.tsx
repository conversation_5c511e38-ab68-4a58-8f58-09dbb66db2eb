'use client';

import { Card } from 'antd-mobile';
import { clsx } from 'clsx';
import { format } from 'date-fns';
import { Activity, Calendar, Edit, Eye, Trash2, Users } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { PiDotsThreeOutlineFill } from 'react-icons/pi';
import { calculateProjectProgress } from '@/utils/projectProgress';
import PopoverMenu from './PopoverMenu';

interface Project {
  instId?: string;
  deptId?: string;
  projectId: string;
  projectName: string;
  description: string;
  theme?: string;
  drivingQuestion?: string;
  learningGoals?: string;
  keyword?: string;
  startDate: string;
  endDate: string;
  status?: number;
  delFlag?: number;
  createUserId?: string;
  createTime: string;
  updateTime: string;
  cover?: string;
  dept?: {
    name: string;
  };
}

interface ProjectCardProps {
  project: Project;
  onDelete?: (projectId: string) => void;
}

export default function ProjectCard({ project, onDelete }: ProjectCardProps) {
  const router = useRouter();

  const { progress, statusText, isActive } = calculateProjectProgress(
    project.startDate,
    project.endDate
  );

  const statusColorClass = (() => {
    if (isActive) {
      return 'border-emerald-200 bg-emerald-50 text-emerald-700';
    }
    if (statusText === '未开始') {
      return 'border-sky-200 bg-sky-50 text-sky-700';
    }
    return 'border-slate-200 bg-slate-50 text-slate-700';
  })();

  const statusCapsule = clsx(
    'absolute top-3 right-3 rounded-full border px-2.5 py-1 font-medium text-xs',
    statusColorClass
  );

  const handleView = () => {
    router.push(`/pbl/detail?projectId=${project.projectId}`);
  };

  const handleEdit = () => {
    router.push(`/pbl/create?projectId=${project.projectId}`);
  };

  return (
    <Card
      className="group relative overflow-hidden rounded-2xl border border-slate-200 bg-white shadow-sm ring-1 ring-black/0 transition-all duration-300 hover:shadow-md"
      onClick={handleView}
    >
      {/* 封面 */}
      <div className="relative isolate h-40">
        {project.cover ? (
          <>
            <img
              alt={project.projectName}
              className="absolute inset-0 h-full w-full object-cover"
              src={project.cover}
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent" />
          </>
        ) : (
          <div className="absolute inset-0 bg-gradient-to-br from-indigo-100 via-sky-100 to-purple-100" />
        )}

        <div className="relative z-10 flex h-full">
          {/* 顶部图标区域 */}
          <div className="absolute flex h-5/6 w-full items-center justify-center">
            <div
              className={clsx(
                'rounded-2xl border p-2 backdrop-blur',
                project.cover
                  ? 'border-white/30 bg-white/20'
                  : 'border-white/60 bg-white/60'
              )}
            >
              <div
                className={clsx(
                  'mx-auto flex h-12 w-12 items-center justify-center rounded-xl shadow-inner',
                  project.cover ? 'bg-white/30' : 'bg-indigo-100'
                )}
              >
                <Activity
                  className={clsx(
                    'h-6 w-6',
                    project.cover ? 'text-white drop-shadow' : 'text-indigo-600'
                  )}
                />
              </div>
            </div>
          </div>

          {/* 底部标题区域 */}
          <div className="absolute bottom-0 w-full p-4 pb-3">
            <h3
              className={clsx(
                'line-clamp-2 font-semibold text-xl tracking-tight',
                project.cover ? 'text-white drop-shadow-sm' : 'text-slate-900'
              )}
            >
              {project.projectName}
            </h3>
          </div>
        </div>

        <span className={statusCapsule}>{statusText}</span>
      </div>

      {/* 内容 */}
      <div className="px-2 pt-4">
        <p className="mb-4 line-clamp-2 text-slate-500 text-sm">
          {project.description}
        </p>

        {/* 进度 */}
        <div className="mb-4">
          <div className="mb-1 flex items-center justify-between">
            <span className="text-slate-500 text-sm">项目进度</span>
            <span className="font-medium text-slate-900 text-sm">
              {progress}%
            </span>
          </div>
          <div className="h-2 w-full rounded-full bg-slate-200">
            <div
              className="h-2 rounded-full bg-gradient-to-r from-indigo-400 to-indigo-500 transition-all duration-500"
              style={{ width: `${progress}%` }}
            />
          </div>
        </div>

        {/* 信息 */}
        <div className="mb-4 space-y-2">
          <div className="flex items-center text-slate-600 text-sm">
            <Calendar className="mr-2 h-4 w-4" />
            <span>
              {format(new Date(project.startDate), 'M月d日')} -{' '}
              {format(new Date(project.endDate), 'M月d日')}
            </span>
          </div>
          <div className="flex items-center text-slate-600 text-sm">
            <Users className="mr-2 h-4 w-4" />
            <span>{project.dept?.name || ''}</span>
          </div>
        </div>

        {/* 标签 */}
        {!!project.keyword && (
          <div className="mb-4 flex flex-wrap gap-2">
            {project.keyword.split(',').map((tag) => (
              <span
                className="rounded-md bg-slate-100 px-2 py-1 text-slate-700 text-xs"
                key={`${project.projectId}-${tag.trim()}`}
              >
                {tag.trim()}
              </span>
            ))}
          </div>
        )}

        {/* 操作 */}
        <div className="flex items-center justify-between border-slate-100 border-t pt-3">
          <div className="flex gap-2">
            <button
              className="flex items-center rounded-md p-1.5 text-indigo-600 text-sm transition-colors hover:bg-indigo-50"
              onClick={handleView}
              type="button"
            >
              <Eye className="mr-1 h-4 w-4" />
              查看
            </button>
          </div>
          <div className="flex items-center gap-2">
            <div className="relative z-20">
              <PopoverMenu
                actions={[
                  {
                    text: '编辑',
                    key: 'edit',
                    icon: <Edit color="#333" size={16} />,
                    onClick: handleEdit,
                  },
                  {
                    text: '删除',
                    key: 'delete',
                    icon: <Trash2 className="text-red-500" size={16} />,
                    onClick: () => onDelete?.(project.projectId),
                  },
                ]}
                placement="bottom-end"
                trigger="click"
              >
                <button
                  aria-label="更多操作"
                  className="flex items-center rounded-md p-1.5 text-slate-400 transition-colors hover:bg-slate-100 hover:text-slate-600"
                  type="button"
                >
                  <PiDotsThreeOutlineFill fontSize={16} />
                </button>
              </PopoverMenu>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
}
