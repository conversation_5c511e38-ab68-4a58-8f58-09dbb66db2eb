/** biome-ignore-all lint/correctness/noUnusedFunctionParameters: <explanation> */
/** biome-ignore-all lint/correctness/noUndeclaredVariables: <explanation> */
/** biome-ignore-all lint/style/useCollapsedElseIf: <explanation> */
'use client';

import { Button, Dialog, Form, Input, List, Toast } from 'antd-mobile';
import { ChevronDown, ChevronRight, Edit3, Plus, Trash2 } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';
import {
  createReportArea,
  deleteReportArea,
  getReportAreas,
  updateReportArea,
} from '@/api/pbl';

// 数据类型定义

interface AreaCategory {
  zoneId: string;
  zoneName: string;
  selected: boolean;
  parentId: string;
  children: AreaCategory[];
}

interface EditForm {
  type: 'category' | 'item';
  mode: 'add' | 'edit';
  categoryId?: string;
  itemId?: string;
  currentName?: string;
}

const AreaManagePage = () => {
  const [categories, setCategories] = useState<AreaCategory[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
  const [editForm, setEditForm] = useState<EditForm | null>(null);
  const [form] = Form.useForm();

  // 初始化数据

  // 加载数据
  const loadData = useCallback(() => {
    getReportAreas().then((res: any) => {
      setCategories(res.list);
    });
  }, []);

  useEffect(() => {
    loadData();
    if (typeof document !== 'undefined') {
      document.title = '区域管理';
    }
  }, [loadData]);

  // 保存数据
  const saveData = useCallback((newCategories: AreaCategory[]) => {
    try {
      localStorage.setItem('pbl-area-settings', JSON.stringify(newCategories));
      setCategories(newCategories);
    } catch (error) {
      console.error('保存数据失败:', error);
      Toast.show({
        icon: 'fail',
        content: '保存失败',
      });
    }
  }, []);

  // 切换展开状态
  const toggleExpanded = useCallback((categoryId: string) => {
    setExpandedKeys((prev) =>
      prev.includes(categoryId)
        ? prev.filter((id) => id !== categoryId)
        : [...prev, categoryId]
    );
  }, []);

  // 打开编辑弹窗
  const openEditDialog = useCallback(
    (editConfig: EditForm) => {
      setEditForm(editConfig);
      form.setFieldsValue({ name: editConfig.currentName || '' });
    },
    [form]
  );

  // 关闭编辑弹窗
  const closeEditDialog = useCallback(() => {
    setEditForm(null);
    form.resetFields();
  }, [form]);

  // 提交编辑
  const handleSubmit = useCallback(async () => {
    if (!editForm) {
      return;
    }

    try {
      const values = await form.validateFields();
      const newName = values.name.trim();

      if (!newName) {
        Toast.show({ content: '名称不能为空' });
        return;
      }

      // 检查名称是否已存在
      if (editForm.mode === 'add') {
        if (editForm.type === 'category') {
          // 检查主区域名称是否重复
          const existingCategory = categories.find(
            (cat) => cat.zoneName === newName
          );
          if (existingCategory) {
            Toast.show({ content: '该区域名称已存在，请使用其他名称' });
            return;
          }
        } else {
          // 检查子区域名称是否重复（在同一主区域下）
          const parentCategory = categories.find(
            (cat) => cat.zoneId === editForm.categoryId
          );
          if (parentCategory) {
            const existingItem = parentCategory.children.find(
              (item) => item.zoneName === newName
            );
            if (existingItem) {
              Toast.show({ content: '该区域名称已存在，请使用其他名称' });
              return;
            }
          }
        }
      } else if (editForm.mode === 'edit') {
        // 编辑模式下也需要检查重复（排除自身）
        if (editForm.type === 'category') {
          const existingCategory = categories.find(
            (cat) =>
              cat.zoneName === newName && cat.zoneId !== editForm.categoryId
          );
          if (existingCategory) {
            Toast.show({ content: '该区域名称已存在，请使用其他名称' });
            return;
          }
        } else {
          const parentCategory = categories.find(
            (cat) => cat.zoneId === editForm.categoryId
          );
          if (parentCategory) {
            const existingItem = parentCategory.children.find(
              (item) =>
                item.zoneName === newName && item.zoneId !== editForm.itemId
            );
            if (existingItem) {
              Toast.show({ content: '该区域名称已存在，请使用其他名称' });
              return;
            }
          }
        }
      }

      let newCategories = [...categories];

      if (editForm.type === 'category') {
        if (editForm.mode === 'add') {
          // 添加新分类
          const { zoneId }: any = await createReportArea({
            zoneName: newName,
            parentId: '',
          });
          newCategories.unshift({
            zoneId,
            zoneName: newName,
            selected: false,
            parentId: '',
            children: [],
          });
        } else {
          // 编辑分类
          await updateReportArea(editForm.categoryId ?? '', {
            zoneName: newName,
          });
          newCategories = newCategories.map((cat) =>
            cat.zoneId === editForm.categoryId
              ? { ...cat, zoneName: newName }
              : cat
          );
        }
      } else {
        if (editForm.mode === 'add') {
          // 添加新子区域
          const { zoneId }: any = await createReportArea({
            zoneName: newName,
            parentId: editForm.categoryId,
            sort: 0,
          });
          newCategories = newCategories.map((cat) =>
            cat.zoneId === editForm.categoryId
              ? {
                  ...cat,
                  children: [
                    {
                      zoneId,
                      zoneName: newName,
                      selected: false,
                      parentId: cat.zoneId,
                      children: [],
                    },
                    ...cat.children,
                  ],
                }
              : cat
          );
        } else {
          // 编辑子区域
          await updateReportArea(editForm.itemId ?? '', {
            zoneName: newName,
            parentId: editForm.categoryId,
          });
          newCategories = newCategories.map((cat) =>
            cat.zoneId === editForm.categoryId
              ? {
                  ...cat,
                  children: cat.children.map((item) =>
                    item.zoneId === editForm.itemId
                      ? { ...item, zoneName: newName }
                      : item
                  ),
                }
              : cat
          );
        }
      }

      // 统一使用saveData方法更新状态，确保页面刷新
      saveData(newCategories);
      closeEditDialog();
      Toast.show({
        icon: 'success',
        content: editForm.mode === 'add' ? '添加成功' : '修改成功',
      });
    } catch (error) {
      console.error('提交失败:', error);
      Toast.show({
        icon: 'fail',
        content: '操作失败，请重试',
      });
    }
  }, [editForm, form, categories, saveData, closeEditDialog]);

  // 删除分类
  const deleteCategory = useCallback(
    (categoryId: string) => {
      const category = categories.find((cat) => cat.zoneId === categoryId);
      if (!category) {
        return;
      }

      if (category.children.length > 0) {
        Toast.show({
          content: '请先删除该分类下的所有子区域',
        });
        return;
      }

      Dialog.confirm({
        content: `确定要删除分类"${category.zoneName}"吗？`,
        onConfirm: () => {
          deleteReportArea(categoryId).then(() => {
            const newCategories = categories.filter(
              (cat) => cat.zoneId !== categoryId
            );
            saveData(newCategories);
            Toast.show({
              icon: 'success',
              content: '删除成功',
            });
          });
        },
      });
    },
    [categories, saveData]
  );

  // 删除子区域
  const deleteItem = useCallback(
    (categoryId: string, itemId: string) => {
      const category = categories.find((cat) => cat.zoneId === categoryId);
      const item = category?.children.find((i) => i.zoneId === itemId);
      if (!(category && item)) {
        return;
      }

      Dialog.confirm({
        content: `确定要删除区域"${item.zoneName}"吗？`,
        onConfirm: () => {
          deleteReportArea(itemId).then(() => {
            const newCategories = categories.map((cat) =>
              cat.zoneId === categoryId
                ? {
                    ...cat,
                    children: cat.children.filter((i) => i.zoneId !== itemId),
                  }
                : cat
            );
            saveData(newCategories);
            Toast.show({
              icon: 'success',
              content: '删除成功',
            });
          });
        },
      });
    },
    [categories, saveData]
  );

  // 渲染分类标题
  const renderCategoryHeader = (category: AreaCategory) => {
    const isExpanded = expandedKeys.includes(category.zoneId);

    return (
      <div className="flex items-center justify-between px-4 py-3">
        <button
          className="flex min-w-0 max-w-200 flex-1 items-center overflow-hidden"
          onClick={() => toggleExpanded(category.zoneId)}
          type="button"
        >
          {isExpanded ? (
            <ChevronDown className="mr-2 h-4 w-4 flex-shrink-0 text-gray-500" />
          ) : (
            <ChevronRight className="mr-2 h-4 w-4 flex-shrink-0 text-gray-500" />
          )}
          <span className="text-ellipsis break-words font-medium text-gray-800">
            {category.zoneName}
          </span>
          <span className="ml-2 flex-shrink-0 text-gray-500 text-sm">
            ({category.children.length})
          </span>
        </button>
        <div className="flex flex-shrink-0 gap-2">
          <Button
            fill="none"
            onClick={() =>
              openEditDialog({
                type: 'item',
                mode: 'add',
                categoryId: category.zoneId,
              })
            }
            size="small"
          >
            <Plus className="h-4 w-4" />
          </Button>
          <Button
            fill="none"
            onClick={() =>
              openEditDialog({
                type: 'category',
                mode: 'edit',
                categoryId: category.zoneId,
                currentName: category.zoneName,
              })
            }
            size="small"
          >
            <Edit3 className="h-4 w-4" />
          </Button>
          <Button
            color="danger"
            fill="none"
            onClick={() => deleteCategory(category.zoneId)}
            size="small"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </div>
    );
  };

  // 渲染子区域列表
  const renderItems = (category: AreaCategory) => {
    if (!expandedKeys.includes(category.zoneId)) {
      return null;
    }

    return (
      <div className="ml-4 bg-white">
        {category.children.map((item) => (
          <div
            className="flex items-center justify-between border-gray-100 border-b bg-white px-4 py-3 last:border-b-0"
            key={item.zoneId}
          >
            <div className="flex min-w-0 max-w-200 flex-1 items-center overflow-hidden">
              <span className="text-gray-800">{item.zoneName}</span>
            </div>
            <div className="flex flex-shrink-0 gap-2">
              <Button
                fill="none"
                onClick={() =>
                  openEditDialog({
                    type: 'item',
                    mode: 'edit',
                    categoryId: item.parentId,
                    itemId: item.zoneId,
                    currentName: item.zoneName,
                  })
                }
                size="small"
              >
                <Edit3 className="h-4 w-4" />
              </Button>
              <Button
                color="danger"
                fill="none"
                onClick={() => deleteItem(category.zoneId, item.zoneId)}
                size="small"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        ))}
        {category.children.length === 0 && (
          <div className="py-8 text-center text-gray-500 text-sm">
            暂无子区域
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-slate-50">
      <div className="p-4">
        <div className="mb-2 flex justify-end">
          <Button
            color="primary"
            fill="none"
            onClick={() =>
              openEditDialog({
                type: 'category',
                mode: 'add',
              })
            }
            size="small"
          >
            <Plus className="h-3 w-3" />
            添加主区域
          </Button>
        </div>
        <div className="overflow-hidden rounded-lg bg-white shadow-sm">
          {categories.map((category) => (
            <div
              className="border-gray-100 border-b last:border-b-0"
              key={category.zoneId}
            >
              {renderCategoryHeader(category)}
              {renderItems(category)}
            </div>
          ))}
        </div>

        {categories.length === 0 && (
          <div className="py-16 text-center text-gray-500">
            <p className="mb-4">暂无主区域</p>
            <Button
              color="primary"
              onClick={() =>
                openEditDialog({
                  type: 'category',
                  mode: 'add',
                })
              }
            >
              添加主区域
            </Button>
          </div>
        )}
      </div>

      {/* 编辑弹窗 */}
      {editForm && (
        <Dialog
          actions={[
            [
              {
                key: 'cancel',
                text: '取消',
                onClick: closeEditDialog,
              },
              {
                key: 'submit',
                text: '确定',
                bold: true,
                onClick: handleSubmit,
              },
            ],
          ]}
          closeOnAction={true}
          content={
            <Form form={form} layout="horizontal">
              <Form.Item
                label="名称"
                name="name"
                rules={[{ required: true, message: '请输入名称' }]}
              >
                <Input maxLength={32} placeholder="请输入名称" />
              </Form.Item>
            </Form>
          }
          title={(() => {
            if (editForm.type === 'category') {
              if (editForm.mode === 'add') {
                return '添加主区域';
              }
              return '编辑主区域';
            }
            if (editForm.mode === 'add') {
              return '添加子区域';
            }
            return '编辑子区域';
          })()}
          visible={true}
        />
      )}
    </div>
  );
};

export default AreaManagePage;
