/** biome-ignore-all lint/a11y/useSemanticElements: <explanation> */
'use client';

import { useRouter } from 'next/navigation';
import { FaPlay, FaRegClock } from 'react-icons/fa';
import Media from '../../detail/components/Media';
import type { RecordData } from '../mock/recordData';
import StudentsDisplay from './StudentsDisplay';

interface RecordItemProps {
  record: RecordData;
  studentId?: string;
}

export default function RecordItem({ record, studentId }: RecordItemProps) {
  const router = useRouter();

  const handleRecordClick = () => {
    if (record.isGenerating === 1) {
      return;
    }
    if (studentId) {
      router.push(
        `/pbl/record/detail/student?observationId=${record.observationId}&studentId=${studentId}`
      );
    } else {
      router.push(`/pbl/record/detail?observationId=${record.observationId}`);
    }
  };

  const getTagColor = (color: string) => {
    const colorMap: Record<string, string> = {
      violet: 'bg-violet-100 text-violet-600',
      blue: 'bg-blue-100 text-blue-600',
      purple: 'bg-purple-100 text-purple-600',
      yellow: 'bg-yellow-100 text-yellow-600',
      green: 'bg-green-100 text-green-600',
      red: 'bg-red-100 text-red-600',
      indigo: 'bg-indigo-100 text-indigo-600',
      teal: 'bg-teal-100 text-teal-600',
    };
    return colorMap[color] || 'bg-gray-100 text-gray-600';
  };

  return (
    <button
      className="mb-4 w-full overflow-hidden rounded-2xl bg-white text-left shadow-sm"
      onClick={handleRecordClick}
      type="button"
    >
      <div className="p-4">
        <Media media={record.medias || []} />

        {(record.type === 'text' || record.type === 'audio') && (
          <h3 className="mb-1 font-semibold text-lg">{record.title}</h3>
        )}

        <p className="mb-3 line-clamp-5 text-gray-700">{record.content}</p>

        {record.type === 'audio' && record.audio && (
          <div className="mb-3 flex items-center rounded-lg bg-yellow-50 p-3">
            <button
              className="flex h-10 w-10 items-center justify-center rounded-full bg-violet-500 text-white shadow-md transition-colors hover:bg-violet-600"
              type="button"
            >
              <FaPlay />
            </button>
            <div className="ml-3 flex-1">
              <div className="mb-1 flex justify-between text-gray-500 text-sm">
                <span>故事录音</span>
                <span>{record.audio.duration}</span>
              </div>
              <div className="h-1.5 w-full rounded-full bg-gray-200">
                <div
                  className="h-1.5 rounded-full bg-violet-500"
                  style={{ width: `${record.audio.progress * 100}%` }}
                />
              </div>
            </div>
          </div>
        )}
        {!studentId && <StudentsDisplay record={record} />}

        {record.tags && Array.isArray(record.tags) && (
          <div className="mb-4 flex flex-wrap gap-2">
            {record.tags.map((tag, _index) => (
              <div
                className={`rounded-full px-2 py-1 text-xs ${getTagColor(
                  tag.tagName
                )}`}
                key={`${tag.tagId}`}
              >
                #{tag.tagName}
              </div>
            ))}
          </div>
        )}

        <div className="flex items-center text-gray-600 text-sm">
          <div className="flex items-center">
            <picture>
              <img
                alt={record.createUser?.name}
                className="mr-1 h-6 w-6 rounded-full border-2 border-white object-cover"
                src={record.createUser?.avatar}
              />
            </picture>
            <span>{record.createUser?.name}</span>
          </div>
          <div className="mx-2">•</div>
          <div className="flex items-center">
            <FaRegClock className="mr-1 text-gray-500" />
            <span>{record.createTime}</span>
          </div>
        </div>
      </div>
    </button>
  );
}
