'use client';

import { ActionSheet } from 'antd-mobile';
import type { Action } from 'antd-mobile/es/components/action-sheet';
import { compare } from 'compare-versions';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { useCommonStore } from '@/store/useCommonStore';
import { navigationToNativePage } from '@/utils';

interface AddRecordButtonProps {
  extra?: string;
  cancelText?: string;
  buttonText?: string;
  icon?: React.ReactNode;
  className?: string;
  classId?: string;
  projectId?: string;
  children: (onClick: () => void) => React.ReactNode;
}

export default function AddRecordButton({
  extra = '请选择记录模式',
  cancelText = '取消',
  classId = '',
  projectId = '',
  children,
}: AddRecordButtonProps) {
  const version = useCommonStore((state) => state.version);
  const brand = useCommonStore((state) => state.brand);
  const router = useRouter();
  const [visible, setVisible] = useState(false);

  // 在组件内部定义固定的 actions
  const actions: Action[] = [
    {
      text: 'AI 自动生成观察记录',
      description: (
        <div className="text-gray-500 text-xs">
          上传视频，AI 自动生成观察记录
        </div>
      ),
      key: 'copy',
      onClick: () => {
        if (
          (brand === '1' &&
            compare(version, '1.33.0', '>') &&
            compare(version, '1.34.0', '<')) ||
          (brand === '2' &&
            compare(version, '6.21.2', '>=') &&
            compare(version, '6.22.0', '<'))
        ) {
          navigationToNativePage(
            `app://app/pbl/addMaterials?deptId=${classId}&videoCompressBitrate=10000000`
          );
          setVisible(false);
        } else {
          router.push(
            `/pbl/material/create?classId=${classId}${projectId ? `&projectId=${projectId}` : ''}`
          );
        }
      },
    },
    {
      text: '手动创建观察记录',
      key: 'edit',
      onClick: () => {
        router.push(
          `/pbl/record/create?classId=${classId}${projectId ? `&projectId=${projectId}` : ''}`
        );
      },
    },
  ];

  const handleClick = () => {
    setVisible(true);
  };

  return (
    <>
      {children(handleClick)}
      <ActionSheet
        actions={actions}
        cancelText={cancelText}
        extra={extra}
        onClose={() => setVisible(false)}
        visible={visible}
      />
    </>
  );
}
