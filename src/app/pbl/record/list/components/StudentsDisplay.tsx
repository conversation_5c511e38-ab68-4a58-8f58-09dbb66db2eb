'use client';

import { ChevronRight, X } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import type { RecordData } from '../mock/recordData';

interface StudentsDisplayProps {
  record: RecordData;
}

const StudentsDisplay = ({ record }: StudentsDisplayProps) => {
  const router = useRouter();
  const [showParticipants, setShowParticipants] = useState(false);

  const handleParticipantsClick = () => {
    setShowParticipants(true);
  };

  if (!record.students?.length) {
    return null;
  }

  return (
    <div className="mb-4 flex items-center space-x-2">
      <button
        className="flex cursor-pointer items-center"
        onClick={(e) => {
          e.stopPropagation();
          handleParticipantsClick();
        }}
        type="button"
      >
        <div className="-space-x-2 flex">
          {record.students.slice(0, 3).map((participant) => (
            <img
              alt={participant.name}
              className="h-8 w-8 rounded-full border-2 border-white object-cover"
              key={participant.id}
              src={participant.avatar}
            />
          ))}
          {record.students.length > 3 && (
            <div className="flex h-8 w-8 items-center justify-center rounded-full border-2 border-white bg-violet-100">
              <span className="text-violet-600 text-xs">
                +{record.students.length - 3}
              </span>
            </div>
          )}
        </div>
        <span className="ml-2 text-gray-500 text-sm">
          {record.students.length}位学生参与
        </span>
        <ChevronRight className="ml-1 h-4 w-4 text-gray-400" />
      </button>
      {showParticipants && (
        <button
          className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
          onClick={(e) => {
            e.stopPropagation();
            setShowParticipants(false);
          }}
          type="button"
        >
          <button
            className="max-h-[80vh] w-80 overflow-hidden rounded-lg bg-white p-4"
            onClick={(e) => e.stopPropagation()}
            type="button"
          >
            <div className="mb-4 flex items-center justify-between border-gray-100 border-b">
              <div className="font-medium">参与学生</div>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setShowParticipants(false);
                }}
                type="button"
              >
                <X className="h-5 w-5 text-gray-400" />
              </button>
            </div>
            <div className="overflow-y-auto">
              {record?.students?.map((participant) => (
                <button
                  className="mb-4 flex w-full items-center justify-between"
                  key={participant.id}
                  onClick={(e) => {
                    e.stopPropagation();
                    router.push(
                      `/pbl/record/detail/student?observationId=${record.observationId}&studentId=${participant.id}`
                    );
                    setShowParticipants(false);
                  }}
                  type="button"
                >
                  <div className="flex items-center">
                    <img
                      alt={participant.name}
                      className="h-10 w-10 rounded-full object-cover"
                      src={participant.avatar}
                    />
                    <span className="ml-3">{participant.name}</span>
                  </div>
                  <ChevronRight className="ml-1 h-6 w-6 text-gray-400" />
                </button>
              ))}
            </div>
          </button>
        </button>
      )}
    </div>
  );
};

export default StudentsDisplay;
