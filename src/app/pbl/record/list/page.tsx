'use client';

import { useInfiniteQuery } from '@tanstack/react-query';
import { InfiniteScroll } from 'antd-mobile';
import { format } from 'date-fns';
import { useAtom } from 'jotai';
import { Calendar } from 'lucide-react';
import { useSearchParams } from 'next/navigation';
import { useEffect, useMemo, useState } from 'react';

import { getObservationList } from '@/api/pbl';
import {
  currentSelectedRegionsAtom,
  currentTimeRangeAtom,
} from '@/store/timeZoneSelectorAtoms';
import RegionSelector from '../components/RegionSelector';
import TimeSelector, { type TimeRange } from '../components/TimeSelector';

import RecordItem from './components/RecordItem';
import type { RecordData } from './mock/recordData';

const pageSize = 10;

/*
 根据date和 studentID 判断两种显示方式
*/
export default function App() {
  const searchParams = useSearchParams();
  const studentId = searchParams?.get('studentId');
  const studentName = searchParams?.get('studentName');
  const date = searchParams?.get('date');
  const observationIds = searchParams?.get('observationIds') || '';
  const weekday = searchParams?.get('weekday');
  const [total, setTotal] = useState(0);

  // 使用全局时间状态
  const [currentTimeRange] = useAtom(currentTimeRangeAtom);

  // 使用全局区域状态
  const [selectedRegions] = useAtom(currentSelectedRegionsAtom);

  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = '观察记录';
    }
  }, []);

  // 处理时间选择确认
  const handleTimeConfirm = (timeRange?: TimeRange) => {
    // 立即触发数据重新获取
    refetchObservations();
    console.log('选择的时间段:', timeRange);
  };

  // 处理区域选择确认
  const handleRegionConfirm = () => {
    // 立即触发数据重新获取
    refetchObservations();
    console.log('选择的区域:', selectedRegions);
  };

  // 构建 API 请求参数
  const buildApiParams = useMemo(() => {
    const params: { observeDate?: string[]; zoneId?: string[] } = {};

    if (currentTimeRange) {
      // 检查时间格式，如果已经包含时间部分则直接使用，否则添加时间部分
      const formatTimeString = (timeStr: string, isEndTime = false) => {
        if (timeStr.includes(' ')) {
          // 已经包含时间部分，直接返回
          return timeStr;
        }
        // 只有日期部分，添加时间
        return isEndTime ? `${timeStr} 23:59:59` : `${timeStr} 00:00:00`;
      };

      params.observeDate = [
        formatTimeString(currentTimeRange.startTime),
        formatTimeString(currentTimeRange.endTime, true),
      ];
    }

    if (selectedRegions && selectedRegions.length > 0) {
      params.zoneId = selectedRegions.map((item) => item.zoneId);
    } else {
      params.zoneId = undefined;
    }

    return params;
  }, [currentTimeRange, selectedRegions]);

  const {
    data: infiniteObservationData,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading: isLoadingObservations,
    refetch: refetchObservations,
  } = useInfiniteQuery({
    queryKey: ['observationList', studentId, date, buildApiParams],
    queryFn: async ({ pageParam = 1 }) => {
      const params: {
        studentId?: string;
        date?: string[];
        page: number;
        perPage: number;
        observationIds?: string[];
        observeDate?: string[];
        zoneId?: string[];
      } = {
        page: pageParam,
        perPage: pageSize,
        ...buildApiParams,
      };
      if (studentId) {
        params.studentId = studentId;
      }
      if (date) {
        try {
          params.observationIds = JSON.parse(observationIds || '[]');
        } catch (error) {
          console.error('Failed to parse observationIds:', error);
          params.observationIds = [];
        }
        params.date = [date];
      }
      const response = await getObservationList(params);

      // 确保返回完整的分页信息，包括数据和分页状态
      // 以安全的方式访问返回数据
      const responseData = response as unknown;
      let records: RecordData[] = [];

      if (Array.isArray(responseData)) {
        records = responseData as RecordData[];
      } else {
        const typedResponse = responseData as { list?: RecordData[] };
        if (Array.isArray(typedResponse.list)) {
          records = typedResponse.list;
        }
      }

      setTotal((responseData as { total?: number }).total || 0);
      return {
        data: records,
        page: pageParam,
        hasMore: records.length >= pageSize,
      };
    },
    getNextPageParam: (lastPage) => {
      // 如果没有更多数据，返回 undefined 表示不需要加载下一页
      if (
        lastPage.hasMore === false ||
        !lastPage.data ||
        lastPage.data.length < pageSize
      ) {
        return;
      }
      return lastPage.page + 1;
    },
    enabled: !!studentId || !!date,
  });

  // 从 infiniteObservationData 中提取记录数据
  const recordsList = infiniteObservationData?.pages
    ? (infiniteObservationData.pages.flatMap((page) => {
        if (page) {
          return page.data || [];
        }
        return [];
      }) as RecordData[])
    : [];

  // 加载更多数据的处理函数
  const loadMore = async () => {
    if (hasNextPage && !isFetchingNextPage) {
      await fetchNextPage();
    }
  };

  if (isLoadingObservations) {
    return (
      <div className="py-10 text-center">
        <div className="text-gray-500 text-sm">加载中...</div>
      </div>
    );
  }

  return (
    <main className=" min-h-screen bg-slate-50">
      {/* 时间和区域选择器 */}
      <div className="sticky top-0 z-10 mb-2 bg-white p-4">
        <div className=" flex items-center justify-between">
          <TimeSelector
            className="cursor-pointer"
            onConfirm={handleTimeConfirm}
          />

          <RegionSelector
            className="cursor-pointer"
            onConfirm={handleRegionConfirm}
            placeholder="全部区域"
          />
        </div>
      </div>

      <div className="mb-4 px-4 pt-2 font-semibold text-base">
        {!!studentId && (
          <div className="flex items-center justify-between">
            <div className="flex flex-1 items-center">
              {studentName}的观察记录
            </div>
            <div className="flex items-center justify-end font-normal text-gray-500 text-sm">
              <span className="">{total} 条记录</span>
            </div>
          </div>
        )}
        {!!date && (
          <div className="flex items-center justify-between">
            <div className="flex flex-1 items-center">
              <Calendar className="mr-2 size-4 text-gray-500" />
              {format(new Date(date), 'yyyy-MM-dd')} 观察记录
            </div>
            <div className="flex items-center justify-end font-normal text-gray-600 text-sm">
              <span>{weekday}</span>
              <span className="mx-2">•</span>
              <span className="">{total} 条记录</span>
            </div>
          </div>
        )}
      </div>
      <div className="flex flex-1 flex-col overflow-y-auto px-4 py-2">
        {recordsList.length === 0 ? (
          <div className="flex flex-1 items-center justify-center py-10 text-center">
            暂无观察记录
          </div>
        ) : (
          <div className="mb-12">
            {/* 这里传递实际的记录数据给 Records 组件 */}
            {recordsList.map((record) => (
              <div key={record.observationId}>
                <RecordItem
                  record={record}
                  studentId={studentId || undefined}
                />
              </div>
            ))}
            {/* 使用 InfiniteScroll 组件 */}
            <InfiniteScroll
              className="!py-2"
              hasMore={!!hasNextPage}
              loadMore={loadMore}
              threshold={250}
            >
              {isFetchingNextPage && (
                <div className="py-3 text-center">
                  <span className="text-gray-500 text-sm">加载更多数据...</span>
                </div>
              )}
              {!isFetchingNextPage && hasNextPage && (
                <div className="py-3 text-center">
                  <span className="text-gray-500 text-sm">上拉加载更多</span>
                </div>
              )}
              {!(isFetchingNextPage || hasNextPage) && (
                <div className="py-3 text-center">
                  <span className="text-gray-500 text-sm">没有更多数据了</span>
                </div>
              )}
            </InfiniteScroll>
          </div>
        )}
      </div>
    </main>
  );
}
