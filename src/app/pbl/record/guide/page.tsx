'use client';

import { motion } from 'framer-motion';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  BookOpen,
  Brain,
  Camera,
  ChevronRight,
  ClipboardList,
  Compass,
  Eye,
  FileText,
  Layers,
  Lightbulb,
  Microscope,
  Pencil,
  Shield,
  Sparkles,
  Target,
} from 'lucide-react';
import { useEffect, useState } from 'react';

// 动画变体
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      type: 'spring',
      stiffness: 100,
      damping: 12,
    },
  },
};

export default function Guide() {
  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = '幼儿观察记录指南';
    }
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-b from-blue-50 to-white">
      <div className="mx-auto max-w-4xl overflow-hidden rounded-lg bg-white shadow-xl">
        <div className="relative overflow-hidden bg-gradient-to-r from-blue-600 to-blue-500 px-8 py-8">
          <div className="-mt-20 -mr-20 absolute top-0 right-0 h-64 w-64 rounded-full bg-blue-400 opacity-20" />
          <div className="-mb-20 -ml-20 absolute bottom-0 left-0 h-40 w-40 rounded-full bg-blue-400 opacity-20" />
          <motion.div
            animate={{ y: 0, opacity: 1 }}
            className="relative z-10 flex items-center justify-center"
            initial={{ y: -20, opacity: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Eye className="mr-3 h-8 w-8 text-white" />
            <h1 className="text-center font-bold text-2xl text-white">
              幼儿观察记录指南
            </h1>
          </motion.div>
        </div>

        <motion.div
          animate="visible"
          className="p-4 sm:p-10"
          initial="hidden"
          variants={containerVariants}
        >
          {/* 第一部分 */}
          <motion.section className="mb-10" variants={itemVariants}>
            <h2 className="mb-6 flex cursor-pointer items-center border-blue-200 border-b-2 pb-2 font-bold text-blue-800 text-xl transition-colors hover:text-blue-600">
              <span className="mr-3 flex h-10 w-10 items-center justify-center rounded-full bg-gradient-to-r from-blue-600 to-blue-500 text-white shadow-md">
                <BookOpen className="h-5 w-5" />
              </span>
              观察前的理论准备
            </h2>

            <motion.div
              className="space-y-6"
              initial={{ height: 'auto' }}
              transition={{ duration: 0.3 }}
            >
              <motion.div
                className="rounded-lg border border-blue-100 bg-blue-50 p-6 shadow-sm transition-shadow hover:shadow-md"
                transition={{ type: 'spring', stiffness: 300 }}
                whileHover={{ y: -5 }}
              >
                <h3 className="mb-4 flex items-center font-semibold text-blue-700 text-xl">
                  <Brain className="mr-2 h-5 w-5 text-blue-500" />
                  1. 三维目标解码
                </h3>
                <ul className="ml-6 list-disc space-y-2 text-gray-700">
                  <li>
                    熟记《指南》五大领域（健康、语言、社会、科学、艺术）的 11
                    个子领域、32 条目标体系
                  </li>
                  <li>掌握各年龄段典型行为表现（3-4/4-5/5-6 岁）的梯度差异</li>
                  <li>绘制"领域 - 目标 - 发展阶梯"对照表，形成快速检索工具</li>
                </ul>
              </motion.div>

              <motion.div
                className="rounded-lg border border-blue-100 bg-blue-50 p-6 shadow-sm transition-shadow hover:shadow-md"
                transition={{ type: 'spring', stiffness: 300 }}
                whileHover={{ y: -5 }}
              >
                <h3 className="mb-4 flex items-center font-semibold text-blue-700 text-xl">
                  <Compass className="mr-2 h-5 w-5 text-blue-500" />
                  2. 观察工具包开发
                </h3>
                <p className="mb-2 text-gray-600 italic">
                  设计模块化观察量表：
                </p>
                <ul className="ml-6 list-disc space-y-2 text-gray-700">
                  <li>动作发展检核表（大肌肉/精细动作里程碑）</li>
                  <li>社会性互动观察矩阵（合作/冲突解决/同理心指标）</li>
                  <li>学习品质评估框架（坚持性/好奇心/问题解决策略）</li>
                </ul>
              </motion.div>
            </motion.div>
          </motion.section>

          {/* 第二部分 */}
          <motion.section className="mb-10" variants={itemVariants}>
            <h2 className="mb-6 flex cursor-pointer items-center border-blue-200 border-b-2 pb-2 font-bold text-blue-800 text-xl transition-colors hover:text-blue-600">
              <span className="mr-3 flex h-10 w-10 items-center justify-center rounded-full bg-gradient-to-r from-teal-600 to-teal-500 text-white shadow-md">
                <Microscope className="h-5 w-5" />
              </span>
              结构化观察流程
            </h2>

            <motion.div
              className="space-y-6"
              initial={{ height: 'auto' }}
              transition={{ duration: 0.3 }}
            >
              <motion.div
                className="rounded-lg border border-teal-100 bg-teal-50 p-6 shadow-sm transition-shadow hover:shadow-md"
                transition={{ type: 'spring', stiffness: 300 }}
                whileHover={{ y: -5 }}
              >
                <h3 className="mb-4 flex items-center font-semibold text-teal-700 text-xl">
                  <Target className="mr-2 h-5 w-5 text-teal-500" />
                  1. 目标导向式观察
                </h3>
                <ul className="ml-6 list-disc space-y-2 text-gray-700">
                  <li>
                    周主题观察：根据教学主题选择对应领域（如建构区活动聚焦"科学探究"目标）
                  </li>
                  <li>个案追踪观察：针对特殊需求儿童制定个性化观察方案</li>
                </ul>
              </motion.div>

              <motion.div
                className="rounded-lg border border-teal-100 bg-teal-50 p-6 shadow-sm transition-shadow hover:shadow-md"
                transition={{ type: 'spring', stiffness: 300 }}
                whileHover={{ y: -5 }}
              >
                <h3 className="mb-4 flex items-center font-semibold text-teal-700 text-xl">
                  <Camera className="mr-2 h-5 w-5 text-teal-500" />
                  2. 多模态记录技术
                </h3>
                <p className="mb-2 text-gray-600 italic">
                  采用"STAR-R"记录法：
                </p>
                <ul className="ml-6 list-disc space-y-2 text-gray-700">
                  <li>
                    <span className="font-semibold">S</span>ituation
                    （情境）：标注时间/地点/参与者
                  </li>
                  <li>
                    <span className="font-semibold">T</span>ask
                    （任务）：活动性质与材料说明
                  </li>
                  <li>
                    <span className="font-semibold">A</span>
                    ction（行为）：可量化的具体行为描述（如"连续抛接球 7 次"）
                  </li>
                  <li>
                    <span className="font-semibold">R</span>
                    esponse（反应）：同伴/教师的互动反馈
                  </li>
                  <li>
                    <span className="font-semibold">R</span>
                    eference（参照）：链接《指南》具体条目（如"健康领域 -
                    动作发展 - 目标 1"）
                  </li>
                </ul>
              </motion.div>
            </motion.div>
          </motion.section>

          {/* 第三部分 */}
          <motion.section className="mb-10" variants={itemVariants}>
            <h2 className="mb-6 flex cursor-pointer items-center border-blue-200 border-b-2 pb-2 font-bold text-blue-800 text-xl transition-colors hover:text-blue-600">
              <span className="mr-3 flex h-10 w-10 items-center justify-center rounded-full bg-gradient-to-r from-purple-600 to-purple-500 text-white shadow-md">
                <BarChart3 className="h-5 w-5" />
              </span>
              数据分析与运用
            </h2>

            <motion.div
              className="space-y-6"
              initial={{ height: 'auto' }}
              transition={{ duration: 0.3 }}
            >
              <motion.div
                className="rounded-lg border border-purple-100 bg-purple-50 p-6 shadow-sm transition-shadow hover:shadow-md"
                transition={{ type: 'spring', stiffness: 300 }}
                whileHover={{ y: -5 }}
              >
                <h3 className="mb-4 flex items-center font-semibold text-purple-700 text-xl">
                  <Layers className="mr-2 h-5 w-5 text-purple-500" />
                  1. 发展轨迹图谱构建
                </h3>
                <ul className="ml-6 list-disc space-y-2 text-gray-700">
                  <li>
                    建立儿童个人发展坐标系，横轴为时间单位，纵轴为《指南》目标维度
                  </li>
                  <li>
                    使用符号系统标注发展节点（▲突破性发展 ●稳定表现 ■待加强）
                  </li>
                </ul>
              </motion.div>

              <motion.div
                className="rounded-lg border border-purple-100 bg-purple-50 p-6 shadow-sm transition-shadow hover:shadow-md"
                transition={{ type: 'spring', stiffness: 300 }}
                whileHover={{ y: -5 }}
              >
                <h3 className="mb-4 flex items-center font-semibold text-purple-700 text-xl">
                  <Lightbulb className="mr-2 h-5 w-5 text-purple-500" />
                  2. 教学调整策略生成
                </h3>
                <p className="mb-2 text-gray-600 italic">
                  基于数据的三级干预：
                </p>
                <ul className="ml-6 list-disc space-y-2 text-gray-700">
                  <li>群体发展水平→调整课程难度梯度</li>
                  <li>领域发展失衡→设计补偿性活动</li>
                  <li>个体特殊需求→制定个别化教育计划</li>
                </ul>
              </motion.div>
            </motion.div>
          </motion.section>

          {/* 第四部分 */}

          <motion.section className="mb-10" variants={itemVariants}>
            <h2 className="mb-6 flex cursor-pointer items-center border-blue-200 border-b-2 pb-2 font-bold text-blue-800 text-xl transition-colors hover:text-blue-600">
              <span className="mr-3 flex h-10 w-10 items-center justify-center rounded-full bg-gradient-to-r from-amber-600 to-amber-500 text-white shadow-md">
                <Shield className="h-5 w-5" />
              </span>
              伦理实践要点
            </h2>

            <motion.div
              className="space-y-4 rounded-lg border border-amber-100 bg-amber-50 p-6 shadow-sm transition-shadow hover:shadow-md"
              initial={{ height: 'auto' }}
              transition={{ duration: 0.3 }}
              whileHover={{ y: -5 }}
            >
              <ol className="ml-6 list-decimal space-y-3 text-gray-700 marker:font-bold marker:text-amber-500">
                <li>观察记录应遵循"三隐原则"：隐形在场、隐私保护、隐性指导</li>
                <li>
                  避免"标签化"解读，强调发展性评价（如用"正在发展 xx
                  能力"替代"xx 能力不足"）
                </li>
                <li>
                  构建"观察 - 记录 - 解读 -
                  支持"的完整闭环，确保每个观察数据都转化为教育行动
                </li>
              </ol>
            </motion.div>
          </motion.section>

          {/* 示例应用 */}
          <motion.section variants={itemVariants}>
            <h2 className="mb-6 flex items-center border-blue-200 border-b-2 pb-2 font-bold text-blue-800 text-xl">
              <span className="mr-3 flex h-10 w-10 items-center justify-center rounded-full bg-gradient-to-r from-green-600 to-green-500 text-white shadow-md">
                <FileText className="h-5 w-5" />
              </span>
              示例应用
            </h2>

            <motion.div
              className="rounded-lg border border-green-200 bg-green-50 p-6 shadow-md transition-all hover:shadow-lg"
              transition={{ type: 'spring', stiffness: 300 }}
              whileHover={{ scale: 1.02 }}
            >
              <p className="text-gray-700 leading-relaxed">
                在积木区观察到中班幼儿 A
                反复尝试搭建斜坡轨道失败后，改用纸板做支撑面。记录应呈现：
              </p>
              <motion.div
                animate={{ opacity: 1, y: 0 }}
                className="mt-4 rounded-lg border-green-500 border-l-4 bg-white p-6 text-gray-700 shadow-sm"
                initial={{ opacity: 0, y: 20 }}
                transition={{ delay: 0.5 }}
              >
                "5.12 建构区，幼儿 A 使用单元积木搭建斜坡（目标：科学探究
                2.1），经历 3
                次坍塌后，自主选取美工区废弃纸板铺设基底（问题解决策略）。持续专注
                21 分钟完成可滑动轨道（学习品质 1.1），邀请同伴 B
                测试小车速度（社会交往 2.1）。"
              </motion.div>

              <motion.div
                animate={{ opacity: 1 }}
                className="mt-6 flex justify-end"
                initial={{ opacity: 0 }}
                transition={{ delay: 0.8 }}
              >
                <div className="inline-flex items-center font-medium text-green-600 text-sm">
                  <Sparkles className="mr-1 h-4 w-4" />
                  <span>根据观察记录制定个性化教学计划</span>
                </div>
              </motion.div>
            </motion.div>
          </motion.section>
        </motion.div>
      </div>
    </div>
  );
}
