'use client';

import { Date<PERSON>icker, Picker, Toast } from 'antd-mobile';
import clsx from 'clsx';
import { format } from 'date-fns';
import { useAtom } from 'jotai';
import { Calendar, Loader, Save } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { createRef, useEffect, useRef, useState } from 'react';
import { useImmer } from 'use-immer';
import {
  createObservation,
  getProjectList,
  getReportAreas,
  getReportTags,
} from '@/api/pbl';
import StudentPicker from '@/components/StudentPicker';
import Upload, { type FileType, type UploadRef } from '@/components/UploadFile';
import { evaluationAtom } from '@/store/pbl';
import type { RegionData } from '../../material/create/components/RegionSelector';
import RegionSelector from '../../material/create/components/RegionSelector';
import SelectField from '../../material/create/components/SelectField';
import type { TagOption } from '../components/TagSelector';
import TagSelector from '../components/TagSelector';
import Evaluation, { type EvaluationRef } from './components/Evaluation';
import ObservationInput from './components/ObservationInput';

interface Student {
  studentId: string;
  studentName: string;
  avatar: string;
  gender: number;
  classId?: string;
  abilities: Ability[];
}

interface MediaItem {
  type: 1 | 2;
  url: string;
  fileSize: number;
  cover: string;
  duration: number;
  videoPlayType: number;
}

interface Ability {
  id?: string;
  name?: string;
  icon?: React.ComponentType<{ className?: string }>;
  color?: 'blue' | 'purple' | 'green' | 'yellow' | 'red';
  abilityId?: string | number;
}

interface StudentEvaluation {
  studentId: string;
  studentName?: string;
  avatar?: string;
  gender?: number;
  deptId?: string;
  abilities: Ability[];
}

interface FormData {
  title: string;
  content: string;
  type: number;
  source: number;
  date: string;
  deptId: string;
  projectId: string;
  projectName: string;
  medias: MediaItem[];
  studentEvaluations: StudentEvaluation[];
  tagIds: string[];
  zoneId: string;
}

export default function App() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const classId = searchParams?.get('classId');
  const projectId = searchParams?.get('projectId') || '';
  const [formData, setFormData] = useImmer<FormData>({
    title: '',
    content: '',
    type: 1,
    source: 2,
    deptId: classId || '',
    projectId: projectId || '',
    projectName: '',
    medias: [],
    date: format(new Date(), 'yyyy-MM-dd'),
    studentEvaluations: [],
    tagIds: [],
    zoneId: '',
  });

  const [evaluationRefs, setEvaluationRefs] = useState<
    Record<string, React.RefObject<EvaluationRef>>
  >({});
  const [selectedStudents, setSelectedStudents] = useState<Student[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [datePickerVisible, setDatePickerVisible] = useState(false);
  const [evaluations, setEvaluation] = useAtom(evaluationAtom);
  const [projectList, setProjectList] = useState<
    Array<{ projectId: string; projectName: string; deptId: string }>
  >([]);
  const [projectPickerVisible, setProjectPickerVisible] = useState(false);
  const [regionPopupVisible, setRegionPopupVisible] = useState(false);
  const [tagPopupVisible, setTagPopupVisible] = useState(false);
  const [tagList, setTagList] = useState<TagOption[]>([]);
  const [reportAreaList, setReportAreaList] = useState<RegionData[]>([]);
  const [regionName, setRegionName] = useState('');
  const [selectedTags, setSelectedTags] = useState<TagOption[]>([]);
  const [changeTags, setChangeTags] = useState<TagOption[]>([]);

  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = '创建观察记录';
    }
    // 清空 atom 数据
    setEvaluation({});
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [setEvaluation]);

  useEffect(() => {
    fetchProjectList();
    fetchReportAreas();
    fetchTagList();
  }, []);

  useEffect(() => {
    // 当学生列表变化时，为每个学生创建对应的 ref
    const newRefs: Record<string, React.RefObject<EvaluationRef>> = {};
    for (const student of formData.studentEvaluations) {
      newRefs[student.studentId] = createRef<EvaluationRef>();
    }
    setEvaluationRefs(newRefs);
  }, [formData.studentEvaluations]);

  const fetchProjectList = async () => {
    const response = await getProjectList({});
    // @ts-expect-error
    const data = response.list || [];

    setProjectList(
      data.filter((item: { deptId: string }) => item.deptId === classId)
    );
  };

  // 自动选中传入的 projectId 对应的项目
  useEffect(() => {
    if (projectId && projectList.length > 0) {
      const targetProject = projectList.find(
        (item) => item.projectId === projectId
      );
      if (targetProject) {
        setFormData((draft) => {
          draft.projectId = targetProject.projectId;
          draft.projectName = targetProject.projectName;
        });
      }
    }
  }, [projectId, projectList, setFormData]);
  const fetchReportAreas = async () => {
    const response = await getReportAreas();
    // @ts-expect-error
    const data = response.list;
    setReportAreaList(data || []);
  };

  const fetchTagList = async () => {
    const response = await getReportTags();
    // @ts-expect-error
    const data = response.list;

    setTagList(data || []);
  };

  const handleSubmit = () => {
    // 检查所有文件是否已上传完成
    if (uploadRef.current && !uploadRef.current.isAllUploaded()) {
      Toast.show({
        icon: 'fail',
        content: '请等待所有文件上传完成',
      });
      return;
    }

    const currentFormData = JSON.parse(JSON.stringify(formData));

    // 转换评估数据为提交格式
    // currentFormData.studentEvaluations = Object.entries(allStudentEvaluations)
    //   .filter(([_, abilities]) => {
    //     // 检查是否有至少一个值为 true 的能力
    //     return Object.values(abilities).some((value) => value === true);
    //   })
    //   .map(([studentId, abilities]) => {
    //     // 只保留值为 true 的 ability
    //     const filteredAbilities = Object.entries(abilities)
    //       .filter(([_, value]) => value === true)
    //       .map(([abilityId]) => ({
    //         abilityId: Number.parseInt(abilityId)
    //       }));

    //     return {
    //       studentId,
    //       deptId: classId,
    //       abilities: filteredAbilities
    //     };
    //   });

    currentFormData.studentEvaluations = Object.entries(evaluations)
      .map(([studentId, abilitiesObj]) => {
        // 只保留 abilityId 为 34 并且为 true 的
        const abilities = Object.entries(abilitiesObj)
          .filter(([_, value]) => value === true)
          .map(([abilityId]) => ({ abilityId: Number(abilityId) }));

        // 如果 abilities 为空则不返回
        if (abilities.length === 0) {
          return null;
        }

        return {
          studentId,
          deptId: classId,
          abilities,
        };
      })
      .filter(Boolean); // 去除空项

    // 输出处理后的数据以便调试
    console.log('处理后的学生评估数据：', currentFormData.studentEvaluations);

    if (uploadRef.current) {
      const files = uploadRef.current.getFiles();
      console.log('当前文件列表：', files);
      currentFormData.medias = files.map((item) => ({
        ...item,
        cover:
          item.type === 'video'
            ? `${item.url}?x-workflow-graph-name=video-thumbnail`
            : '',
        name: item.name || '未命名文件',
        fileSize: item.size || 0,
        type: (() => {
          if (item.type === 'image') {
            return 1;
          }
          if (item.type === 'video') {
            return 2;
          }
          if (item.type === 'audio') {
            return 3;
          }
          return 0;
        })(),
      }));
    }

    // 使用最新的数据进行后续操作
    if (
      !(
        currentFormData.content &&
        currentFormData.date &&
        currentFormData.studentEvaluations.length
      )
    ) {
      Toast.show({
        icon: 'fail',
        content: '请填写完整信息',
      });
      return;
    }
    setIsSubmitting(true);

    createObservation(currentFormData)
      .then((res) => {
        console.log(res);
        Toast.show({
          icon: 'success',
          content: '保存成功',
        });
        router.back();
      })
      .finally(() => {
        setIsSubmitting(false);
      });
  };

  // 定义 StudentPicker 返回的学生类型 (不含 abilities)
  type PickerStudent = Omit<Student, 'abilities'>;

  // 调整 handleStudentsSelect 的参数类型
  const handleStudentsSelect = (students: PickerStudent[]) => {
    // 将 PickerStudent[] 映射为 Student[]，添加空的 abilities
    const fullStudents: Student[] = students.map((s) => ({
      ...s,
      abilities: [], // 添加空的 abilities 数组
    }));
    setSelectedStudents(fullStudents); // 使用映射后的完整类型

    setFormData((draft) => {
      // draft.studentEvaluations 需要 studentId, studentName, avatar 等
      draft.studentEvaluations = students.map((student: PickerStudent) => ({
        studentId: student.studentId,
        deptId: student.classId,
        studentName: student.studentName,
        avatar: student.avatar, // 确保 avatar 存在
        gender: student.gender, // 确保 gender 存在
        abilities: [], // 初始化为空数组
      }));
    });
  };
  // Removed duplicate lines 218-222

  const uploadRef = useRef<UploadRef>(null);

  return (
    <main className="">
      <div className="p-4 pb-0">
        <StudentPicker
          // onMultiSelect 现在接收 PickerStudent[] 类型
          classId={classId ?? undefined}
          // multiValue 可能也需要调整类型，暂时保持不变
          multiple={true}
          multiValue={selectedStudents}
          onMultiSelect={handleStudentsSelect}
          placeholder="请选择班级和学生"
        />
      </div>
      <div className="flex-1 overflow-y-auto p-4">
        <div className="fade-in">
          <div className="mb-0">
            <label
              className="mb-2 block font-medium text-gray-700 text-sm"
              htmlFor="observation"
            >
              观察记录描述
            </label>
            <ObservationInput
              onChange={(content) =>
                setFormData((draft) => {
                  draft.content = content;
                })
              }
              value={formData.content}
            />
          </div>
          <div className="mb-4">
            <label
              className="mb-1 block font-medium text-gray-700 text-sm"
              htmlFor="date"
            >
              记录日期
            </label>
            {/** biome-ignore lint/a11y/useSemanticElements: <explanation> */}
            <div
              className="flex w-full items-center justify-between rounded-md border border-gray-300 p-2 text-gray-700"
              id="date"
              onClick={() => setDatePickerVisible(true)}
              onKeyDown={() => null}
              role="button"
              tabIndex={0}
            >
              {formData.date}
              <Calendar className="h-4 w-4" />
            </div>
          </div>
        </div>
        {projectList.length > 0 && (
          <div className="mb-4">
            <SelectField
              label="关联 PBL 项目"
              onClick={() => setProjectPickerVisible(true)}
              placeholder="请选择 PBL 项目"
              value={formData.projectName || ''}
            />
          </div>
        )}
        <div className="fade-in mb-4">
          <div className="mb-3 font-medium text-gray-700 text-sm">
            关联能力
            <span className="ml-2 text-gray-500 text-xs">
              (每个学生可单独选择)
            </span>
          </div>
          {formData.studentEvaluations.length === 0 ? (
            <div className="py-4 text-center text-gray-500">请先选择学生</div>
          ) : (
            <div className="space-y-6">
              {formData.studentEvaluations.map((child, index) => (
                <div
                  className="rounded-lg border p-3"
                  key={child.studentId || index}
                >
                  <div className="mb-3 flex items-center">
                    <picture>
                      <img
                        alt={child.studentName}
                        className="mr-2 h-8 w-8 rounded-full"
                        src={child.avatar}
                      />
                    </picture>
                    <span className="font-medium">{child.studentName}</span>
                    {/* <span className="ml-2 text-sm text-gray-500">
                      ({getChildAbilityCount(index)}/{abilityList.length})
                    </span> */}
                  </div>
                  {/* 传递 studentId 给 Evaluation 组件 */}
                  <Evaluation
                    ref={evaluationRefs[child.studentId]}
                    studentId={child.studentId}
                  />
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="fade-in mb-4">
          <label
            className="mb-1 block font-medium text-gray-700 text-sm"
            htmlFor="observation"
          >
            关联文件
          </label>
          <Upload
            className="min-h-[400px]"
            initialFiles={formData.medias as unknown as FileType[]}
            ref={uploadRef}
          />
        </div>

        {/* 区域选择 */}
        <SelectField
          className="mb-4"
          label="观察地点"
          onClick={() => setRegionPopupVisible(true)}
          placeholder="请选择"
          value={regionName || ''}
        />

        {/* 标签选择 */}
        <SelectField
          label="内容标签"
          onClick={() => {
            setChangeTags(selectedTags || []);
            setTagPopupVisible(true);
          }}
          placeholder="请选择标签"
          value={
            tagPopupVisible
              ? changeTags.map((tag) => tag.tagName).join(', ') || ''
              : selectedTags.map((tag) => tag.tagName).join(', ') || ''
          }
        />
      </div>

      <div className="border-gray-200 border-t bg-white p-4">
        <button
          className={clsx(
            'flex w-full items-center justify-center gap-2 rounded-md py-3',
            isSubmitting ||
              !formData.content ||
              (uploadRef.current ? !uploadRef.current.isAllUploaded() : false)
              ? 'cursor-not-allowed bg-gray-300 text-gray-500'
              : 'bg-indigo-500 text-white hover:bg-primary/90'
          )}
          disabled={
            isSubmitting ||
            !formData.content ||
            (uploadRef.current ? !uploadRef.current.isAllUploaded() : false)
          }
          onClick={handleSubmit}
          type="button"
        >
          {isSubmitting ? (
            <>
              <Loader className="h-4 w-4 animate-spin" />
              提交中...
            </>
          ) : (
            <>
              <Save className="h-4 w-4" />
              保存观察记录
            </>
          )}
        </button>
      </div>
      <DatePicker
        max={new Date()}
        onClose={() => {
          setDatePickerVisible(false);
        }}
        onConfirm={(val) => {
          setFormData((draft) => {
            draft.date = format(val, 'yyyy-MM-dd');
          });
        }}
        title="时间选择"
        visible={datePickerVisible}
      />
      {/* PBL 项目选择 */}
      <Picker
        columns={[
          projectList.map((project) => ({
            label: project.projectName,
            value: project.projectId,
          })),
        ]}
        onClose={() => {
          setProjectPickerVisible(false);
        }}
        onConfirm={(v, c) => {
          if (v[0]) {
            setFormData((draft) => {
              draft.projectId = (c.items[0]?.value as string) || '';
              draft.projectName = (c.items[0]?.label as string) || '';
            });
          }
        }}
        visible={projectPickerVisible}
      />

      {/* 观察地点选择组件 */}
      <RegionSelector
        onClose={() => setRegionPopupVisible(false)}
        onRegionSelect={(regionId, name) => {
          setRegionName(name);
          setFormData((draft) => {
            draft.zoneId = regionId;
          });
        }}
        regionData={reportAreaList}
        selectedRegionId={formData.zoneId}
        visible={regionPopupVisible}
      />

      {/* 标签选择组件 */}
      <TagSelector
        onClose={() => setTagPopupVisible(false)}
        onConfirm={(tags) => {
          setSelectedTags(tags);
          setFormData((draft) => {
            draft.tagIds = tags.map((tag) => tag.tagId);
          });
        }}
        onTagsChange={(tags) => {
          setChangeTags(tags);
        }}
        selectedTags={tagPopupVisible ? changeTags : selectedTags}
        tagOptions={tagList}
        visible={tagPopupVisible}
      />
    </main>
  );
}
