'use client';

import { Input, Picker, Switch, Toast } from 'antd-mobile';
import { Check, ChevronDown, Loader2 } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import Script from 'next/script';
import { useEffect, useRef, useState } from 'react';
import { useImmer } from 'use-immer';
import {
  getProjectList,
  getReportAreas,
  getReportTags,
  uploadMaterial,
} from '@/api/pbl';
import TagSelector, {
  type TagOption,
} from '@/app/pbl/record/components/TagSelector';
import ClassPicker from '@/components/ClassPicker';
import { cn } from '@/lib/utils';
import { canObserveAddMedia, observeAddMedia } from '@/utils';
import type { FileItemType } from './components/FileItem';
// 组件导入
import FileUpload from './components/FileUpload';
import RegionSelector, { type RegionData } from './components/RegionSelector';
import SelectField from './components/SelectField';

interface MediaItem {
  type: number; // 1 图片 2 视频 3 音频
  url: string;
  videoPlayType: number;
  fileSize: number;
  duration: number;
  cover: string;
  name: string;
  source: number;
}

interface SubmitData {
  observationId?: string;
  projectId: string;
  projectName?: string;
  deptId: string;
  deptName?: string;
  medias: MediaItem[];
  isMerge: number;
  contentTheme?: string; // 内容主题
  regionId?: string; // 区域ID
  regionName?: string; // 区域名称
  tags?: TagOption[]; // 标签数组
}

export default function MaterialCreatePage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const classId = searchParams?.get('classId') || '';
  const projectId = searchParams?.get('projectId') || '';
  const fileUploadRef = useRef<{ getFiles: () => FileItemType[] }>(null);
  // 原始项目列表，项目列表会根据班级 id 过滤
  const [originalProjectList, setOriginalProjectList] = useState<
    Array<{ projectId: string; projectName: string; deptId: string }>
  >([]);

  const [projectList, setProjectList] = useState<
    Array<{ projectId: string; projectName: string; deptId: string }>
  >([]);
  const [isUploading, setIsUploading] = useState(false);
  const [projectPickerVisible, setProjectPickerVisible] = useState(false);
  const [regionPopupVisible, setRegionPopupVisible] = useState(false);
  const [tagPopupVisible, setTagPopupVisible] = useState(false);
  const [submitData, setSubmitData] = useImmer<SubmitData>({
    observationId: '',
    projectId: '',
    projectName: '',
    deptId: classId,
    deptName: '',
    medias: [],
    isMerge: 0,
    contentTheme: '', // 内容主题
    regionId: '',
    regionName: '',
    tags: [],
  });
  const [tagList, setTagList] = useState<TagOption[]>([]);
  const [reportAreaList, setReportAreaList] = useState<RegionData[]>([]);
  const [selectedTags, setSelectedTags] = useState<TagOption[]>([]);

  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = 'AI生成观察记录';
    }
    fetchProjectList();
    fetchReportAreas();
    fetchTagList();
  }, []);

  useEffect(() => {
    if (submitData.deptId && originalProjectList.length) {
      setSubmitData((draft) => {
        draft.projectId = '';
        draft.projectName = '';
      });
      setProjectList(
        originalProjectList.filter((item) => item.deptId === submitData.deptId)
      );
    }
  }, [submitData.deptId, originalProjectList]);

  // 自动选中传入的 projectId 对应的项目
  useEffect(() => {
    if (projectId && projectList.length > 0) {
      const targetProject = projectList.find(
        (item) => item.projectId === projectId
      );
      if (targetProject) {
        setSubmitData((draft) => {
          draft.projectId = targetProject.projectId;
          draft.projectName = targetProject.projectName;
        });
      }
    }
  }, [projectId, projectList, setSubmitData]);

  const fetchProjectList = async () => {
    const response = await getProjectList({});
    // @ts-expect-error
    const data = response.list;
    setOriginalProjectList(data || []);
  };

  const fetchReportAreas = async () => {
    const response = await getReportAreas();
    // @ts-expect-error
    const data = response.list;
    setReportAreaList(data || []);
  };

  const fetchTagList = async () => {
    const response = await getReportTags();
    // @ts-expect-error
    const data = response.list;

    setTagList(data || []);
  };

  // 处理文件上传完成
  const handleUploadComplete = (completedFiles: FileItemType[]) => {
    console.log('🚀 ~ completedFiles:', completedFiles);
    setSubmitData((draft) => {
      draft.medias = completedFiles.map((file) => {
        let type: number;
        if (file.type.includes('video')) {
          type = 2;
        } else if (file.type.includes('audio')) {
          type = 3;
        } else if (file.type.includes('image')) {
          type = 1;
        } else {
          type = 0;
        }

        return {
          type,
          duration: Math.floor(file.duration || 0),
          videoPlayType: 1,
          fileSize: file.size || 0,
          source: 1,
          url: file.url || '',
          cover: file.cover || '',
          name: file.name,
        };
      });
    });
  };

  // 处理上传状态变化
  const handleUploadStatusChange = (uploading: boolean) => {
    setIsUploading(uploading);
  };

  // 处理保存
  const handleSave = async () => {
    // 表单验证
    if (!submitData.deptId) {
      Toast.show({
        content: '请选择班级',
        position: 'top',
      });
      return;
    }

    // 获取当前文件列表
    const currentFiles = fileUploadRef.current?.getFiles() || [];
    console.log('🚀 ~ currentFiles:', currentFiles);
    // 准备最终的提交数据
    const finalSubmitData =
      submitData.medias.length === 0 && currentFiles.length > 0
        ? {
            ...submitData,
            medias: currentFiles.map((file) => {
              let type: number;
              if (file.type.includes('video')) {
                type = 2;
              } else if (file.type.includes('audio')) {
                type = 3;
              } else if (file.type.includes('image')) {
                type = 1;
              } else {
                type = 0;
              }

              return {
                type,
                duration: Math.floor(file.duration || 0),
                videoPlayType: 1,
                fileSize: file.size || 0,
                source: 1,
                url: file.url || '',
                cover: file.cover || '',
                name: file.name,
              };
            }),
          }
        : { ...submitData };

    // 验证是否有文件
    if (finalSubmitData.medias.length === 0) {
      Toast.show({
        content: '请选择至少一个文件',
        position: 'top',
      });
      return;
    }
    if (canObserveAddMedia()) {
      const medias = currentFiles.map((file) => ({
        filePath: file.url || '',
        coverFilePath: file.cover || '',
        fileName: file.name,
        fileSize: file.size || 0,
        duration: Math.floor(file.duration || 0),
        localIdentifier: file.localIdentifier || '',
        videoPlayType: 1,
        type: (() => {
          if (file.type.includes('video')) {
            return 2;
          }
          if (file.type.includes('audio')) {
            return 3;
          }
          if (file.type.includes('image')) {
            return 1;
          }
          return 0;
        })(),
      }));
      observeAddMedia({
        videoCompressbBitrate: 10_000_000,
        medias,
        dataExtra: {
          isMerge: submitData.isMerge,
          projectId: submitData.projectId,
          deptId: submitData.deptId,
          observationId: submitData.observationId,
        },
        mediasExtra: {
          caption: submitData.contentTheme,
          source: '1',
          zoneId: submitData.regionId,
          tagIds: submitData.tags?.map((item) => item.tagId) ?? [],
        },
      });
      router.back();
      return;
    }
    try {
      setIsUploading(true);

      // 同步更新状态（可选，用于 UI 显示）
      setSubmitData((draft) => {
        draft.medias = finalSubmitData.medias;
      });

      // 使用最新的数据进行上传
      await uploadMaterial(finalSubmitData);

      Toast.show({
        icon: 'success',
        content: '保存成功，请耐心等待AI处理',
        position: 'top',
      });
      router.back();
    } catch (error) {
      Toast.show({
        icon: 'fail',
        content: '保存失败，请重试',
        position: 'top',
      });
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <main className="min-h-screen bg-gradient-to-b from-slate-50 to-white px-4 py-8">
      {/* 文件上传组件 */}
      <div className="mx-auto max-w-3xl">
        <FileUpload
          onUploadComplete={handleUploadComplete}
          onUploadStatusChange={handleUploadStatusChange}
          ref={fileUploadRef}
        />
      </div>

      {/* 素材信息 */}
      <div className="mx-auto mb-8 max-w-3xl">
        <div className="mb-6 rounded-xl border border-gray-100 bg-white p-4 shadow-sm transition-all duration-300 hover:shadow-md">
          <h2 className="mb-5 font-semibold text-gray-800 text-lg">填写信息</h2>
          <div className="grid gap-5 md:grid-cols-2">
            <div>
              <div className="mb-1.5 block font-medium text-gray-700 text-sm">
                关联班级
              </div>
              <div className="relative cursor-pointer">
                <div className="w-full rounded-lg border border-gray-200 bg-gray-50 px-4 py-3 text-gray-700 transition-colors duration-200 hover:bg-gray-100 focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500">
                  <ClassPicker
                    onChange={(id, className) => {
                      if (id) {
                        setSubmitData((draft) => {
                          draft.deptId = id;
                          draft.deptName = className;
                        });
                      }
                    }}
                    placeholder="请选择班级"
                    value={classId}
                  />
                </div>
                <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                  <ChevronDown className="h-5 w-5 text-gray-400" />
                </div>
              </div>
            </div>
            {!!submitData.deptId && projectList.length > 0 && (
              <SelectField
                label="关联 PBL 项目"
                onClick={() => setProjectPickerVisible(true)}
                placeholder="请选择 PBL 项目"
                value={submitData.projectName || ''}
              />
            )}
            <div className="md:col-span-2">
              <div className="mb-1.5 block font-medium text-gray-700 text-sm">
                内容主题
              </div>
              <Input
                className="w-full rounded-lg border border-gray-200 bg-gray-50 px-4 py-3 text-gray-700 transition-colors duration-200 focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
                onChange={(value) => {
                  setSubmitData((draft) => {
                    draft.contentTheme = value;
                  });
                }}
                placeholder="补充更多信息，提升AI理解准确度"
                style={{
                  '--color': '#374151',
                  '--font-size': '14px',
                  '--placeholder-color': '#9ca3af',
                }}
                value={submitData.contentTheme}
              />
            </div>

            {/* 区域选择 */}
            <SelectField
              label="观察地点"
              onClick={() => setRegionPopupVisible(true)}
              placeholder="请选择"
              value={submitData.regionName || ''}
            />

            {/* 标签选择 */}
            <SelectField
              label="内容标签"
              onClick={() => {
                setSelectedTags(submitData.tags || []);
                setTagPopupVisible(true);
              }}
              placeholder="请选择标签"
              value={
                tagPopupVisible
                  ? selectedTags.map((tag) => tag.tagName).join(', ') || ''
                  : submitData.tags?.map((tag) => tag.tagName).join(', ') || ''
              }
            />
            {submitData.medias.length > 1 && (
              <div className="mt-4 flex items-center justify-between gap-2">
                <span className="text-gray-700 text-sm">
                  本次上传素材合并为一条观察记录
                </span>
                <Switch
                  checked={submitData.isMerge === 1}
                  onChange={(checked) => {
                    setSubmitData((draft) => {
                      draft.isMerge = checked ? 1 : 0;
                    });
                  }}
                />
              </div>
            )}
          </div>
        </div>

        {/* 保存按钮 */}
        <button
          className={cn(
            'w-full rounded-full px-6 py-3 font-medium text-white transition-all duration-300',
            'flex items-center justify-center space-x-2',
            'shadow-md hover:shadow-lg disabled:cursor-not-allowed disabled:opacity-60',
            'bg-gradient-to-r from-violet-400 to-violet-500 text-white'
          )}
          disabled={isUploading}
          onClick={handleSave}
          type="button"
        >
          {isUploading ? (
            <>
              <Loader2 className="mr-2 h-5 w-5 animate-spin" />
              <span>处理中...</span>
            </>
          ) : (
            <>
              <Check className="mr-2 h-5 w-5" />
              <span>保存</span>
            </>
          )}
        </button>
      </div>
      {/* PBL 项目选择 */}
      <Picker
        columns={[
          projectList.map((project) => ({
            label: project.projectName,
            value: project.projectId,
          })),
        ]}
        onClose={() => {
          setProjectPickerVisible(false);
        }}
        onConfirm={(v, c) => {
          console.log(v, c);
          if (v[0]) {
            setSubmitData((draft) => {
              draft.projectId = (c.items[0]?.value as string) || '';
              draft.projectName = (c.items[0]?.label as string) || '';
            });
          }
        }}
        visible={projectPickerVisible}
      />

      {/* 观察地点选择组件 */}
      <RegionSelector
        onClose={() => setRegionPopupVisible(false)}
        onRegionSelect={(regionId, regionName) => {
          setSubmitData((draft) => {
            draft.regionId = regionId;
            draft.regionName = regionName;
          });
        }}
        regionData={reportAreaList}
        selectedRegionId={submitData.regionId}
        visible={regionPopupVisible}
      />

      {/* 标签选择组件 */}
      <TagSelector
        onClose={() => setTagPopupVisible(false)}
        onConfirm={(tags) => {
          setSubmitData((draft) => {
            draft.tags = tags;
          });
        }}
        onTagsChange={(tags) => {
          setSelectedTags(tags);
        }}
        selectedTags={tagPopupVisible ? selectedTags : submitData.tags || []}
        tagOptions={tagList}
        visible={tagPopupVisible}
      />
      <Script
        onLoad={() => {
          const { eruda } = window as any;
          eruda.init();
          eruda.position({ x: 20, y: 240 });
        }}
        src="//cdn.jsdelivr.net/npm/eruda"
      />
    </main>
  );
}
