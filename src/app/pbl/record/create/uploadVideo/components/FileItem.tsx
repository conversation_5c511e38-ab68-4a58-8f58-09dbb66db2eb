'use client';

import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import {
  Check,
  CheckCircle2,
  GripVertical,
  Mic,
  Trash2,
  Video,
} from 'lucide-react';
import { cn } from '@/lib/utils';

// 定义文件状态
export const FILE_STATUS = {
  WAITING: 'waiting',
  UPLOADING: 'uploading',
  COMPLETED: 'completed',
  FAILED: 'failed',
};

// 定义文件类型
export interface FileItemType {
  id: string;
  file?: File;
  name: string;
  size: number;
  type: string;
  status: string;
  progress: number;
  url?: string;
  duration?: number;
  cover?: string;
  localIdentifier?: string;
  error?: string;
}

interface FileItemProps {
  file: FileItemType;
  onDelete: (id: string) => void;
  isUploading: boolean;
  formatFileSize: (bytes: number) => string;
}

const FileItem: React.FC<FileItemProps> = ({
  file,
  onDelete,
  isUploading,
  formatFileSize,
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: file.id });

  let opacity: number;
  if (isDragging) {
    opacity = 0.5;
  } else {
    opacity = 1;
  }

  let zIndex: any;
  if (isDragging) {
    zIndex = 10;
  } else {
    zIndex = 'auto';
  }

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity,
    zIndex,
  };

  const isVideo = file.type.includes('video');
  const isCompleted = file.status === FILE_STATUS.COMPLETED;
  const isFailed = file.status === FILE_STATUS.FAILED;

  return (
    <div
      className={cn(
        'relative flex items-center overflow-hidden rounded-xl shadow-sm transition-all duration-300',
        (() => {
          if (isCompleted) {
            return 'border border-emerald-100 bg-gradient-to-r from-emerald-50 to-teal-50';
          }
          return 'border border-gray-100 bg-white';
        })(),
        'hover:shadow-md'
      )}
      ref={setNodeRef}
      style={style}
    >
      {/** biome-ignore lint/a11y/useAriaPropsSupportedByRole: <explanation> */}
      <div
        {...attributes}
        {...listeners}
        aria-label="拖动排序"
        className="ml-4 flex cursor-grab touch-none items-center self-stretch"
      >
        <GripVertical className="h-4 w-4 text-gray-400" />
      </div>

      <div className="flex min-w-0 flex-1 items-center gap-3 p-4">
        <div
          className={cn(
            'flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-lg',
            (() => {
              if (isVideo) {
                return 'bg-blue-100 text-blue-600';
              }
              return 'bg-purple-100 text-purple-600';
            })(),
            isCompleted && 'bg-emerald-100 text-emerald-600'
          )}
        >
          {(() => {
            if (isCompleted) {
              return <CheckCircle2 className="h-6 w-6" />;
            }
            if (isVideo) {
              return <Video className="h-6 w-6" />;
            }
            return <Mic className="h-6 w-6" />;
          })()}
        </div>

        <div className="min-w-0 flex-1">
          <div className="mb-1 flex items-center justify-between">
            <h4 className="truncate pr-2 font-medium text-gray-800 text-sm">
              {file.name}
            </h4>
            <span className="whitespace-nowrap text-gray-500 text-xs">
              {formatFileSize(file.size)}
            </span>
          </div>
          {(() => {
            if (isCompleted) {
              return (
                <div className="flex items-center justify-between">
                  <div className="flex items-center text-emerald-600 text-xs">
                    <Check className="mr-1 h-3.5 w-3.5" />
                    <span>上传完成</span>
                  </div>
                  {(() => {
                    if (file.duration) {
                      return (
                        <div className="text-gray-500 text-xs">
                          {Math.floor(file.duration / 60)}:
                          {String(Math.floor(file.duration % 60)).padStart(
                            2,
                            '0'
                          )}
                        </div>
                      );
                    }
                    return null;
                  })()}
                </div>
              );
            }
            if (isFailed) {
              return (
                <div className="text-red-500 text-xs">
                  {file.error || '上传失败'}
                </div>
              );
            }
            if (file.status === FILE_STATUS.UPLOADING) {
              return (
                <div className="w-full">
                  <div className="mb-1 flex justify-between text-gray-500 text-xs">
                    <span>正在上传...</span>
                    <span>{file.progress}%</span>
                  </div>
                  <div className="h-1.5 w-full overflow-hidden rounded-full bg-gray-100">
                    <div
                      className="h-full bg-gradient-to-r from-blue-400 to-indigo-500 transition-all duration-300"
                      style={{ width: `${file.progress}%` }}
                    />
                  </div>
                </div>
              );
            }
            return (
              <div className="flex items-center justify-between">
                <div className="flex items-center text-amber-600 text-xs">
                  <span>等待上传</span>
                </div>
                {(() => {
                  if (file.duration) {
                    return (
                      <div className="text-gray-500 text-xs">
                        {Math.floor(file.duration / 60)}:
                        {String(Math.floor(file.duration % 60)).padStart(
                          2,
                          '0'
                        )}
                      </div>
                    );
                  }
                  return null;
                })()}
              </div>
            );
          })()}
        </div>

        <button
          aria-label="删除文件"
          className={cn(
            'ml-1 rounded-full transition-colors',
            (() => {
              if (isCompleted) {
                return 'text-gray-400 hover:bg-emerald-50 hover:text-emerald-600';
              }
              return 'text-gray-400 hover:bg-red-50 hover:text-red-500';
            })(),
            'disabled:cursor-not-allowed disabled:opacity-50'
          )}
          disabled={isUploading && !isCompleted}
          onClick={() => onDelete(file.id)}
          type="button"
        >
          <Trash2 className="h-4 w-4 text-red-400" />
        </button>
      </div>
    </div>
  );
};

export default FileItem;
