'use client';

import { ChevronDown } from 'lucide-react';

interface SelectFieldProps {
  label: string;
  value: string;
  placeholder: string;
  onClick: () => void;
  className?: string;
}

const SelectField: React.FC<SelectFieldProps> = ({
  label,
  value,
  placeholder,
  onClick,
  className,
}) => {
  return (
    <div className={className}>
      <div className="mb-1.5 block font-medium text-gray-700 text-sm">
        {label}
      </div>
      {/** biome-ignore lint/a11y/useSemanticElements: <explanation> */}
      <div
        className="relative cursor-pointer"
        onClick={onClick}
        onKeyDown={() => null}
        role="button"
        tabIndex={0}
      >
        <div className="w-full rounded-lg border border-gray-200 bg-gray-50 px-4 py-3 text-gray-700 transition-colors duration-200 hover:bg-gray-100 focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500">
          {value ? (
            <span>{value}</span>
          ) : (
            <span className="text-gray-400">{placeholder}</span>
          )}
        </div>
        <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
          <ChevronDown className="h-5 w-5 text-gray-400" />
        </div>
      </div>
    </div>
  );
};

export default SelectField;
