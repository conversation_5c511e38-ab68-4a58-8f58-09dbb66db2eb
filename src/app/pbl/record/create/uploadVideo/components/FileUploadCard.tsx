'use client';

import { CloudUpload, Info } from 'lucide-react';
import { cn } from '@/lib/utils';

interface FileUploadCardProps {
  isUploading: boolean;
  onFileSelect: () => void;
  className?: string;
  multiple?: boolean;
}

const FileUploadCard: React.FC<FileUploadCardProps> = ({
  isUploading,
  onFileSelect,
  className,
  multiple = false,
}) => {
  return (
    <div
      className={cn(
        'mx-auto flex w-full max-w-md flex-col items-center',
        className
      )}
    >
      <ul className="mb-4 list-disc pl-4 text-gray-600 text-sm">
        <li>上传的视频，系统将自动分析生成观察记录。</li>
        <li>请确保已经录入幼儿人脸，以便AI能够准确识别每个幼儿。</li>
      </ul>
      <button
        className="group relative flex aspect-square w-56 flex-col items-center justify-center overflow-hidden rounded-2xl border-2 border-blue-300 border-dashed bg-white transition-all duration-300 hover:border-blue-500 disabled:cursor-not-allowed disabled:opacity-70"
        disabled={isUploading}
        onClick={onFileSelect}
        type="button"
      >
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-indigo-50 opacity-0 transition-opacity duration-300 group-hover:opacity-100" />
        <div className="relative z-10 flex flex-col items-center justify-center p-6 text-center">
          <div className="mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-blue-100 transition-transform duration-300 group-hover:scale-110 ">
            <CloudUpload
              className={cn('h-8 w-8 text-blue-600 transition-colors')}
              strokeWidth={1.5}
            />
          </div>
          <div className="mb-2 font-medium text-gray-700 text-xl transition-colors group-hover:text-blue-700">
            点击这里添加文件
          </div>
        </div>
      </button>

      <div className="mt-4 flex items-center justify-center text-gray-500 text-sm">
        <Info className="mr-2 h-4 w-4" />
        <span className="text-xs">
          支持 MP4, MOV 格式，最大 1000MB{multiple ? '，可多选' : ''}
        </span>
      </div>
    </div>
  );
};

export default FileUploadCard;
