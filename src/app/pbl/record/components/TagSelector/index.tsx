'use client';

import { Dialog, Form, Input, Popup, Toast } from 'antd-mobile';
import { Plus, Search, X } from 'lucide-react';
import { useEffect, useMemo, useRef, useState } from 'react';
import { createReportTag } from '@/api/pbl';
import { cn } from '@/lib/utils';
// 标签选项接口
export interface TagOption {
  tagId: string;
  tagName: string;
  tagType: number;
}

// 组件属性接口
export interface TagSelectorProps {
  visible: boolean;
  onClose: () => void;
  selectedTags: TagOption[];
  onTagsChange: (tags: TagOption[]) => void;
  onConfirm: (tags: TagOption[]) => void;
  tagOptions?: TagOption[];
  title?: string;
  searchPlaceholder?: string;
}

export default function TagSelector({
  visible,
  onClose,
  selectedTags,
  onTagsChange,
  onConfirm,
  tagOptions = [],
  title = '选择标签',
  searchPlaceholder = '请输入标签名称',
}: TagSelectorProps) {
  const [searchText, setSearchText] = useState('');
  const [customTagOptions, setCustomTagOptions] = useState<TagOption[]>(
    tagOptions.filter((tag) => tag.tagType === 2)
  );
  // 使用 useRef 保存初始值，只在组件挂载时设置一次
  const defaultTagOptionsRef = useRef<TagOption[]>(selectedTags);
  const selectedTagsRef = useRef<TagOption[]>(selectedTags);
  const [form] = Form.useForm();
  useEffect(() => {
    defaultTagOptionsRef.current = selectedTags;
    selectedTagsRef.current = selectedTags;
  }, [selectedTags]);
  // 使用 useMemo 优化标签过滤和分类计算
  const { filteredTags, systemTags, customTags } = useMemo(() => {
    // 合并系统标签和本地自定义标签
    const allTags = [
      ...tagOptions.filter((tag) => tag.tagType === 2 || tag.tagType === 1),
      ...customTagOptions,
    ];

    const filtered = allTags.filter(
      (tag) =>
        searchText === '' ||
        tag.tagName.toLowerCase().includes(searchText.toLowerCase())
    );

    return {
      filteredTags: filtered,
      systemTags: filtered.filter((tag) => tag.tagType === 1),
      customTags: filtered.filter((tag) => tag.tagType === 2),
    };
  }, [tagOptions, customTagOptions, searchText]);
  // 处理标签选择
  const handleTagToggle = (tag: TagOption) => {
    const isSelected = selectedTags.some(
      (selectedTag) => selectedTag.tagId === tag.tagId
    );

    if (isSelected) {
      // 取消选择
      const newTags = selectedTags.filter(
        (selectedTag) => selectedTag.tagId !== tag.tagId
      );
      onTagsChange(newTags);
      selectedTagsRef.current = newTags;
    } else {
      // 添加选择
      const newTags = [...selectedTags, tag];
      onTagsChange(newTags);
      selectedTagsRef.current = newTags;
    }
  };

  // 新建标签处理函数
  const handleCreateTag = () => {
    form.resetFields();
    Dialog.show({
      title: '新建自定义标签',
      content: (
        <Form form={form} layout="vertical">
          <Form.Item
            label="标签名称"
            name="name"
            rules={[{ required: true, message: '请输入标签名称' }]}
          >
            <Input placeholder="请输入标签名称" />
          </Form.Item>
        </Form>
      ),
      closeOnAction: true,
      actions: [
        [
          {
            key: 'cancel',
            text: '取消',
          },
          {
            key: 'confirm',
            text: '确定',
            bold: true,
            onClick: async () => {
              try {
                const values = await form.validateFields();
                const tagName = values.name.trim();

                const newTag: TagOption = {
                  tagId: `custom_${Date.now()}`,
                  tagName,
                  tagType: 2,
                };
                const res: any = await createReportTag({
                  tagName,
                  tagType: 2,
                });
                newTag.tagId = res.tagId;
                setCustomTagOptions((prev) => [newTag, ...prev]);
                Toast.show({
                  icon: 'success',
                  content: '标签创建成功',
                });
              } catch (error) {
                console.log('表单验证失败:', error);
              }
            },
          },
        ],
      ],
    });
  };

  // 关闭弹窗并清空搜索
  const handleClose = () => {
    setSearchText('');
    onClose();
  };
  const handleConfirm = () => {
    onConfirm(selectedTagsRef.current);
    setSearchText('');
    onClose();
  };

  return (
    <Popup
      bodyStyle={{
        borderTopLeftRadius: '8px',
        borderTopRightRadius: '8px',
        height: '80vh',
        maxHeight: '90vh',
      }}
      onMaskClick={handleClose}
      position="bottom"
      visible={visible}
    >
      <div className="flex h-full flex-col">
        {/* 标题栏 */}
        <div className="relative flex flex-shrink-0 items-center justify-center p-4 pb-0">
          <h3 className="font-medium text-gray-800 text-lg">{title}</h3>
          <button
            className="absolute right-4 rounded-full p-1 hover:bg-gray-100"
            onClick={handleClose}
            type="button"
          >
            <X className="h-5 w-5 text-gray-500" />
          </button>
        </div>

        {/* 搜索框 */}
        <div className="flex-shrink-0 px-4 py-4">
          <div className="flex items-center rounded-lg bg-gray-50 px-3 py-1">
            <Search className="mr-2 h-4 w-4 text-gray-400" />
            <input
              className="flex-1 bg-transparent text-gray-700 text-sm placeholder-gray-400 outline-none"
              onChange={(e) => setSearchText(e.target.value)}
              placeholder={searchPlaceholder}
              type="text"
              value={searchText}
            />
          </div>
        </div>
        {searchText && filteredTags.length === 0 ? (
          <div className="py-8 text-center text-gray-400">
            <p>未找到相关标签</p>
            <p className="mt-1 text-sm">尝试调整搜索关键词</p>
          </div>
        ) : (
          <div className="flex-1 overflow-y-auto px-4">
            {/* 系统标签 */}
            {systemTags.length > 0 && (
              <div className="mb-6">
                <div className="mb-3 flex items-center">
                  <div className="mr-2 h-4 w-1 rounded bg-blue-500" />
                  <h4 className="font-medium text-base text-gray-700">
                    系统标签
                  </h4>
                </div>
                <div className="flex flex-wrap gap-2">
                  {systemTags.map((tag) => {
                    const isSelected = selectedTags.some(
                      (selectedTag) => selectedTag.tagId === tag.tagId
                    );

                    return (
                      <button
                        className={cn(
                          'rounded-full px-3 py-1 font-medium text-sm transition-all duration-200',
                          'border border-gray-200 hover:border-gray-300',
                          isSelected
                            ? 'border-indigo-500 bg-indigo-500 text-white'
                            : 'bg-gray-50 text-gray-700 hover:bg-gray-100'
                        )}
                        key={tag.tagId}
                        onClick={() => handleTagToggle(tag)}
                        type="button"
                      >
                        #{tag.tagName}
                      </button>
                    );
                  })}
                </div>
              </div>
            )}

            {/* 自定义标签 */}
            <div className="mb-6">
              <div className="mb-3 flex items-center justify-between">
                <div className="flex items-center">
                  <div className="mr-2 h-4 w-1 rounded bg-purple-500" />
                  <h4 className="font-medium text-base text-gray-700">
                    自定义标签
                  </h4>
                </div>
                <button
                  className="flex items-center rounded px-2 py-1 text-indigo-600 text-sm transition-colors duration-200 hover:bg-indigo-50 hover:text-indigo-700"
                  onClick={handleCreateTag}
                  type="button"
                >
                  <Plus className="mr-1 h-4 w-4" />
                  新建标签
                </button>
              </div>
              {customTags.length > 0 ? (
                <div className="flex flex-wrap gap-2">
                  {customTags.map((tag) => {
                    const isSelected = selectedTags.some(
                      (selectedTag) => selectedTag.tagId === tag.tagId
                    );

                    return (
                      <button
                        className={cn(
                          'rounded-full px-3 py-1 font-medium text-sm transition-all duration-200',
                          'border border-gray-200 hover:border-gray-300',
                          isSelected
                            ? 'border-indigo-500 bg-indigo-500 text-white'
                            : 'bg-gray-50 text-gray-700 hover:bg-gray-100'
                        )}
                        key={tag.tagId}
                        onClick={() => handleTagToggle(tag)}
                        type="button"
                      >
                        #{tag.tagName}
                      </button>
                    );
                  })}
                </div>
              ) : (
                <div className="py-4 text-center text-gray-400 text-sm">
                  暂无自定义标签，点击右上角新建
                </div>
              )}
            </div>
          </div>
        )}

        {/* 已添加标签 */}
        <div className="max-h-[calc(35%)] flex-shrink-0 border-gray-100 border-t">
          {/* 固定标题 */}
          <div className="sticky top-0 border-gray-100 border-b bg-white px-4 py-3">
            <div className="text-gray-500 text-sm">
              已添加标签 ({selectedTags.length})
            </div>
          </div>

          {/* 可滚动内容 */}
          <div
            className="overflow-y-auto px-4 py-3"
            style={{ maxHeight: 'calc(100% - 48px)' }}
          >
            {selectedTags.length === 0 ? (
              <div className="py-2 text-center text-gray-400">暂无添加标签</div>
            ) : (
              <div className="flex flex-wrap gap-2">
                {selectedTags.map((tag) => (
                  <div
                    className="relative inline-flex items-center rounded-full bg-indigo-50 px-3 py-1 text-indigo-700 text-sm"
                    key={tag.tagId}
                  >
                    <span>#{tag.tagName}</span>
                    <button
                      className="-mr-1 ml-1 flex h-4 w-4 items-center justify-center rounded-full bg-indigo-200 transition-colors duration-200 hover:bg-indigo-300"
                      onClick={() => handleTagToggle(tag)}
                      type="button"
                    >
                      <X className="h-2.5 w-2.5 text-indigo-600" />
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* 底部按钮 */}
        <div className="flex flex-shrink-0 gap-3 p-4 pt-3">
          <button
            className="flex-1 rounded-full border border-gray-300 py-3 font-medium text-gray-700"
            onClick={handleClose}
            type="button"
          >
            取消
          </button>
          <button
            className="flex-1 rounded-full bg-indigo-500 py-3 font-medium text-white"
            onClick={() => handleConfirm()}
            type="button"
          >
            确定
          </button>
        </div>
      </div>
    </Popup>
  );
}
