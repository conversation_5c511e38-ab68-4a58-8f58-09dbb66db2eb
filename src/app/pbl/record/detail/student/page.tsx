'use client';

import { But<PERSON>, SpinLoading } from 'antd-mobile';
import { ClipboardList, File, MapPin } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useImmer } from 'use-immer';
import { getObservationDetail } from '@/api/pbl';
import Media from '../components/Media';
import StudentCard, { type Student } from '../components/StudentCard';

export default function App() {
  const searchParams = useSearchParams();
  const observationId = searchParams?.get('observationId');
  const studentId = searchParams?.get('studentId');
  const router = useRouter();

  const [loading, setLoading] = useState(true);
  const [record, setRecord] = useImmer({
    date: '',
    content: '',
    nextStepPlan: '',
    students: [],
    medias: [],
    createUser: {
      name: '',
      avatar: '',
    },
    createTime: '',
    deptId: '',
    conversation: [],
    zone: {
      zoneName: '',
    },
    source: 0,
    tags: [],
  });

  const [student, setStudent] = useState<Student>();

  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = '观察记录详情';
    }
  }, []);

  useEffect(() => {
    if (observationId) {
      setLoading(true);
      getObservationDetail(observationId)
        .then((res) => {
          setRecord(res);
          setStudent(res.students.find((student) => student.id === studentId));
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [observationId]);

  if (loading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <SpinLoading color="primary" />
      </div>
    );
  }

  return (
    <div className="relative flex h-screen flex-col">
      <div className="flex-grow overflow-y-auto pb-20">
        {!!student && (
          <StudentCard
            isStudentExpanded={() => true}
            observationId={observationId}
            readonly={true}
            student={student}
          />
        )}
        {/* 观察地点 */}
        {record.zone?.zoneName && (
          <div className="mb-4 flex items-center px-4">
            <div className="flex items-center gap-2 font-bold text-base text-gray-700">
              <MapPin className="h-5 w-5 text-green-500" />
              <span>观察地点：</span>
            </div>
            <div className="text-base text-gray-700">
              {record.zone.zoneName}
            </div>
          </div>
        )}
        <div className="px-4">
          {record.medias.length > 0 && (
            <>
              <div className="mb-3 flex items-center justify-between font-bold text-gray-700 text-md">
                <div className="flex items-center gap-2">
                  <File className="h-5 w-5 text-green-500" />
                  <span>关联文件</span>
                  <span className="font-normal text-gray-500 text-xs">
                    ({record.medias.length})
                  </span>
                </div>
              </div>
              <Media media={record.medias} />
            </>
          )}
          {/* 标签 */}
          {record.tags && Array.isArray(record.tags) && (
            <div className="mb-4 flex flex-wrap gap-2">
              {record.tags?.map((tag: any, index) => {
                const tagColors = [
                  'bg-green-50 text-green-700',
                  'bg-orange-50 text-orange-700',
                  'bg-purple-50 text-purple-700',
                  'bg-blue-50 text-blue-700',
                  'bg-pink-50 text-pink-700',
                ];
                return (
                  <span
                    className={`mr-2 rounded-full px-3 py-1.5 text-sm ${
                      tagColors[index % tagColors.length]
                    }`}
                    key={tag.tagId}
                  >
                    #{tag.tagName}
                  </span>
                );
              })}
            </div>
          )}
          {/* 观察记录内容 */}
          <div className="mb-6">
            <div className="mb-2 flex items-center gap-2 font-bold text-gray-700 text-md">
              <ClipboardList className="h-5 w-5 text-amber-500" />
              <span>观察实录</span>
            </div>
            <div className="rounded-2xl bg-amber-50 p-4 text-base text-gray-700 leading-relaxed">
              {record.content}
            </div>
          </div>
          <div className="flex items-center justify-between rounded-lg bg-gray-50 p-3">
            <div className="flex items-center gap-3">
              <picture>
                <img
                  alt="老师头像"
                  className="h-10 w-10 rounded-full object-cover"
                  src={record.createUser?.avatar || ''}
                />
              </picture>
              <div>
                <p className="font-medium text-dark text-sm">记录人</p>
                <p className="text-gray-600 text-sm">
                  {record.createUser?.name} · {record.date} {record.createTime}
                </p>
              </div>
            </div>
          </div>
        </div>
        <div className="mt-5 flex justify-center">
          <Button
            color="primary"
            fill="outline"
            onClick={() => {
              router.push(`/pbl/record/detail?observationId=${observationId}`);
            }}
            size="mini"
          >
            查看关联的观察记录
          </Button>
        </div>
      </div>
    </div>
  );
}
