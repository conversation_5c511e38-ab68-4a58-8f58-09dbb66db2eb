'use client';

import { Toast } from 'antd-mobile';
import { useSetAtom } from 'jotai';
import { Plus, Users } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { memo, useRef, useState } from 'react';
import StudentPicker, {
  type StudentPickerRef,
} from '@/components/StudentPicker';
import { studentAtom } from '@/store/pbl';
import StudentCard from './StudentCard';

interface Student {
  id: string;
  name: string;
  avatar: string;
  abilities: string[];
  abilityCategories?: any[];
  progress?: string;
  evaluation: {
    observationId: string;
    observationText: string;
    abilities: {
      abilityId: string;
    }[];
  };
}

interface AssociatedStudentsProps {
  students: Student[];
  deptId: string;
  observationId: string;
}

const AssociatedStudents = memo(
  ({ students, deptId, observationId }: AssociatedStudentsProps) => {
    const router = useRouter();
    const [expandedStudents, setExpandedStudents] = useState<string[]>([]);
    const studentPickerRef = useRef<StudentPickerRef>(null);
    const setStudent = useSetAtom(studentAtom);

    const handleSelectStudent = async () => {
      if (studentPickerRef.current) {
        try {
          const selectedStudent =
            await studentPickerRef.current.selectStudentsByClassId();
          if (selectedStudent) {
            console.log('选中的学生：', selectedStudent);
            console.log('🚀 ~ students:', students);
            const selectedIdStr = String(selectedStudent.studentId);
            if (students.some((student) => student.id === selectedIdStr)) {
              Toast.show({
                content: '学生已存在',
                position: 'bottom',
              });
              return;
            }
            setStudent({
              id: selectedIdStr,
              name: selectedStudent.studentName,
              avatar: selectedStudent.avatar,
              deptId,
              evaluation: {
                evaluationId: '',
                observationId,
                abilities: [],
              },
            });
            router.push('/pbl/record/update/evaluation');
          }
        } catch (error) {
          console.error('选择学生失败：', error);
        }
      }
    };

    // 切换学生卡片展开/折叠状态
    const toggleStudentExpand = (studentId: string, e: React.MouseEvent) => {
      e.stopPropagation(); // 阻止事件冒泡，避免触发卡片点击事件
      setExpandedStudents((prev) =>
        prev.includes(studentId)
          ? prev.filter((id) => id !== studentId)
          : [...prev, studentId]
      );
    };

    // 检查学生是否展开
    const isStudentExpanded = (studentId: string) =>
      expandedStudents.includes(studentId);

    return (
      <div className="mb-6 px-4">
        <div className="mb-2 flex items-center justify-between gap-2 font-bold text-md">
          <div className="flex items-center gap-2">
            <Users className="h-5 w-5 text-indigo-500" />
            <span>关联学生</span>
          </div>
          <div>
            <button
              className="flex items-center gap-1 rounded-full bg-indigo-50 px-3 py-1.5 text-indigo-600 text-xs transition-colors hover:bg-indigo-100"
              onClick={() => handleSelectStudent()}
              type="button"
            >
              <Plus className="h-4 w-4" /> 新增学生
            </button>
          </div>
        </div>
        
        <div className="space-y-3">
          {students.map((student) => (
            <StudentCard
              isStudentExpanded={
                students.length === 1 ? () => true : isStudentExpanded
              }
              key={student.id}
              student={student}
              toggleStudentExpand={toggleStudentExpand}
            />
          ))}
        </div>
        <StudentPicker classId={deptId} hideUI={true} ref={studentPickerRef} />
      </div>
    );
  }
);

AssociatedStudents.displayName = 'AssociatedStudents';

export default AssociatedStudents;
