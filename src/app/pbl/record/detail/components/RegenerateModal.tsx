'use client';

import { But<PERSON>, DotLoading, Modal, Toast } from 'antd-mobile';
import { useState } from 'react';
import { aiGenerateContent, updateObservation } from '@/api/pbl';

interface RegenerateModalProps {
  visible: boolean;
  observationId: string;
  currentContent: string;
  nextStepPlan: string;
  onClose: () => void;
  onSuccess: (newContent: string) => void;
}

const RegenerateModal = ({
  visible,
  observationId,
  currentContent,
  nextStepPlan,
  onClose,
  onSuccess,
}: RegenerateModalProps) => {
  const [prompt, setPrompt] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [editableContent, setEditableContent] = useState('');
  const [showConfirm, setShowConfirm] = useState(false);

  const generateContent = async () => {
    if (!prompt.trim()) {
      Toast.show({
        content: '请输入补充信息',
        position: 'bottom',
      });
      return;
    }

    setIsLoading(true);
    try {
      const query = `
        基于以下观察记录内容，根据用户的补充要求重新生成观察记录：
        
        原始内容：${currentContent}
        
        用户补充要求：${prompt}
        
        请生成一份新的观察记录内容，要求详细、专业，符合学前教育观察记录的标准。直接输出记录的正文内容，不要带格式，也不需要观察记录的其他额外信息。
      `;

      const response: any = await aiGenerateContent({
        conversation_id: '',
        files: [],
        inputs: [],
        query,
        response_mode: 'blocking',
      });

      if (response?.answer) {
        setEditableContent(response.answer);
        setShowConfirm(true);
      } else {
        Toast.show({
          content: '生成失败，请重试',
          position: 'bottom',
        });
      }
    } catch (error) {
      console.error('生成内容失败：', error);
      Toast.show({
        content: '生成失败，请重试',
        position: 'bottom',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleConfirmUse = async () => {
    try {
      await updateObservation(observationId, {
        title: '',
        content: editableContent,
        nextStepPlan, // 保持原有的nextStepPlan
        observationId,
      });

      Toast.show({
        content: '更新成功',
        position: 'bottom',
      });

      onSuccess(editableContent);
      handleClose();
    } catch (error) {
      console.error('更新失败：', error);
      Toast.show({
        content: '更新失败，请重试',
        position: 'bottom',
      });
    }
  };

  const handleClose = () => {
    setPrompt('');
    setEditableContent('');
    setShowConfirm(false);
    setIsLoading(false);
    onClose();
  };

  if (!visible) {
    return null;
  }

  return (
    <>
      {/* 输入补充信息的Modal */}
      {!showConfirm && (
        <Modal
          closeOnMaskClick={!isLoading}
          content={
            <div className="p-2">
              <div className="mb-4">
                <h3 className="text-center font-bold text-lg">
                  重新生成观察记录
                </h3>
                <p className="mt-2 text-center text-gray-500 text-sm">
                  补充更多信息重新生成新的观察记录
                </p>
              </div>

              <textarea
                className="mb-4 w-full rounded border border-gray-300 p-3"
                disabled={isLoading}
                onChange={(e) => setPrompt(e.target.value)}
                placeholder="请输入你的补充信息，信息越详细，AI生成的内容越准确"
                rows={3}
                value={prompt}
              />

              {isLoading ? (
                <div className="flex flex-col items-center justify-center py-4">
                  <div className="text-blue-500">
                    <DotLoading />
                  </div>
                  <p className="mt-2 text-gray-500">正在生成内容，请稍候...</p>
                </div>
              ) : (
                <div className="flex justify-evenly gap-2">
                  <Button color="default" onClick={handleClose}>
                    取消
                  </Button>
                  <Button color="primary" onClick={generateContent}>
                    重新生成
                  </Button>
                </div>
              )}
            </div>
          }
          onClose={handleClose}
          showCloseButton={!isLoading}
          visible={true}
        />
      )}

      {/* 确认使用生成内容的Modal */}
      {showConfirm && (
        <Modal
          closeOnMaskClick={false}
          content={
            <div className="p-2">
              <div className="mb-4">
                <h3 className="text-center font-bold text-lg">
                  确认使用生成的内容
                </h3>
              </div>

              <div className="mb-4 max-h-60 overflow-y-auto rounded border bg-gray-50 p-3">
                <textarea
                  className="h-40 w-full resize-none border-none bg-transparent text-sm leading-relaxed outline-none"
                  onChange={(e) => setEditableContent(e.target.value)}
                  placeholder="编辑生成的内容..."
                  value={editableContent}
                />
              </div>

              <div className="flex justify-end gap-2">
                <Button color="default" onClick={handleClose}>
                  放弃
                </Button>
                <Button color="primary" onClick={handleConfirmUse}>
                  确定使用
                </Button>
              </div>
            </div>
          }
          showCloseButton={false}
          visible={true}
        />
      )}
    </>
  );
};

export default RegenerateModal;
