'use client';

import clsx from 'clsx';
import { use<PERSON>tom, useSet<PERSON><PERSON> } from 'jotai';
import {
  ChevronDown,
  ChevronRight,
  ChevronUp,
  ClipboardList,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import type { MouseEvent } from 'react';
import { memo, useEffect, useState } from 'react';
import { getSchoolScales } from '@/api/pbl';
import { evaluationAtom, studentAtom } from '@/store/pbl';
import Bloom from './Bloom';
import DeeperLearning from './DeeperLearning';
import DevelopmentGuide from './DevelopmentGuide';
import LeuvenScales from './LeuvenScales';

interface AbilityTarget {
  id: number;
  name: string;
  completed: boolean;
}

interface AbilityCategory {
  id: string;
  name: string;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
  targets: AbilityTarget[];
  subCategories?: {
    name: string;
    targets: AbilityTarget[];
  }[];
}
export interface Student {
  id: string;
  name: string;
  avatar: string;
  abilities?: string[];
  abilityCategories?: AbilityCategory[];
  evaluation?: {
    observationText: string;
    observationId: string;
    abilities: {
      abilityId: string;
    }[];
  };
}

interface StudentCardProps {
  readonly?: boolean;
  student: Student;
  toggleStudentExpand?: (studentId: string, e: MouseEvent) => void;
  isStudentExpanded: (studentId: string) => boolean;
  observationId?: string | null;
}

const tabs = [
  { id: 'guide', type: '2', label: '3-6 岁发展指南' },
  { id: 'leuven', type: '3', label: '幸福感与参与度' },
  { id: 'deeperLearning', type: '1', label: '深度学习' },
  { id: 'bloom', type: '4', label: '布卢姆分类法评估' },
];

function StudentCard({
  readonly = false,
  observationId,
  student,
  toggleStudentExpand,
  isStudentExpanded,
}: StudentCardProps) {
  const router = useRouter();
  // 在组件内部管理 activeTab 状态
  const [activeTab, setActiveTab] = useState<string>('guide'); // 默认选中第一个 Tab
  const setStudent = useSetAtom(studentAtom);
  const [allEvaluations, setAllEvaluations] = useAtom(evaluationAtom);
  const [schoolScales, setSchoolScales] = useState<string[]>([]);

  const evaluations =
    student.evaluation?.abilities?.map((a) => a.abilityId) || [];

  useEffect(() => {
    getSchoolScales().then((res) => {
      const dimensions = res.dimensions || [];
      setSchoolScales(dimensions);

      setActiveTab(
        tabs.find((tab) => dimensions.includes(tab.type))?.id || 'guide'
      );
    });
  }, []);

  return (
    <div
      className={clsx(
        'overflow-hidden bg-white',
        !readonly &&
          'rounded-lg border border-gray-100 shadow-sm transition-shadow hover:shadow-lg'
      )}
      key={student.id}
    >
      {/* 学生基本信息和简化版能力图标 */}
      <button
        className="flex w-full items-center justify-between px-3 py-3"
        onClick={(e) => toggleStudentExpand(student.id, e)}
        type="button"
      >
        <button
          className="flex items-center"
          onClick={(e) => {
            e.stopPropagation();
            router.push(
              `/pbl/record/detail/student?observationId=${observationId}&studentId=${student.id}`
            );
          }}
          type="button"
        >
          <picture>
            <img
              alt={student.name}
              className="mr-2 h-10 w-10 rounded-full border-2 border-white object-cover shadow-sm"
              src={student.avatar}
            />
          </picture>
          <div className="flex flex-grow items-center">
            {student.name}
            <ChevronRight className="h-5 w-5 text-gray-400" />
          </div>
        </button>
        <div className="flex items-center justify-end">
          {student.evaluation && !readonly && (
            <button
              className="ml-2 rounded-full p-1 hover:bg-gray-100"
              onClick={(e) => toggleStudentExpand(student.id, e)}
              type="button"
            >
              {isStudentExpanded(student.id) ? (
                <ChevronUp className="h-5 w-5 text-gray-400" />
              ) : (
                <ChevronDown className="h-5 w-5 text-gray-400" />
              )}
            </button>
          )}
        </div>
      </button>
      {readonly && student.evaluation?.observationText && (
        <div className="px-3 py-2">
          <div className="">{student.evaluation?.observationText || ''}</div>
        </div>
      )}

      {/* 展开后的能力详情 */}
      {isStudentExpanded(student.id) && student.evaluation && (
        <div className="border-gray-100 border-t px-4 pt-2 pb-4">
          <div className="mb-3 flex items-center justify-between">
            <div className="flex items-center justify-between gap-2 text-small text-stone-600">
              <ClipboardList className="h-4 w-4 text-amber-500" />
              <span>关联能力</span>
            </div>
            {!readonly && (
              <button
                className="text-indigo-500 text-xs"
                onClick={() => {
                  setStudent(student);
                  setAllEvaluations({
                    ...allEvaluations,
                    [student.id]: student.evaluation?.abilities?.reduce<
                      Record<string, boolean>
                    >((acc, cur) => {
                      acc[cur.abilityId] = true;
                      return acc;
                    }, {}),
                  });
                  router.push('/pbl/record/update/evaluation?isEdit=true');
                }}
                type="button"
              >
                编辑关联能力
              </button>
            )}
          </div>
          {/* Tab 切换组件 */}
          <div className="mb-4">
            <div
              className="scrollbar-hide flex gap-2 overflow-x-auto whitespace-nowrap"
              style={{
                scrollbarWidth: 'none',
                WebkitOverflowScrolling: 'touch',
              }}
            >
              {tabs
                .filter((tab) => schoolScales.includes(tab.type))
                .map((tab) => (
                  <button
                    className={clsx(
                      'rounded-full bg-gray-100 px-3 py-0.5 text-xs',
                      activeTab === tab.id
                        ? 'bg-indigo-400 text-white shadow-sm'
                        : 'bg-gray-200 text-stone-900'
                    )}
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    type="button"
                  >
                    {tab.label}
                  </button>
                ))}
            </div>
          </div>

          {/* 根据选中的 Tab 显示对应内容 */}
          {activeTab === 'guide' && (
            <DevelopmentGuide evaluations={evaluations} />
          )}
          {activeTab === 'leuven' && <LeuvenScales evaluations={evaluations} />}
          {activeTab === 'bloom' && <Bloom />}
          {activeTab === 'deeperLearning' && (
            <DeeperLearning evaluations={evaluations} />
          )}
        </div>
      )}
    </div>
  );
}

export default memo(StudentCard);
