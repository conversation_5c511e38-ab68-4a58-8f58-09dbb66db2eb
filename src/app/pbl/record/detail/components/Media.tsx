import { Mic, PlayCircle } from 'lucide-react';
import type React from 'react';
import { useState } from 'react';
import MediaPreview from '@/components/UploadFile/components/MediaPreview';
import type { FileType } from '@/components/UploadFile/types';
import { formatDuration } from '@/utils';

interface MediaItem {
  mediaId?: number;
  url: string;
  name?: string;
  duration?: string | number;
  type: number | string;
  cover?: string;
}

function Media({ media }: { media: MediaItem[] }) {
  console.log('🚀 ~ media:', media);

  const mediaList: FileType[] = media.map((item) => {
    let type: 'image' | 'video' | 'audio';
    if (item.type === 1) {
      type = 'image';
    } else if (item.type === 2) {
      type = 'video';
    } else {
      type = 'audio';
    }
    return {
      ...item,
      id: String(item.mediaId || ''),
      mediaId: item.mediaId !== undefined ? String(item.mediaId) : undefined,
      type,
      duration:
        typeof item.duration === 'string'
          ? Number.parseInt(item.duration, 10)
          : item.duration,
    };
  });

  const [previewFile, setPreviewFile] = useState<FileType | null>(null);

  // 打开预览
  const handlePreview = (e: React.MouseEvent, file: FileType) => {
    e.stopPropagation();
    setPreviewFile({
      status: 'success',
      progress: 100,
      url: file.url,
      type: file.type,
      id: file.id,
      name: file.name || '',
      duration: file.duration,
    });
  };

  // 关闭预览
  const handleClosePreview = () => {
    setPreviewFile(null);
  };

  // 渲染媒体预览
  const renderMediaPreview = (media: FileType) => {
    if (media.type === 'image') {
      return (
        <button
          className="relative overflow-hidden rounded-lg bg-slate-100"
          onClick={(e) => handlePreview(e, media)}
          type="button"
        >
          <img
            alt=""
            className="aspect-[4/3] w-full object-cover"
            src={media.url || ''}
          />
        </button>
      );
    }

    return (
      <button
        className="relative flex aspect-[4/3] w-full items-center justify-center overflow-hidden rounded-lg bg-slate-100 "
        onClick={(e) => handlePreview(e, media)}
        type="button"
      >
        {media.type === 'video' && (
          <>
            <img
              alt=""
              className="absolute aspect-[4/3] w-full object-cover"
              src={
                media.cover ||
                `${media.url}?x-workflow-graph-name=video-thumbnail`
              }
            />
            <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-20">
              <PlayCircle className="h-8 w-8 text-white opacity-80" />
            </div>
          </>
        )}
        {media.type === 'audio' && (
          <button
            className="flex flex-col items-center justify-center"
            onClick={(e) => handlePreview(e, media)}
            type="button"
          >
            <Mic className="mb-1 h-8 w-8 text-primary" />
            <span className="text-xs">{formatDuration(media.duration)}</span>
          </button>
        )}
      </button>
    );
  };

  return (
    <div>
      {/* 媒体内容 */}
      {mediaList && mediaList.length > 0 && (
        <div className="mb-4">
          <div className="grid grid-cols-3 gap-2">
            {mediaList.map((item) => (
              <div className="media-item" key={item.mediaId}>
                {renderMediaPreview(item)}
                <p className="mt-1 truncate text-xs">{item.name || ''}</p>
              </div>
            ))}
          </div>
        </div>
      )}
      {/* 媒体预览模态框 */}
      {previewFile && (
        <MediaPreview file={previewFile} onClose={handleClosePreview} />
      )}
    </div>
  );
}

export default Media;
