'use client';

import { useQuery } from '@tanstack/react-query';
import { SpinLoading, Toast } from 'antd-mobile';
import clsx from 'clsx';
import { CalendarRange, ClipboardList, Edit3, File } from 'lucide-react';
import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { getDailyObservationDetail, updateDailyObservation } from '@/api/pbl';
import Media from '@/app/pbl/record/detail/components/Media';
import EditTextDialog from './components/EditTextDialog';

interface Student {
  id: string;
  name: string;
  avatar: string;
}

interface AssociatedStudentsProps {
  students: Student[];
}

interface MediaItem {
  type: number | string;
  url: string;
  cover?: string;
  thumbnail?: string;
  caption?: string;
  duration?: number | string;
  name?: string;
  mediaId?: string;
}

interface RecordData {
  summaryId?: string;
  summaryDate: string;
  observationIds?: number[];
  suggest: string;
  summary: string;
  createTime?: string;
  updateTime?: string;
  projectId?: string;
  observationCnt?: number;
  mediaList: MediaItem[];
  students: Student[];
}

// 关联学生列表
function AssociatedStudents({ students }: AssociatedStudentsProps) {
  return (
    <div className="mb-6 px-4">
      <h2 className="mb-2 font-semibold text-base">关联学生</h2>
      <div className="mb-2 flex flex-wrap gap-4">
        {students.map((student) => (
          <div
            className="flex cursor-pointer flex-col items-center rounded-lg py-1 shadow-card transition-all"
            key={student.id}
          >
            <div
              className={clsx(
                'rounded-full border-4 border-transparent transition-all'
              )}
            >
              <img
                alt={student.name}
                className="h-12 w-12 rounded-full border-2 border-white shadow-sm"
                src={student.avatar}
              />
            </div>
            <p className={clsx('mt-1 font-medium text-sm transition-colors')}>
              {student.name}
            </p>
          </div>
        ))}
      </div>
    </div>
  );
}

// 默认数据
const defaultRecord: RecordData = {
  summaryDate: '',
  summary: '',
  suggest: '',
  mediaList: [],
  students: [],
};

export default function App() {
  const searchParams = useSearchParams();
  const id = searchParams?.get('id') || '';
  const [isEditRecordDialogVisible, setIsEditRecordDialogVisible] =
    useState(false);
  const [isEditSuggestDialogVisible, setIsEditSuggestDialogVisible] =
    useState(false);
  // 使用 React Query 获取数据
  const {
    data: record,
    isLoading,
    error,
  } = useQuery<RecordData>({
    queryKey: ['dailyObservation', id],
    queryFn: async () => {
      if (!id) return defaultRecord;
      const response = await getDailyObservationDetail(id);
      //@ts-expect-error
      return (response as RecordData) || defaultRecord;
    },
    enabled: !!id, // 只有当 id 存在时才执行查询，
  });

  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = '观察记录详情';
    }
  }, []);

  // 格式化日期
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return `${date.getFullYear()} 年 ${date.getMonth() + 1} 月 ${date.getDate()} 日`;
    } catch (e) {
      return dateString;
    }
  };

  const onRecordUpdate = async (text: string | undefined) => {
    if (!text) return;
    await updateDailyObservation(id, {
      summary: text,
      suggest: record?.suggest,
    });
    Toast.show({
      content: '更新成功',
    });
  };

  const onSuggestUpdate = async (text: string | undefined) => {
    if (!text) return;
    await updateDailyObservation(id, {
      suggest: text,
      summary: record?.summary,
    });
    Toast.show({
      content: '更新成功',
    });
  };

  // 如果正在加载，显示加载指示器
  if (isLoading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <SpinLoading color="primary" />
      </div>
    );
  }

  // 如果出错，显示错误信息
  if (error) {
    return (
      <div className="flex h-screen items-center justify-center">
        <p className="text-red-500">加载失败，请稍后重试</p>
      </div>
    );
  }

  // 如果没有数据
  if (!record) {
    return (
      <div className="flex h-screen items-center justify-center">
        <p>未找到观察记录</p>
      </div>
    );
  }

  return (
    // 使用指定的尺寸和样式
    <div className="relative flex flex-col overflow-hidden">
      {/* 可滚动内容区域 */}
      <div className="flex-grow overflow-y-auto pb-20">
        {/* 观察记录内容 */}
        <div className="my-6 px-4">
          <h1 className="mb-4 font-bold text-2xl">
            {formatDate(record.summaryDate)} 观察记录
          </h1>

          <h3 className="mb-2 flex items-center gap-2 font-bold text-gray-700 text-md">
            <ClipboardList className="h-5 w-5 text-amber-500" />
            <span>观察实录</span>
          </h3>
          <div className="relative">
            <div className="text-sm leading-relaxed">
              {record.summary}{' '}
              <button
                className="inline-block p-1 text-gray-500 transition-colors hover:text-primary"
                onClick={() => setIsEditRecordDialogVisible(true)}
                type="button"
              >
                <Edit3 className="h-4 w-4 text-blue-500" />
              </button>
            </div>
          </div>
        </div>
        <AssociatedStudents students={record.students} />

        <div className="px-4">
          <h3 className="mb-3 flex items-center gap-2 font-bold text-gray-700 text-md">
            <File className="h-5 w-5 text-green-500" />
            <span>关联文件</span>
            <span className="font-normal text-gray-500 text-xs">
              ({record.mediaList.length})
            </span>
          </h3>
          <Media media={record.mediaList} />
        </div>
        {/* 下一步计划 */}
        <div className="mb-6 px-4">
          <h2 className="mb-2 flex items-center gap-2 font-semibold text-base">
            <CalendarRange className="h-4 w-4 text-teal-400" />
            下一步计划
          </h2>

          <div className="rounded-lg border border-teal-200 bg-teal-50 p-3">
            <div className="whitespace-pre-line text-gray-700 text-sm">
              {record.suggest}
              <button
                className="inline-block p-1 text-gray-500 transition-colors hover:text-primary"
                onClick={() => setIsEditSuggestDialogVisible(true)}
                type="button"
              >
                <Edit3 className="h-4 w-4 text-blue-500" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* 编辑记录内容对话框 */}
      <EditTextDialog
        emptyMessage="记录内容不能为空"
        initialContent={record.summary}
        onClose={() => setIsEditRecordDialogVisible(false)}
        onSave={async (content) => {
          await onRecordUpdate(content);
        }}
        onSuccess={(newContent) => {
          // 更新本地记录内容
          record.summary = newContent;
        }}
        placeholder="请输入记录内容"
        title="编辑记录内容"
        visible={isEditRecordDialogVisible}
      />

      {/* 编辑下一步计划对话框 */}
      <EditTextDialog
        emptyMessage="下一步计划不能为空"
        initialContent={record.suggest}
        onClose={() => setIsEditSuggestDialogVisible(false)}
        onSave={async (content) => {
          await onSuggestUpdate(content);
        }}
        onSuccess={(newContent) => {
          // 更新本地记录内容
          record.suggest = newContent;
        }}
        placeholder="请输入下一步计划"
        title="编辑下一步计划"
        visible={isEditSuggestDialogVisible}
      />
    </div>
  );
}
