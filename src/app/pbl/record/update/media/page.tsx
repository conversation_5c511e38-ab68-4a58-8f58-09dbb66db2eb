'use client';

import { Toast } from 'antd-mobile';
import { useAtom } from 'jotai';
import { Check, File } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useRef } from 'react';
import { deleteMaterial, uploadMaterial } from '@/api/pbl';
import Upload, { type FileType, type UploadRef } from '@/components/UploadFile';
import { mediaAtom } from '@/store/pbl';

const MediaPage = () => {
  const [media] = useAtom(mediaAtom);
  const router = useRouter();
  const uploadRef = useRef<UploadRef>(null);
  const submit = () => {
    const files = uploadRef.current?.getFiles();
    console.log('files', files);
    const medias = files
      ?.filter((item) => !item.mediaId)
      .map((item) => ({
        ...item,
        cover:
          item.type === 'video'
            ? `${item.url}?x-workflow-graph-name=video-thumbnail`
            : '',
        fileSize: item.size || 0,
        videoPlayType: 1,
        name: item.name || '未命名文件',
        type:
          item.type === 'image'
            ? 1
            : item.type === 'video'
              ? 2
              : item.type === 'audio'
                ? 3
                : 0,
      }));
    if (medias?.length === 0) {
      router.back();
      return;
    }

    uploadMaterial({
      observationId: media.observationId,
      deptId: media.deptId,
      medias,
    }).then(() => {
      Toast.show({
        content: '保存成功',
        icon: 'success',
      });
      router.back();
    });
  };

  const handleDelete = (id: string) => {
    deleteMaterial(id);
  };

  return (
    <div className="fade-in p-4">
      <h3 className="mb-3 flex items-center gap-2 font-bold text-gray-700 text-md">
        <File className="h-5 w-5 text-green-500" />
        <span>关联文件</span>
        <span className="font-normal text-gray-500 text-xs">
          ({media.medias.length})
        </span>
      </h3>
      <Upload
        className="min-h-[400px]"
        initialFiles={media.medias as unknown as FileType[]}
        onDelete={(id) => {
          handleDelete(id);
        }}
        ref={uploadRef}
      />
      <div className="fixed right-0 bottom-0 left-0 flex items-center justify-center pb-3">
        <button
          className="rounded-full bg-indigo-500 px-8 py-3 font-medium text-base text-white shadow-none focus:outline-none focus:ring-4 dark:focus:ring-blue-800"
          onClick={submit}
          type="button"
        >
          <div className="flex items-center justify-center">
            <Check /> <span className="ml-1">保存</span>
          </div>
        </button>
      </div>
    </div>
  );
};

export default MediaPage;
