'use client';

import {
  Button,
  DatePicker,
  Input,
  Card as MobileCard,
  Space,
  TextArea,
  Toast,
} from 'antd-mobile';
import clsx from 'clsx';
import { format } from 'date-fns';
import {
  Calendar,
  HelpCircle,
  ImagePlus,
  Plus,
  Sparkles,
  Tag,
  Target,
  Users,
  X,
} from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useCallback, useEffect, useState } from 'react';
import { useImmer } from 'use-immer';
import { createProject, getProjectDetail, updateProject } from '@/api/pbl';
import ClassPicker from '@/components/ClassPicker';
import AIProjectGenerator from './components/AIProjectGenerator';
import CoverImagePicker from './components/CoverImagePicker';

// 顶层正则常量，避免在函数内重复创建

interface ProjectForm {
  topic: string;
  briefIntro?: string;
  title: string;
  description: string;
  objectivesText: string;
  drivingQuestions: string[];
  tags: string[]; // 新增：内容标签
  startDate: Date | null;
  endDate: Date | null;
  cover?: string; // 新增：项目封面
  deptId?: string; // 新增：班级ID
  className?: string; // 新增：班级名称
}

function SectionTitle({
  icon,
  title,
  hint,
  accent = 'from-indigo-500/80 via-fuchsia-500/70 to-pink-500/70',
}: {
  icon: React.ReactNode;
  title: string;
  hint?: string;
  accent?: string;
}) {
  return (
    <div className="mb-4 flex items-center">
      <div
        className={clsx(
          'mr-3 flex aspect-square h-9 w-9 shrink-0 items-center justify-center overflow-hidden rounded-lg',
          'bg-gradient-to-br text-white shadow-[0_8px_24px_-8px_rgba(99,102,241,0.45)]',
          accent
        )}
      >
        {icon}
      </div>
      <div className="flex flex-col">
        <div className="font-semibold text-base text-gray-900 leading-none">
          {title}
        </div>
        {hint ? (
          <div className="mt-2 text-gray-500 text-xs leading-3">{hint}</div>
        ) : null}
      </div>
    </div>
  );
}

function CapsuleBadge({ index }: { index: number }) {
  return (
    <span
      className={clsx(
        'inline-flex select-none items-center justify-center',
        'h-6 min-w-6 rounded-full border border-gray-200 bg-white px-2',
        'font-medium text-gray-700 text-xs shadow-sm'
      )}
    >
      {index}
    </span>
  );
}

function CreateProjectPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const projectId = searchParams.get('projectId');
  const isEditMode = !!projectId;

  const [hasGenerated, setHasGenerated] = useState(false);
  const [coverPreview, setCoverPreview] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [startDateVisible, setStartDateVisible] = useState(false);
  const [endDateVisible, setEndDateVisible] = useState(false);
  const [tagInputValue, setTagInputValue] = useState('');
  const [form, setForm] = useImmer<ProjectForm>({
    topic: '',
    briefIntro: '',
    title: '',
    description: '',
    objectivesText: '',
    drivingQuestions: [''],
    tags: [],
    startDate: null,
    endDate: null,
    cover: '',
    deptId: '',
    className: '',
  });

  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = '创建项目';
    }
  }, []);

  useEffect(() => {
    if (isEditMode) {
      const fetchProjectDetails = async () => {
        try {
          // biome-ignore lint/suspicious/noExplicitAny: <explanation>
          const project: any = await getProjectDetail(projectId);
          setForm((draft) => {
            draft.title = project.projectName;
            draft.description = project.description;
            draft.objectivesText = project.learningGoals;
            draft.drivingQuestions = project.drivingQuestion
              ? project.drivingQuestion.split('\n')
              : [''];
            draft.tags = project.keyword
              ? project.keyword.split(',').filter((tag: string) => tag)
              : [];
            draft.startDate = project.startDate
              ? new Date(project.startDate)
              : null;
            draft.endDate = project.endDate ? new Date(project.endDate) : null;
            draft.cover = project.cover;
            draft.deptId = project.deptId;
            draft.className = project.dept?.name || '';
            draft.topic = ''; // Not available from API, reset
            draft.briefIntro = ''; // Not available from API, reset
          });
          setCoverPreview(project.cover);
          setHasGenerated(true); // Show form for editing
        } catch (error) {
          console.error('获取项目详情失败:', error);
          Toast.show('获取项目详情失败');
        }
      };
      fetchProjectDetails();
    }
  }, [isEditMode, projectId]);

  // AI 生成成功回调
  const handleAIGenerated = useCallback((data: Partial<ProjectForm>) => {
    setForm((draft) => {
      Object.assign(draft, data);
    });
    setHasGenerated(true);
  }, []);

  // 手动创建回调
  const handleManualCreate = useCallback(() => {
    setHasGenerated(true);
  }, []);

  const handleQuestionChange = useCallback((index: number, value: string) => {
    setForm((draft) => {
      draft.drivingQuestions[index] = value;
    });
  }, []);

  const addQuestion = useCallback(() => {
    setForm((draft) => {
      draft.drivingQuestions.push('');
    });
  }, []);

  const removeQuestion = useCallback(
    (index: number) => {
      if (form.drivingQuestions.length > 1) {
        setForm((draft) => {
          draft.drivingQuestions.splice(index, 1);
        });
      } else {
        Toast.show('至少保留一个驱动性问题');
      }
    },
    [form.drivingQuestions.length]
  );

  // 班级选择处理函数
  const handleClassChange = useCallback(
    (classId: string, className: string) => {
      setForm((draft) => {
        draft.deptId = classId;
        draft.className = className;
      });
    },
    []
  );

  // 封面选择处理函数
  const handleCoverChange = useCallback((coverUrl: string) => {
    setForm((draft) => {
      draft.cover = coverUrl;
    });
    setCoverPreview(coverUrl);
  }, []);

  const handleSubmit = async () => {
    // 表单验证
    if (!form.title.trim()) {
      Toast.show('请输入项目标题');
      return;
    }
    if (!form.description.trim()) {
      Toast.show('请输入项目描述');
      return;
    }
    if (!form.deptId) {
      Toast.show('请选择关联班级');
      return;
    }
    if (!(form.startDate && form.endDate)) {
      Toast.show('请选择项目开始和结束时间');
      return;
    }
    if (form.startDate >= form.endDate) {
      Toast.show('结束时间必须晚于开始时间');
      return;
    }

    setIsSubmitting(true);
    try {
      // 准备提交数据
      const projectData = {
        projectName: form.title,
        description: form.description,
        learningGoals: form.objectivesText,
        drivingQuestion: form.drivingQuestions
          .filter((q) => q.trim())
          .join('\n'),
        startDate: format(form.startDate, 'yyyy-MM-dd'),
        endDate: format(form.endDate, 'yyyy-MM-dd'),
        deptId: form.deptId,
        cover: form.cover || '',
        keyword: form.tags.join(','),
      };

      if (isEditMode) {
        await updateProject(projectId, projectData);
        Toast.show('项目更新成功！');
        router.replace(`/pbl/detail?projectId=${projectId}`);
      } else {
        const response = await createProject({ project: projectData });
        // @ts-expect-error - API响应类型暂时忽略
        const projectResult = response.project;
        if (projectResult?.projectId) {
          Toast.show('项目创建成功！');
          router.replace(`/pbl/detail?projectId=${projectResult.projectId}`);
        } else {
          throw new Error('创建项目失败');
        }
      }
    } catch (error) {
      console.error(isEditMode ? '更新项目失败:' : '创建项目失败:', error);
      Toast.show(isEditMode ? '更新项目失败，请重试' : '创建项目失败，请重试');
    } finally {
      setIsSubmitting(false);
    }
  };

  const titleCount = form.title.trim().length;
  const descCount = form.description.trim().length;

  return (
    <div
      className={clsx(
        'relative min-h-screen',
        // 背景：纸感噪点 + 流体渐变
        'bg-[radial-gradient(1200px_600px_at_100%_-10%,rgba(168,85,247,0.10),transparent),radial-gradient(1200px_600px_at_-10%_10%,rgba(59,130,246,0.10),transparent)]',
        'antialiased'
      )}
    >
      {/* 页面主体 */}
      <div className="mx-auto max-w-3xl px-4 pt-4 pb-28">
        {/* AI 生成 */}
        {!(isEditMode || hasGenerated) && (
          <AIProjectGenerator
            onGenerated={handleAIGenerated}
            onManualCreate={handleManualCreate}
          />
        )}

        {/* 基本信息 */}
        {hasGenerated && (
          <div className="mt-4 space-y-4">
            <MobileCard className="rounded-2xl border border-white/60 bg-white/80 shadow-[0_10px_30px_-12px_rgba(99,102,241,0.15)] backdrop-blur supports-[backdrop-filter]:bg-white/70">
              <div className="p-2">
                <SectionTitle
                  accent="from-amber-500/80 via-rose-500/70 to-purple-500/70"
                  hint="为项目命名并补充描述信息"
                  icon={<Sparkles className="h-5 w-5 shrink-0" />}
                  title="项目基本信息"
                />
                <Space block className="gap-3" direction="vertical">
                  <div>
                    <div className="mb-2 flex items-center justify-between">
                      <div className="font-medium text-gray-700 text-sm">
                        项目标题 <span className="text-rose-500">*</span>
                      </div>
                      <div
                        className={clsx(
                          'text-xs',
                          titleCount ? 'text-gray-400' : 'text-gray-300'
                        )}
                      >
                        {titleCount}/60
                      </div>
                    </div>
                    <Input
                      clearable
                      onChange={(value) =>
                        setForm((draft) => {
                          draft.title = value.slice(0, 60);
                        })
                      }
                      placeholder="请输入项目标题（不超过 30 字）"
                      value={form.title}
                    />
                  </div>

                  <div>
                    <div className="mb-2 flex items-center justify-between">
                      <div className="font-medium text-gray-700 text-sm">
                        项目描述 <span className="text-rose-500">*</span>
                      </div>
                      <div
                        className={clsx(
                          'text-xs',
                          descCount ? 'text-gray-400' : 'text-gray-300'
                        )}
                      >
                        {descCount}/300
                      </div>
                    </div>
                    <TextArea
                      onChange={(value) =>
                        setForm((draft) => {
                          draft.description = value.slice(0, 300);
                        })
                      }
                      placeholder="请描述项目的主要内容（建议 100~300 字）"
                      rows={4}
                      showCount
                      value={form.description}
                    />
                  </div>
                </Space>
              </div>
            </MobileCard>

            {/* 驱动性问题 */}
            <MobileCard className="rounded-2xl border border-white/60 bg-white/80 shadow-[0_10px_30px_-12px_rgba(99,102,241,0.15)] backdrop-blur supports-[backdrop-filter]:bg-white/70">
              <div className="p-2">
                <SectionTitle
                  accent="from-teal-500/80 via-emerald-500/70 to-lime-500/70"
                  hint="提出能引发探索与讨论的问题"
                  icon={<HelpCircle className="h-5 w-5 shrink-0" />}
                  title="驱动性问题"
                />
                <Space block className="gap-3" direction="vertical">
                  {form.drivingQuestions.map((question, index) => (
                    <div
                      className="group flex items-center gap-2"
                      key={`dq-${index}`}
                    >
                      <CapsuleBadge index={index + 1} />
                      <div className="flex-1">
                        <TextArea
                          autoSize={{ minRows: 1, maxRows: 3 }}
                          maxLength={100}
                          onChange={(value) =>
                            handleQuestionChange(index, value)
                          }
                          placeholder={`驱动性问题 ${index + 1}`}
                          rows={1}
                          style={{ resize: 'none' }}
                          value={question}
                        />
                      </div>
                      <button
                        className="inline-flex h-7 items-center justify-center rounded-xl px-2.5 text-rose-600 text-xs opacity-90 transition hover:bg-rose-50/70 hover:opacity-100"
                        onClick={() => removeQuestion(index)}
                        type="button"
                      >
                        <X className="h-4 w-4" />
                      </button>
                    </div>
                  ))}
                  <Button
                    block
                    color="primary"
                    fill="outline"
                    onClick={addQuestion}
                    shape="rounded"
                    size="small"
                  >
                    <span className="inline-flex items-center">
                      <Plus className="mr-1 h-4 w-4" />
                      添加驱动性问题
                    </span>
                  </Button>
                </Space>
              </div>
            </MobileCard>

            {/* 学习目标 */}
            <MobileCard className="rounded-2xl border border-white/60 bg-white/80 shadow-[0_10px_30px_-12px_rgba(99,102,241,0.15)] backdrop-blur supports-[backdrop-filter]:bg-white/70">
              <div className="p-2">
                <SectionTitle
                  accent="from-sky-500/80 via-cyan-500/70 to-emerald-500/70"
                  hint="请填写项目希望达成的学习成果"
                  icon={<Target className="h-5 w-5 shrink-0" />}
                  title="学习目标"
                />
                <Space block className="gap-3" direction="vertical">
                  <TextArea
                    maxLength={800}
                    onChange={(value) =>
                      setForm((draft) => {
                        draft.objectivesText = value;
                      })
                    }
                    placeholder="请以多行方式填写学习目标"
                    rows={5}
                    showCount
                    value={form.objectivesText}
                  />
                </Space>
              </div>
            </MobileCard>

            {/* 内容标签 */}
            <MobileCard className="mt-4 rounded-2xl border border-white/60 bg-white/80 shadow-[0_10px_30px_-12px_rgba(99,102,241,0.15)] backdrop-blur supports-[backdrop-filter]:bg-white/70">
              <div className="p-2">
                <SectionTitle
                  accent="from-emerald-500/80 via-teal-500/70 to-cyan-500/70"
                  hint="选择主题标签，可点击推荐或自定义输入"
                  icon={<Tag className="h-5 w-5 shrink-0" />}
                  title="内容标签"
                />
                <Space block className="gap-3" direction="vertical">
                  {/* 推荐标签 */}
                  <div>
                    <div className="mb-2 font-medium text-gray-700 text-sm">
                      推荐标签
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {[
                        '自然',
                        '低碳',
                        '探究',
                        '团队',
                        '艺术',
                        '科学',
                        '社区',
                        '环保',
                      ].map((t) => {
                        const selected = form.tags.includes(t);
                        return (
                          <button
                            className={clsx(
                              'rounded-full border px-2.5 py-1 text-xs transition active:scale-[0.98]',
                              selected
                                ? 'border-emerald-300 bg-emerald-50 text-emerald-700'
                                : 'border-gray-200 bg-white text-gray-600 hover:border-gray-300 hover:text-gray-800'
                            )}
                            key={`rec-${t}`}
                            onClick={() =>
                              setForm((draft) => {
                                if (selected) {
                                  draft.tags = draft.tags.filter(
                                    (x) => x !== t
                                  );
                                } else {
                                  if (draft.tags.length >= 5) {
                                    Toast.show('最多添加 5 个标签');
                                    return;
                                  }
                                  draft.tags.push(t);
                                }
                              })
                            }
                            type="button"
                          >
                            {t}
                          </button>
                        );
                      })}
                    </div>
                  </div>

                  {/* 自定义标签输入 */}
                  <div>
                    <div className="mb-2 font-medium text-gray-700 text-sm">
                      自定义标签
                    </div>
                    <div className="flex items-center gap-2">
                      <Input
                        className="flex-1"
                        clearable
                        maxLength={8}
                        onChange={(value) => {
                          // 更新输入值到state，避免DOM查询
                          setTagInputValue(value);
                        }}
                        placeholder="输入标签名称"
                        value={tagInputValue}
                      />
                      <Button
                        color="primary"
                        fill="outline"
                        onClick={() => {
                          const raw = tagInputValue.trim();
                          if (!raw) {
                            Toast.show('请输入标签内容');
                            return;
                          }
                          const safe = raw.replace(/[^\u4e00-\u9fa5\w-]/g, '');
                          if (!safe) {
                            Toast.show('无效的标签内容');
                            return;
                          }
                          setForm((draft) => {
                            if (draft.tags.includes(safe)) {
                              Toast.show('标签已存在');
                              return;
                            }
                            if (draft.tags.length >= 5) {
                              Toast.show('最多添加 5 个标签');
                              return;
                            }
                            draft.tags.push(safe);
                          });
                          // 清空输入框
                          setTagInputValue('');
                        }}
                        shape="rounded"
                        size="mini"
                      >
                        添加
                      </Button>
                    </div>
                  </div>

                  {/* 已选择标签（胶囊展示，可删除） */}
                  <div>
                    <div className="mb-2 font-medium text-gray-700 text-sm">
                      已选择
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {form.tags.map((tag) => (
                        <span
                          className={clsx(
                            'inline-flex items-center gap-1 rounded-full border px-2 py-1 text-xs shadow-sm transition',
                            'border-gray-400 bg-white text-gray-700 hover:border-gray-300'
                          )}
                          key={`sel-${tag}`}
                        >
                          <span className="select-none">#</span>
                          <span>{tag}</span>
                          <button
                            aria-label={`移除标签 ${tag}`}
                            className="ml-1 rounded-full text-gray-400 transition hover:text-rose-500"
                            onClick={() =>
                              setForm((draft) => {
                                draft.tags = draft.tags.filter(
                                  (t) => t !== tag
                                );
                              })
                            }
                            type="button"
                          >
                            <X className="h-3.5 w-3.5" />
                          </button>
                        </span>
                      ))}
                    </div>
                  </div>
                </Space>
              </div>
            </MobileCard>

            {/* 项目封面 */}
            <MobileCard className="rounded-2xl border border-white/60 bg-white/80 shadow-[0_10px_30px_-12px_rgba(99,102,241,0.15)] backdrop-blur supports-[backdrop-filter]:bg-white/70">
              <div className="p-2">
                <SectionTitle
                  accent="from-fuchsia-500/80 via-purple-500/70 to-indigo-500/70"
                  hint="上传项目封面图，大小 ≤ 5MB"
                  icon={<ImagePlus className="h-5 w-5 shrink-0" />}
                  title="项目封面"
                />

                <CoverImagePicker
                  coverPreview={coverPreview}
                  onPickCover={handleCoverChange}
                  onRemoveCover={() => {
                    setCoverPreview(null);
                    setForm((draft) => {
                      draft.cover = '';
                    });
                  }}
                />
              </div>
            </MobileCard>

            {/* 班级选择 */}
            <MobileCard className="rounded-2xl border border-white/60 bg-white/80 shadow-[0_10px_30px_-12px_rgba(99,102,241,0.15)] backdrop-blur supports-[backdrop-filter]:bg-white/70">
              <div className="p-2">
                <SectionTitle
                  accent="from-emerald-500/80 via-blue-500/70 to-purple-500/70"
                  hint="选择项目关联的班级"
                  icon={<Users className="h-5 w-5 shrink-0" />}
                  title="关联班级"
                />
                <Space block className="gap-3" direction="vertical">
                  <div>
                    <div className="mb-2 flex items-center justify-between">
                      <div className="font-medium text-gray-700 text-sm">
                        选择班级 <span className="text-rose-500">*</span>
                      </div>
                    </div>
                    <div className="rounded-xl border border-gray-200 bg-white px-4 py-3 transition-colors hover:border-gray-300">
                      <ClassPicker
                        onChange={handleClassChange}
                        placeholder="请选择年级/班级"
                        value={form.deptId}
                      />
                    </div>
                    {form.className && (
                      <div className="mt-2 text-gray-600 text-sm">
                        已选择：{form.className}
                      </div>
                    )}
                  </div>
                </Space>
              </div>
            </MobileCard>

            {/* 项目时间 */}
            <MobileCard className="rounded-2xl border border-white/60 bg-white/80 shadow-[0_10px_30px_-12px_rgba(99,102,241,0.15)] backdrop-blur supports-[backdrop-filter]:bg-white/70">
              <div className="p-2">
                <SectionTitle
                  accent="from-orange-500/80 via-amber-500/70 to-rose-500/70"
                  hint="选择项目起止日期，确保时间安排合理"
                  icon={<Calendar className="h-5 w-5 shrink-0" />}
                  title="项目时间"
                />
                <div className="grid grid-cols-1 gap-3 sm:grid-cols-2">
                  <div>
                    <div className="mb-2 font-medium text-gray-700 text-sm">
                      开始时间 <span className="text-rose-500">*</span>
                    </div>
                    <button
                      className="inline-flex h-10 w-full items-center justify-start rounded-xl border border-gray-200 bg-white px-3.5 text-left text-gray-700 text-sm transition-all duration-200 hover:border-gray-300 hover:text-gray-900"
                      onClick={() => setStartDateVisible(true)}
                      type="button"
                    >
                      {form.startDate
                        ? form.startDate.toLocaleDateString('zh-CN')
                        : '选择开始时间'}
                    </button>
                    <DatePicker
                      onCancel={() => setStartDateVisible(false)}
                      onClose={() => setStartDateVisible(false)}
                      onConfirm={(date) => {
                        setForm((draft) => {
                          draft.startDate = date;
                        });
                        setStartDateVisible(false);
                      }}
                      title="选择开始时间"
                      value={form.startDate}
                      visible={startDateVisible}
                    />
                  </div>
                  <div>
                    <div className="mb-2 font-medium text-gray-700 text-sm">
                      结束时间 <span className="text-rose-500">*</span>
                    </div>
                    <button
                      className="inline-flex h-10 w-full items-center justify-start rounded-xl border border-gray-200 bg-white px-3.5 text-left text-gray-700 text-sm transition-all duration-200 hover:border-gray-300 hover:text-gray-900"
                      onClick={() => setEndDateVisible(true)}
                      type="button"
                    >
                      {form.endDate
                        ? form.endDate.toLocaleDateString('zh-CN')
                        : '选择结束时间'}
                    </button>
                    <DatePicker
                      onCancel={() => setEndDateVisible(false)}
                      onClose={() => setEndDateVisible(false)}
                      onConfirm={(date) => {
                        setForm((draft) => {
                          draft.endDate = date;
                        });
                        setEndDateVisible(false);
                      }}
                      title="选择结束时间"
                      value={form.endDate}
                      visible={endDateVisible}
                    />
                  </div>
                </div>
                {form.startDate &&
                form.endDate &&
                form.startDate >= form.endDate ? (
                  <div className="mt-2 rounded-md border border-rose-200 bg-rose-50 px-2 py-1.5 text-rose-700 text-xs">
                    结束时间必须晚于开始时间
                  </div>
                ) : null}
              </div>
            </MobileCard>
          </div>
        )}

        {/* 底部提交条 */}
        {hasGenerated && (
          <div className="pointer-events-none fixed inset-x-0 bottom-0 z-40">
            <div className="mx-auto max-w-3xl px-4 pb-safe">
              <div className="pointer-events-auto mb-3 rounded-2xl">
                <div className="flex flex-col items-center gap-2">
                  <Button
                    block
                    className="w-full sm:w-auto sm:min-w-[160px]"
                    color="primary"
                    loading={isSubmitting}
                    onClick={handleSubmit}
                    shape="rounded"
                    size="large"
                  >
                    {isEditMode ? '更新项目' : '创建项目'}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default CreateProjectPage;
