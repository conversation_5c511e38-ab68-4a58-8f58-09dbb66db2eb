import type { JSONContent } from '@tiptap/core';

export const defaultPblData = [
  {
    id: '421c6552-71c2-401d-8c08-f20b2690a1ec',
    type: 'heading',
    props: {
      textColor: 'default',
      backgroundColor: 'default',
      textAlignment: 'left',
      level: 2,
    },
    content: [
      {
        type: 'text',
        text: '一、项目缘起',
        styles: {},
      },
    ],
    children: [],
  },
  {
    id: 'eca387cc-5d35-4ee3-acbb-1512c0f0e062',
    type: 'paragraph',
    props: {
      textColor: 'default',
      backgroundColor: 'default',
      textAlignment: 'left',
    },
    content: [],
    children: [],
  },
  {
    id: 'cb05000d-2307-4941-a655-e3f60bc85cc1',
    type: 'heading',
    props: {
      textColor: 'default',
      backgroundColor: 'default',
      textAlignment: 'left',
      level: 2,
    },
    content: [
      {
        type: 'text',
        text: '二、项目发展实录',
        styles: {},
      },
    ],
    children: [],
  },
  {
    id: 'beb547d0-63bf-45a9-a9ad-baec4c9d6406',
    type: 'paragraph',
    props: {
      textColor: 'default',
      backgroundColor: 'default',
      textAlignment: 'left',
    },
    content: [],
    children: [],
  },
  {
    id: '69277b4e-522d-4e79-becb-b0527d73a168',
    type: 'heading',
    props: {
      textColor: 'default',
      backgroundColor: 'default',
      textAlignment: 'left',
      level: 2,
    },
    content: [
      {
        type: 'text',
        text: '三、项目总结与反思',
        styles: {},
      },
    ],
    children: [],
  },
  {
    id: 'c3c9f2c2-9ec7-4f67-9958-efb628899956',
    type: 'paragraph',
    props: {
      textColor: 'default',
      backgroundColor: 'default',
      textAlignment: 'left',
    },
    content: [],
    children: [],
  },
];

export const initData: JSONContent = {
  type: 'doc',
  content: [
    {
      type: 'heading',
      attrs: {
        textAlign: null,
        level: 2,
      },
      content: [
        {
          type: 'text',
          marks: [
            {
              type: 'bold',
            },
          ],
          text: '一、项目缘起',
        },
      ],
    },
    {
      type: 'paragraph',
      attrs: {
        textAlign: null,
      },
      content: [
        {
          type: 'text',
          text: '[内容]',
        },
      ],
    },
    {
      type: 'heading',
      attrs: {
        textAlign: null,
        level: 2,
      },
      content: [
        {
          type: 'text',
          marks: [
            {
              type: 'bold',
            },
          ],
          text: '二、项目发展实录',
        },
      ],
    },
    {
      type: 'paragraph',
      attrs: {
        textAlign: null,
      },
      content: [
        {
          type: 'text',
          text: '[内容]',
        },
      ],
    },
    {
      type: 'heading',
      attrs: {
        textAlign: null,
        level: 2,
      },
      content: [
        {
          type: 'text',
          marks: [
            {
              type: 'bold',
            },
          ],
          text: '三、项目总结',
        },
      ],
    },
    {
      type: 'paragraph',
      attrs: {
        textAlign: null,
      },
      content: [
        {
          type: 'text',
          text: '[内容]',
        },
      ],
    },
  ],
};
