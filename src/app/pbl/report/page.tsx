'use client';

import type { JSONContent } from '@tiptap/core';
import type { DebouncedFunc } from 'lodash-es';
import { debounce } from 'lodash-es';
import { useSearchParams } from 'next/navigation';
import { useEffect, useMemo, useState } from 'react';

import { getPblContent, getProjectDetail, updatePblContent } from '@/api/pbl';
import { SimpleEditor } from '@/components/tiptap-templates/simple/simple-editor';
import { initData } from './data';

export default function Page() {
  const searchParams = useSearchParams();
  const projectId = searchParams?.get('projectId') || '';
  const [content, setContent] = useState<JSONContent | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (projectId) {
      init();
    }
  }, [projectId]);

  // 生成包含项目基本信息的初始化内容
  const generateInitDataWithProjectInfo = async (): Promise<JSONContent> => {
    console.log('🚀 ~ 开始获取项目详情并生成初始化内容');

    try {
      // 获取项目详情
      const projectDetailRes = await getProjectDetail(projectId);
      const projectInfo = projectDetailRes?.data || projectDetailRes;
      console.log('🚀 ~ projectInfo:', projectInfo);
      const projectInfoContent: JSONContent[] = [
        // 项目基本信息标题
        {
          type: 'heading',
          attrs: {
            textAlign: null,
            level: 1,
          },
          content: [
            {
              type: 'text',
              marks: [
                {
                  type: 'bold',
                },
              ],
              text: `${projectInfo?.projectName ? `“${projectInfo.projectName}”` : ''}项目报告`,
            },
          ],
        },
        {
          type: 'paragraph',
          attrs: {
            textAlign: null,
          },
          content: [
            {
              type: 'text',
              text: projectInfo?.description || '[项目描述]',
            },
          ],
        },
        {
          type: 'heading',
          attrs: {
            textAlign: null,
            level: 3,
          },
          content: [
            {
              type: 'text',
              marks: [
                {
                  type: 'bold',
                },
              ],
              text: '目标',
            },
          ],
        },
        {
          type: 'paragraph',
          attrs: {
            textAlign: null,
          },
          content: [
            {
              type: 'text',
              text: projectInfo?.learningGoals || '[项目目标]',
            },
          ],
        },
        {
          type: 'heading',
          attrs: {
            textAlign: null,
            level: 3,
          },
          content: [
            {
              type: 'text',
              marks: [
                {
                  type: 'bold',
                },
              ],
              text: '驱动性问题',
            },
          ],
        },
        {
          type: 'paragraph',
          attrs: {
            textAlign: null,
          },
          content: [
            {
              type: 'text',
              text: projectInfo?.drivingQuestion || '[驱动性问题]',
            },
          ],
        },
      ];

      // 将项目基本信息与原有的 initData 内容合并
      return {
        type: 'doc',
        content: [...projectInfoContent, ...(initData.content || [])],
      };
    } catch (error) {
      console.error('🚀 ~ 获取项目详情失败，使用默认初始化数据:', error);
      // 如果获取项目详情失败，返回原有的默认数据
      return initData;
    }
  };

  // 类型守卫函数，检查对象是否为有效的 JSONContent
  const isValidJSONContent = (obj: unknown): obj is JSONContent => {
    if (!obj || typeof obj !== 'object') {
      return false;
    }

    const candidate = obj as Record<string, unknown>;

    // JSONContent 必须有 type 属性
    if (typeof candidate.type !== 'string') {
      return false;
    }

    // 如果有 content 属性，必须是数组
    if (candidate.content !== undefined && !Array.isArray(candidate.content)) {
      return false;
    }

    // 如果有 attrs 属性，必须是对象
    if (
      candidate.attrs !== undefined &&
      (typeof candidate.attrs !== 'object' || candidate.attrs === null)
    ) {
      return false;
    }

    // 如果有 marks 属性，必须是数组
    if (candidate.marks !== undefined && !Array.isArray(candidate.marks)) {
      return false;
    }

    return true;
  };

  const init = async () => {
    setIsLoading(true);

    try {
      // 获取 PBL 内容
      const res = await getPblContent(projectId);
      const contentData = res?.data || res;

      try {
        if (contentData?.content) {
          // 如果有内容，直接解析使用
          const parsed = JSON.parse(contentData.content);

          // 只有当 parsed 是有效的 JSONContent 类型时才设置内容
          if (isValidJSONContent(parsed)) {
            setContent(parsed);
          } else {
            console.warn(
              '解析的内容不是有效的 JSONContent 格式，使用包含项目信息的默认数据'
            );
            const initContentWithProjectInfo =
              await generateInitDataWithProjectInfo();
            setContent(initContentWithProjectInfo);
          }
        } else {
          // 如果没有内容，使用包含项目基本信息的初始化数据
          const initContentWithProjectInfo =
            await generateInitDataWithProjectInfo();
          setContent(initContentWithProjectInfo);
        }
      } catch (error) {
        console.error('🚀 ~ 解析内容错误:', error);
        const initContentWithProjectInfo =
          await generateInitDataWithProjectInfo();
        setContent(initContentWithProjectInfo);
      }
    } catch (error) {
      console.error('🚀 ~ 获取PBL内容失败:', error);
      // 如果获取内容失败，使用原有的默认数据
      setContent(initData);
    } finally {
      setIsLoading(false);
    }
  };

  const debouncedOnChange: DebouncedFunc<(nextContent: JSONContent) => void> =
    useMemo(
      () =>
        debounce((nextContent: JSONContent) => {
          console.log('🚀 ~ content:', nextContent);
          updatePblContent(projectId, {
            content: JSON.stringify(nextContent),
          }).then(() => {
            console.log('🚀 ~ 更新完成');
          });
        }, 2000),
      [projectId]
    );

  useEffect(() => {
    // 组件卸载或项目切换时，取消未决的防抖调用，避免内存泄漏或误写
    return () => {
      debouncedOnChange.cancel();
    };
  }, [debouncedOnChange]);

  if (isLoading) {
    return (
      <div className="flex h-screen flex-col items-center justify-center">
        <div className="h-12 w-12 animate-spin rounded-full border-4 border-primary border-t-transparent" />
        <p className="mt-4">加载中...</p>
      </div>
    );
  }

  return (
    <>
      {content ? (
        <SimpleEditor
          content={content as JSONContent}
          onChange={debouncedOnChange}
          projectId={projectId}
        />
      ) : null}
    </>
  );
}
