'use client';

import {
  <PERSON><PERSON><PERSON>,
  Camera,
  ClipboardList,
  Eye,
  FileText,
  FolderOpen,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import ActionSheetButton from '../../record/list/components/AddRecordButton';

export default function QuickActions({
  classId,
  projectId,
  projectName,
}: {
  classId: string;
  projectId: string;
  projectName?: string;
}) {
  const router = useRouter();

  return (
    <div className="border-b">
      <div className="p-4">
        <div className="grid grid-cols-4 gap-3">
          {/* 添加记录按钮 - 使用 ActionSheet */}
          <ActionSheetButton classId={classId} projectId={projectId}>
            {(onClick) => (
              <button
                className="flex flex-col items-center gap-2 transition-all active:translate-y-0 active:opacity-90"
                onClick={onClick}
                type="button"
              >
                <div className="flex h-12 w-12 items-center justify-center rounded-full border border-blue-300 bg-blue-50 shadow-[0_1px_0_rgba(0,0,0,0.02)] transition-all group-hover:border-blue-400 group-hover:bg-blue-100 group-hover:shadow-md">
                  <Camera className="h-5 w-5 text-blue-600" />
                </div>
                <span className="font-medium text-gray-700 text-xs">
                  添加记录
                </span>
              </button>
            )}
          </ActionSheetButton>
          {/* 项目报告按钮 - 直接跳转 */}
          <button
            className="flex flex-col items-center gap-2 transition-all active:translate-y-0 active:opacity-90"
            onClick={() => {
              router.push(`/pbl/report?projectId=${projectId}`);
            }}
            type="button"
          >
            <div className="flex h-12 w-12 items-center justify-center rounded-full border border-purple-300 bg-purple-50 shadow-[0_1px_0_rgba(0,0,0,0.02)] transition-all group-hover:border-purple-400 group-hover:bg-purple-100 group-hover:shadow-md">
              <FileText className="h-5 w-5 text-purple-600" />
            </div>
            <span className="font-medium text-gray-700 text-xs">项目报告</span>
          </button>

          {/* 项目活动按钮 - 直接跳转 */}
          {/* <button
            className="flex flex-col items-center gap-2 transition-all active:translate-y-0 active:opacity-90"
            onClick={() => {
              router.push('/pbl-v2/task');
            }}
            type="button"
          >
            <div className="flex h-12 w-12 items-center justify-center rounded-full border border-emerald-300 bg-emerald-50 shadow-[0_1px_0_rgba(0,0,0,0.02)] transition-all group-hover:border-emerald-400 group-hover:bg-emerald-100 group-hover:shadow-md">
              <CalendarPlus className="h-5 w-5 text-emerald-600" />
            </div>
            <span className="font-medium text-gray-700 text-xs">项目活动</span>
          </button> */}

          <button
            className="flex flex-col items-center gap-2 transition-all active:translate-y-0 active:opacity-90"
            onClick={() => {
              router.push(
                `/pbl/detail/record?projectId=${projectId}${projectName ? `&projectName=${encodeURIComponent(projectName)}` : ''}`
              );
            }}
            type="button"
          >
            <div className="flex h-12 w-12 items-center justify-center rounded-full border border-teal-300 bg-teal-50 shadow-[0_1px_0_rgba(0,0,0,0.02)] transition-all group-hover:border-teal-400 group-hover:bg-teal-100 group-hover:shadow-md">
              <Eye className="h-5 w-5 text-teal-600" />
            </div>
            <span className="font-medium text-gray-700 text-xs">项目记录</span>
          </button>

          {/* 相关素材按钮 - 直接跳转 */}
          <button
            className="flex flex-col items-center gap-2 transition-all active:translate-y-0 active:opacity-90"
            onClick={() => {
              router.push(
                `/pbl/material?projectId=${projectId}&classId=${classId}`
              );
            }}
            type="button"
          >
            <div className="flex h-12 w-12 items-center justify-center rounded-full border border-orange-300 bg-orange-50 shadow-[0_1px_0_rgba(0,0,0,0.02)] transition-all group-hover:border-orange-400 group-hover:bg-orange-100 group-hover:shadow-md">
              <FolderOpen className="h-5 w-5 text-orange-600" />
            </div>
            <span className="font-medium text-gray-700 text-xs">项目素材</span>
          </button>
          <button
            className="flex flex-col items-center gap-2 transition-all active:translate-y-0 active:opacity-90"
            onClick={() => {
              router.push(`/pbl/detail/dayRecord?projectId=${projectId}`);
            }}
            type="button"
          >
            <div className="flex h-12 w-12 items-center justify-center rounded-full border border-indigo-300 bg-indigo-50 shadow-[0_1px_0_rgba(0,0,0,0.02)] transition-all group-hover:border-indigo-400 group-hover:bg-indigo-100 group-hover:shadow-md">
              <BookOpen className="h-5 w-5 text-indigo-600" />
            </div>
            <span className="font-medium text-gray-700 text-xs">每日汇总</span>
          </button>
          <button
            className="flex flex-col items-center gap-2 transition-all active:translate-y-0 active:opacity-90"
            onClick={() => {
              router.push(`/pbl/summary?projectId=${projectId}`);
            }}
            type="button"
          >
            <div className="flex h-12 w-12 items-center justify-center rounded-full border border-pink-300 bg-pink-50 shadow-[0_1px_0_rgba(0,0,0,0.02)] transition-all group-hover:border-pink-400 group-hover:bg-pink-100 group-hover:shadow-md">
              <ClipboardList className="h-5 w-5 text-pink-600" />
            </div>
            <span className="font-medium text-gray-700 text-xs">
              阶段性总结
            </span>
          </button>
        </div>
      </div>
    </div>
  );
}
