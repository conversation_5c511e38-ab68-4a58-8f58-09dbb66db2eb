'use client';

import { useInfiniteQuery } from '@tanstack/react-query';
import { InfiniteScroll } from 'antd-mobile';
import { useSearchParams } from 'next/navigation';
import { useEffect } from 'react';
import { getDailyObservationList } from '@/api/pbl';
import Empty from '@/components/Empty';
import RecordByDayItem from './components/RecordByDayItem';

// 媒体项类型定义
interface MediaItem {
  type: 'image' | 'video' | 'audio';
  url: string;
  caption: string;
  thumbnail?: string;
  duration?: string;
}

// 学生信息类型定义
interface Student {
  id: string;
  name: string;
  avatar: string;
}

// 观察记录类型
interface ObservationRecord {
  summaryId: string;
  summaryDate: string;
  observationIds: number[];
  suggest: string;
  summary: string;
  createTime: string;
  updateTime: string;
  projectId: string;
  observationCnt: number;
  mediaList: MediaItem[];
  students: Student[];
}

// 接口返回数据类型
interface ApiResponse {
  list: ObservationRecord[];
  total: number;
  pageSize?: number;
  pageNum?: number;
}

export default function Record() {
  const searchParams = useSearchParams();

  const projectId = searchParams?.get('projectId') || '';

  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = 'PBL 项目每日记录';
    }
  }, []);

  const { isLoading, data, fetchNextPage, hasNextPage, isFetchingNextPage } =
    useInfiniteQuery({
      queryKey: ['dailyObservations', projectId],
      queryFn: async ({ pageParam = 1 }) => {
        // 这里假设 API 支持分页，如果后端 API 不支持分页参数，可以在前端模拟分页
        const response = await getDailyObservationList(projectId);
        // 模拟分页，每页 10 条数据
        // @ts-expect-error - 忽略类型检查以避免 linter 错误
        const allRecords = response.list || [];
        const pageSize = 10;
        const startIndex = (pageParam - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        const paginatedList = allRecords.slice(startIndex, endIndex);

        return {
          list: paginatedList,
          total: allRecords.length,
          pageSize,
          pageNum: pageParam,
        } as ApiResponse;
      },
      enabled: !!projectId,
      refetchOnWindowFocus: false,
      staleTime: 5 * 60 * 1000, // 5 分钟内不重新获取数据
      getNextPageParam: (lastPage, allPages) => {
        // 计算下一页的页码
        const nextPage = allPages.length + 1;
        // 如果没有更多数据，返回 undefined，否则返回下一页页码
        return lastPage.list.length === 0 || lastPage.list.length < 10
          ? undefined
          : nextPage;
      },
    });

  if (isLoading) {
    return (
      <div className="flex h-screen flex-col items-center justify-center">
        <div className="h-12 w-12 animate-spin rounded-full border-4 border-primary border-t-transparent" />
        <p className="mt-4">加载中...</p>
      </div>
    );
  }

  // 合并所有页面的数据
  const observationRecords: ObservationRecord[] =
    data?.pages?.flatMap((page: ApiResponse) => page.list || []) || [];

  // 如果没有数据且不在加载中，则显示 Empty 组件
  if (!isLoading && observationRecords.length === 0) {
    return (
      <main className="flex flex-col items-center justify-center pt-20">
        <Empty title="暂无观察记录" />
        <div className="rounded-lg p-4 text-center text-gray-500 text-xs">
          每日观察记录会在每天的 23:00 自动生成
        </div>
      </main>
    );
  }

  // 加载更多数据的处理函数
  const loadMore = async () => {
    if (hasNextPage) {
      await fetchNextPage();
    }
  };

  return (
    <main className="min-h-main bg-slate-50">
      <div className=" flex flex-col">
        {/* 主内容区 */}
        <div className="custom-scrollbar flex-1 overflow-y-auto bg-neutral p-4">
          <div className="space-y-5">
            {observationRecords.map((record, index) => (
              <div key={record.summaryId}>
                <RecordByDayItem index={index} record={record} />
              </div>
            ))}

            {/* 使用 antd-mobile 的 InfiniteScroll 组件 */}
            <InfiniteScroll
              hasMore={!!hasNextPage}
              loadMore={loadMore}
              threshold={250}
            >
              {(() => {
                if (isFetchingNextPage) {
                  return (
                    <div className="flex flex-col items-center py-4">
                      <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent" />
                      <p className="mt-2 text-gray-500 text-sm">加载更多...</p>
                    </div>
                  );
                }

                if (hasNextPage) {
                  return (
                    <div className="py-6 text-center">
                      <p className="text-gray-500 text-sm">上拉加载更多</p>
                    </div>
                  );
                }

                return (
                  <div className="py-6 text-center">
                    <p className="text-gray-500 text-sm">没有更多数据了</p>
                  </div>
                );
              })()}
            </InfiniteScroll>
          </div>
        </div>
      </div>
    </main>
  );
}
