'use client';

import { useInfiniteQuery } from '@tanstack/react-query';
import type { Editor, JSONContent } from '@tiptap/core';
import {
  Button as AntdButton,
  Checkbox,
  ErrorBlock,
  InfiniteScroll,
  Loading,
  Popup,
  Tag,
  TextArea,
  Toast,
} from 'antd-mobile';
import clsx from 'clsx';
import { ChevronDown, ChevronUp, FileText as FileTextIcon } from 'lucide-react';
import type React from 'react';
import { useCallback, useMemo, useRef, useState } from 'react';

import { aiGenerateContent, getObservationList } from '@/api/pbl';
import type { ProjectContextStorage } from '@/components/tiptap-extensions/project-context-extension';
import type { ObservationRecord } from '@/components/tiptap-node/observation-insert-node/observation-insert-node-extension';

type Props = {
  editor: Editor | null;
  className?: string;
  buttonText?: string;
  onClick?: () => void;
};

const pageSize = 10;

const PhaseSummaryInsertButton: React.FC<Props> = ({
  editor,
  className,
  buttonText,
  onClick,
}) => {
  const [visible, setVisible] = useState(false);
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const [summaryText, setSummaryText] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [expandedRecords, setExpandedRecords] = useState<Set<string>>(
    new Set()
  );
  const [infiniteActive, setInfiniteActive] = useState(false);
  const scrollRef = useRef<HTMLDivElement>(null);

  const selectedSet = useMemo(() => new Set(selectedIds), [selectedIds]);

  const projectId = useMemo(() => {
    if (!editor) {
      return '';
    }
    try {
      return (
        (editor.storage as unknown as { projectContext: ProjectContextStorage })
          .projectContext?.projectId || ''
      );
    } catch {
      return '';
    }
  }, [editor]);

  const {
    data: infiniteObservationData,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    error,
    refetch,
  } = useInfiniteQuery({
    queryKey: ['phaseSummaryObservationList', projectId],
    queryFn: async ({ pageParam = 1 }) => {
      if (!projectId) {
        return { data: [], page: pageParam, hasMore: false };
      }

      const response = await getObservationList({
        projectId,
        page: pageParam,
        perPage: pageSize,
      });
      // @ts-expect-error
      const records = response?.list || [];

      return {
        data: records,
        page: pageParam,
        hasMore: records.length === pageSize,
      };
    },
    getNextPageParam: (lastPage) => {
      if (lastPage?.hasMore) {
        return lastPage.page + 1;
      }
      return;
    },
    enabled: !!projectId && visible,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
  });

  const observationRecords: ObservationRecord[] = useMemo(() => {
    return (infiniteObservationData?.pages.flatMap(
      (page) => page?.data || []
    ) || []) as ObservationRecord[];
  }, [infiniteObservationData]);

  const loadMore = useCallback(async () => {
    if (hasNextPage && !isFetchingNextPage) {
      await fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  const handleScrollActivate: React.UIEventHandler<HTMLDivElement> = (e) => {
    if (infiniteActive) {
      return;
    }
    const el = e.currentTarget;
    if (el && el.scrollTop > 0) {
      setInfiniteActive(true);
    }
  };

  const toggleSelectRecord = (id: string) => {
    setSelectedIds((prev) =>
      prev.includes(id) ? prev.filter((x) => x !== id) : [...prev, id]
    );
  };

  const toggleExpandRecord = (id: string) => {
    setExpandedRecords((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  };

  const clearSelection = () => {
    setSelectedIds([]);
    setSummaryText('');
    setIsEditing(false);
    setExpandedRecords(new Set());
    setInfiniteActive(false);
  };

  const handleGenerateSummary = async () => {
    if (selectedIds.length === 0) {
      Toast.show('请至少选择一条记录');
      return;
    }

    setIsGenerating(true);
    try {
      const selectedRecords = observationRecords.filter((r) =>
        selectedSet.has(r.observationId)
      );

      const prompt = `请根据以下观察记录生成一个阶段性总结，要求：
				1. 概括孩子们的主要活动和表现
				2. 突出体现的能力发展（如探索、合作、语言表达等）
				3. 总结学习成果和亮点
				4. 语言要专业、温暖，适合写入教学报告

				观察记录：
				${selectedRecords
          .map((r, i) => `${i + 1}. ${r.title}：${r.content || ''}`)
          .join('\n')}`;

      const response = await aiGenerateContent({
        conversation_id: '',
        files: [],
        inputs: [],
        query: prompt,
        response_mode: 'blocking',
      });

      if (response?.answer) {
        setSummaryText(response.answer);
        setIsEditing(true);
        Toast.show('已生成总结，您可以进行修改');
      } else {
        throw new Error('AI返回内容为空');
      }
    } catch (err) {
      console.error('生成总结失败:', err);
      Toast.show('生成失败，请重试');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleInsertSummary = () => {
    if (!(editor && summaryText.trim())) {
      return;
    }

    const contentToInsert: JSONContent[] = [
      {
        type: 'heading',
        attrs: { level: 3 },
        content: [{ type: 'text', text: '阶段性总结' }],
      },
      ...summaryText
        .split('\n')
        .filter((line) => line.trim())
        .map(
          (line) =>
            ({
              type: 'paragraph',
              attrs: { class: 'phase-summary', 'data-type': 'phase-summary' },
              content: [{ type: 'text', text: line }],
            }) as JSONContent
        ),
    ];

    editor.chain().focus().insertContent(contentToInsert).run();
    setVisible(false);
    clearSelection();
  };

  const handleOpen = () => {
    onClick?.();
    setVisible(true);
    setInfiniteActive(false);
    if (scrollRef.current) {
      scrollRef.current.scrollTop = 0;
    }
    if (projectId && !infiniteObservationData && !isLoading) {
      refetch();
    }
  };

  return (
    <>
      <button
        className={clsx(
          'flex flex-col items-center rounded-xl bg-purple-50 p-3 font-medium text-neutral-700 text-xs transition-colors hover:bg-purple-100 active:bg-purple-200',
          className
        )}
        onClick={handleOpen}
        type="button"
      >
        <FileTextIcon className="mb-1 h-5 w-5 text-purple-500" />
        {buttonText || null}
      </button>

      <Popup
        bodyClassName="p-0"
        onClose={() => {
          setVisible(false);
          clearSelection();
        }}
        position="bottom"
        visible={visible}
      >
        <div className="flex h-[80vh] max-h-[80vh] w-full flex-col">
          <div className="sticky top-0 z-10 flex items-center justify-between border-zinc-200 border-b bg-white px-4 py-3 dark:border-zinc-800 dark:bg-zinc-900">
            <div>
              <p className="font-semibold text-base text-zinc-900 dark:text-zinc-100">
                阶段性总结
              </p>
              <p className="text-sm text-stone-500">
                选择多条观察记录, AI 自动生成阶段性总结
              </p>
            </div>
            <div className="flex items-center gap-2">
              <AntdButton
                onClick={() => {
                  setVisible(false);
                  clearSelection();
                }}
                size="mini"
              >
                取消
              </AntdButton>
            </div>
          </div>

          <div
            className="flex-1 overflow-y-auto"
            onScroll={handleScrollActivate}
            ref={scrollRef}
          >
            {isEditing ? (
              <div className="px-4 py-3">
                <div className="mb-3 text-sm text-zinc-600 dark:text-zinc-400">
                  编辑总结内容
                </div>
                <TextArea
                  autoSize={{ minRows: 6, maxRows: 12 }}
                  className="mb-4"
                  onChange={(val) => setSummaryText(val)}
                  placeholder="请输入阶段性总结..."
                  rows={8}
                  value={summaryText}
                />
                <div className="flex gap-2">
                  <AntdButton block onClick={() => setIsEditing(false)}>
                    返回选择
                  </AntdButton>
                  <AntdButton
                    block
                    color="primary"
                    disabled={!summaryText.trim()}
                    onClick={handleInsertSummary}
                  >
                    插入到报告中
                  </AntdButton>
                </div>
              </div>
            ) : (
              <div className="px-4 py-3">
                {isLoading && (
                  <div className="flex items-center justify-center py-8">
                    <Loading />
                    <span className="ml-2 text-zinc-500">加载中...</span>
                  </div>
                )}

                {error && (
                  <div className="py-4">
                    <ErrorBlock
                      description={
                        error instanceof Error
                          ? error.message
                          : '请检查网络连接'
                      }
                      status="disconnected"
                      title="加载失败"
                    />
                    <div className="mt-4 text-center">
                      <AntdButton
                        onClick={async () => {
                          try {
                            await refetch();
                          } catch (err) {
                            console.error(err);
                          }
                        }}
                        size="small"
                      >
                        重试
                      </AntdButton>
                    </div>
                  </div>
                )}

                {!(isLoading || error) && observationRecords.length === 0 && (
                  <div className="py-8">
                    <ErrorBlock
                      description="当前项目还没有观察记录"
                      status="empty"
                      title="暂无观察记录"
                    />
                  </div>
                )}

                {!(isLoading || error) && observationRecords.length > 0 && (
                  <>
                    <div className="space-y-3">
                      {observationRecords.map((rec) => {
                        const recordId = rec.observationId;
                        const checked = selectedSet.has(recordId);
                        const images =
                          rec.medias
                            ?.filter((m) => m.type === 1)
                            .map((m) => m.url) || [];
                        const content = rec.content || '';
                        const tags = rec.tags || [];

                        return (
                          <button
                            className={clsx(
                              'w-full rounded-lg border p-3 text-left transition-colors',
                              'border-zinc-200 hover:border-zinc-300 dark:border-zinc-800 dark:hover:border-zinc-700',
                              checked &&
                                'border-purple-500 bg-purple-50 dark:bg-purple-900/20'
                            )}
                            key={recordId}
                            onClick={() => toggleSelectRecord(recordId)}
                            type="button"
                          >
                            <div className="flex items-start gap-3">
                              <Checkbox
                                checked={checked}
                                onChange={() => toggleSelectRecord(recordId)}
                                onClick={(e) => e.stopPropagation()}
                              />
                              <div className="min-w-0 flex-1">
                                <div className="mb-1 font-medium text-zinc-900 dark:text-zinc-100">
                                  {rec.title}
                                </div>

                                {images.length > 0 && (
                                  <div className="mb-2">
                                    <div className="flex gap-1 overflow-x-auto pb-1">
                                      {images.slice(0, 4).map((image) => (
                                        <img
                                          alt="观察记录图片"
                                          className="h-12 w-12 flex-shrink-0 rounded-lg border border-zinc-200 object-cover dark:border-zinc-700"
                                          key={image}
                                          src={image}
                                        />
                                      ))}
                                      {images.length > 4 && (
                                        <div className="flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-lg border border-zinc-200 bg-zinc-100 dark:border-zinc-700 dark:bg-zinc-800">
                                          <span className="text-xs text-zinc-500 dark:text-zinc-400">
                                            +{images.length - 4}
                                          </span>
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                )}

                                <div className="text-sm text-zinc-600 dark:text-zinc-400">
                                  <div
                                    className={clsx(
                                      'transition-all duration-200',
                                      expandedRecords.has(recordId)
                                        ? ''
                                        : 'line-clamp-4'
                                    )}
                                  >
                                    {content}
                                  </div>
                                  {content.length > 100 && (
                                    <button
                                      className="mt-1 flex items-center gap-1 text-purple-600 text-xs hover:text-purple-700 dark:text-purple-400 dark:hover:text-purple-300"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        toggleExpandRecord(recordId);
                                      }}
                                      type="button"
                                    >
                                      {expandedRecords.has(recordId) ? (
                                        <>
                                          <ChevronUp className="h-3 w-3" />
                                          收起
                                        </>
                                      ) : (
                                        <>
                                          <ChevronDown className="h-3 w-3" />
                                          展开
                                        </>
                                      )}
                                    </button>
                                  )}
                                </div>

                                <div className="mt-2 flex items-center gap-2">
                                  {tags.slice(0, 2).map((t) => (
                                    <Tag
                                      className="text-[10px]"
                                      color="primary"
                                      fill="outline"
                                      key={t.tagId}
                                    >
                                      {t.tagName}
                                    </Tag>
                                  ))}
                                  <span className="text-xs text-zinc-500">
                                    {rec.createTime
                                      ? new Date(
                                          rec.createTime
                                        ).toLocaleString()
                                      : ''}
                                  </span>
                                </div>
                              </div>
                            </div>
                          </button>
                        );
                      })}
                    </div>

                    <InfiniteScroll
                      hasMore={!!hasNextPage && infiniteActive}
                      loadMore={loadMore}
                    >
                      {(() => {
                        if (isFetchingNextPage) {
                          return <span>加载更多...</span>;
                        }
                        if (hasNextPage) {
                          return <span>上拉加载更多</span>;
                        }
                        return <span>没有更多了</span>;
                      })()}
                    </InfiniteScroll>
                  </>
                )}

                <div className="sticky bottom-0 mt-4 border-zinc-200 border-t bg-white/95 py-2 dark:border-zinc-800 dark:bg-zinc-900/95">
                  <AntdButton
                    block
                    color="primary"
                    disabled={selectedIds.length === 0 || isGenerating}
                    loading={isGenerating}
                    onClick={handleGenerateSummary}
                  >
                    {isGenerating
                      ? '正在生成总结...'
                      : `总结选中的 ${selectedIds.length} 条记录`}
                  </AntdButton>
                </div>
              </div>
            )}
          </div>
        </div>
      </Popup>
    </>
  );
};

export default PhaseSummaryInsertButton;
