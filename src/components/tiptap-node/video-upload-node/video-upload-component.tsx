import { <PERSON>deViewWrapper } from '@tiptap/react';
import { Toast } from 'antd-mobile';
import clsx from 'clsx';
import { AlertCircle, Loader2, Play, X } from 'lucide-react';
import type React from 'react';
import { useCallback, useRef, useState } from 'react';
import {
  SUPPORTED_VIDEO_TYPES,
  type UploadResult,
  uploadTiptapVideo,
} from '@/components/tiptap-templates/simple/tiptap-media-upload';
import { formatDuration } from '@/utils';

interface VideoUploadComponentProps {
  node: {
    attrs: {
      src?: string;
      poster?: string;
      duration?: number;
      width?: number;
      height?: number;
      uploading?: boolean;
      progress?: number;
      error?: string;
      size?: number;
    };
  };
  updateAttributes: (attrs: Record<string, unknown>) => void;
  deleteNode: () => void;
  extension: {
    options: {
      upload: (
        file: File,
        onProgress?: (progress: number) => void
      ) => Promise<UploadResult>;
      onError?: (error: string) => void;
    };
  };
}

export const VideoUploadComponent: React.FC<VideoUploadComponentProps> = ({
  node,
  updateAttributes,
  deleteNode,
  extension,
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isDragOver, setIsDragOver] = useState(false);
  const abortControllerRef = useRef<AbortController | null>(null);

  const handleFileSelect = useCallback(
    async (file: File) => {
      // 验证文件类型
      if (!SUPPORTED_VIDEO_TYPES.includes(file.type)) {
        Toast.show({ content: '请选择视频文件', icon: 'fail' });
        return;
      }

      // 验证文件大小 (100MB)
      if (file.size > 100 * 1024 * 1024) {
        Toast.show({ content: '视频文件不能超过100MB', icon: 'fail' });
        return;
      }

      // 创建新的 AbortController
      abortControllerRef.current = new AbortController();

      updateAttributes({
        uploading: true,
        progress: 0,
        error: null,
        size: file.size,
      });

      try {
        const result = await uploadTiptapVideo(
          file,
          (progress) => {
            updateAttributes({ progress });
          },
          abortControllerRef.current.signal
        );

        updateAttributes({
          src: result.url,
          poster: result.poster,
          duration: result.duration,
          width: result.width,
          height: result.height,
          uploading: false,
          progress: 100,
          error: null,
        });

        Toast.show({ content: '视频上传成功', icon: 'success' });
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : '上传失败';
        updateAttributes({
          uploading: false,
          error: errorMessage,
        });

        if (errorMessage !== '上传已取消') {
          Toast.show({ content: errorMessage, icon: 'fail' });
          extension.options.onError?.(errorMessage);
        }
      } finally {
        abortControllerRef.current = null;
      }
    },
    [updateAttributes, extension.options]
  );

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      setIsDragOver(false);

      const files = Array.from(e.dataTransfer.files);
      if (files.length > 0) {
        handleFileSelect(files[0]);
      }
    },
    [handleFileSelect]
  );

  const handleClick = useCallback(() => {
    if (node.attrs.uploading) {
      return;
    }
    fileInputRef.current?.click();
  }, [node.attrs.uploading]);

  const handleCancel = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      updateAttributes({
        uploading: false,
        progress: 0,
        error: '上传已取消',
      });
    }
  }, [updateAttributes]);

  const handleRetry = useCallback(() => {
    updateAttributes({
      error: null,
      uploading: false,
      progress: 0,
    });
  }, [updateAttributes]);

  // 如果视频已上传成功，显示视频播放器
  if (node.attrs.src) {
    return (
      <NodeViewWrapper className="video-upload-wrapper my-4">
        <div className="relative overflow-hidden rounded-xl bg-black">
          <video
            className="h-auto max-h-96 w-full"
            controls
            poster={node.attrs.poster}
            preload="metadata"
            src={node.attrs.src}
            style={{
              aspectRatio:
                node.attrs.width && node.attrs.height
                  ? `${node.attrs.width}/${node.attrs.height}`
                  : 'auto',
            }}
          >
            <track kind="captions" label="中文字幕" srcLang="zh" />
            您的浏览器不支持视频播放
          </video>

          {/* 视频信息覆盖层 */}
          <div className="absolute top-2 left-2 flex items-center gap-2">
            {node.attrs.duration && (
              <span className="rounded bg-black/70 px-2 py-1 text-white text-xs">
                {formatDuration(node.attrs.duration)}
              </span>
            )}
          </div>

          {/* 删除按钮 */}
          <button
            aria-label="删除视频"
            className="absolute top-2 right-2 rounded-full bg-black/70 p-1.5 text-white transition-colors hover:bg-black/90"
            onClick={deleteNode}
            type="button"
          >
            <X className="h-4 w-4" />
          </button>
        </div>
      </NodeViewWrapper>
    );
  }

  return (
    <NodeViewWrapper className="video-upload-wrapper my-4">
      <div
        className={clsx(
          'relative cursor-pointer rounded-xl border-2 border-dashed p-8 text-center transition-all',
          isDragOver
            ? 'border-blue-500 bg-blue-50'
            : 'border-gray-300 hover:border-gray-400',
          node.attrs.uploading && 'pointer-events-none opacity-75'
        )}
        onClick={handleClick}
        onDragLeave={() => setIsDragOver(false)}
        onDragOver={(e) => {
          e.preventDefault();
          setIsDragOver(true);
        }}
        onDrop={handleDrop}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            handleClick();
          }
        }}
        role="button"
        tabIndex={0}
      >
        <input
          accept={SUPPORTED_VIDEO_TYPES.join(',')}
          className="hidden"
          onChange={(e) => {
            const file = e.target.files?.[0];
            if (file) {
              handleFileSelect(file);
            }
          }}
          ref={fileInputRef}
          type="file"
        />

        {node.attrs.uploading ? (
          <div className="space-y-4">
            <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
              <Loader2 className="h-6 w-6 animate-spin text-blue-600" />
            </div>
            <div>
              <p className="mb-2 text-gray-600">上传中...</p>
              <div className="mb-2 h-2 w-full rounded-full bg-gray-200">
                <div
                  className="h-2 rounded-full bg-blue-600 transition-all duration-300"
                  style={{ width: `${node.attrs.progress || 0}%` }}
                />
              </div>
              <p className="mb-3 text-gray-500 text-sm">
                {node.attrs.progress || 0}%
                {node.attrs.size && (
                  <span className="ml-2">
                    ({Math.round((node.attrs.size || 0) / (1024 * 1024))}MB)
                  </span>
                )}
              </p>
              <button
                className="px-4 py-2 text-gray-600 text-sm transition-colors hover:text-gray-800"
                onClick={(e) => {
                  e.stopPropagation();
                  handleCancel();
                }}
                type="button"
              >
                取消上传
              </button>
            </div>
          </div>
        ) : node.attrs.error ? (
          <div className="space-y-4">
            <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
              <AlertCircle className="h-6 w-6 text-red-600" />
            </div>
            <div>
              <p className="mb-2 text-red-600">上传失败</p>
              <p className="mb-3 text-gray-500 text-sm">{node.attrs.error}</p>
              <div className="flex justify-center gap-2">
                <button
                  className="rounded-lg bg-blue-600 px-4 py-2 text-sm text-white transition-colors hover:bg-blue-700"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleRetry();
                  }}
                  type="button"
                >
                  重新选择
                </button>
                <button
                  className="px-4 py-2 text-gray-600 text-sm transition-colors hover:text-gray-800"
                  onClick={(e) => {
                    e.stopPropagation();
                    deleteNode();
                  }}
                  type="button"
                >
                  删除
                </button>
              </div>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-gray-100">
              <Play className="h-6 w-6 text-gray-600" />
            </div>
            <div>
              <p className="mb-1 text-gray-600">点击或拖拽上传视频</p>
              <p className="text-gray-500 text-sm">
                支持 MP4、MOV、AVI、WebM 格式，最大100MB
              </p>
            </div>
          </div>
        )}
      </div>
    </NodeViewWrapper>
  );
};
