'use client';

import type { NodeViewProps } from '@tiptap/react';
import { NodeViewWrapper } from '@tiptap/react';
import { Toast } from 'antd-mobile';
import clsx from 'clsx';
import { Play, Video } from 'lucide-react';
import { useCallback, useRef, useState } from 'react';
import { CloseIcon } from '@/components/tiptap-icons/close-icon';
import { Button } from '@/components/tiptap-ui-primitive/button';
import '@/components/tiptap-node/video-upload-node/video-upload-node.scss';
import {
  MAX_VIDEO_SIZE,
  SUPPORTED_VIDEO_TYPES,
  type UploadResult,
  uploadTiptapVideo,
} from '@/components/tiptap-templates/simple/tiptap-media-upload';
import { isValidPosition } from '@/lib/tiptap-utils';
import { formatDuration } from '@/utils';

export interface FileItem {
  /**
   * Unique identifier for the file item
   */
  id: string;
  /**
   * The actual File object being uploaded
   */
  file: File;
  /**
   * Current upload progress as a percentage (0-100)
   */
  progress: number;
  /**
   * Current status of the file upload process
   * @default "uploading"
   */
  status: 'uploading' | 'success' | 'error';
  /**
   * Upload result after successful upload
   * @optional
   */
  result?: UploadResult;
  /**
   * Controller that can be used to abort the upload process
   * @optional
   */
  abortController?: AbortController;
}

export interface UploadOptions {
  /**
   * Maximum allowed file size in bytes
   */
  maxSize: number;
  /**
   * Maximum number of files that can be uploaded
   */
  limit: number;
  /**
   * String specifying acceptable file types (MIME types or extensions)
   * @example "video/*"
   */
  accept: string;
  /**
   * Function that handles the actual file upload process
   * @param {File} file - The file to be uploaded
   * @param {Function} onProgress - Callback function to report upload progress
   * @param {AbortSignal} signal - Signal that can be used to abort the upload
   * @returns {Promise<string>} Promise resolving to the URL of the uploaded file
   */
  upload: (
    file: File,
    onProgress: (event: { progress: number }) => void,
    signal: AbortSignal
  ) => Promise<string>;
  /**
   * Callback triggered when a file is uploaded successfully
   * @param {string} url - URL of the successfully uploaded file
   * @optional
   */
  onSuccess?: (url: string) => void;
  /**
   * Callback triggered when an error occurs during upload
   * @param {Error} error - The error that occurred
   * @optional
   */
  onError?: (error: Error) => void;
}

/**
 * Custom hook for managing video file uploads with progress tracking and cancellation
 */
function useVideoUpload(options: UploadOptions) {
  const [fileItems, setFileItems] = useState<FileItem[]>([]);

  const uploadFile = async (file: File): Promise<UploadResult | null> => {
    // 验证文件类型
    if (!SUPPORTED_VIDEO_TYPES.includes(file.type)) {
      const error = new Error('请选择视频文件');
      options.onError?.(error);
      Toast.show({ content: '请选择视频文件', icon: 'fail' });
      return null;
    }

    // 验证文件大小
    if (file.size > options.maxSize) {
      const error = new Error(
        `视频文件不能超过${options.maxSize / 1024 / 1024}MB`
      );
      options.onError?.(error);
      Toast.show({ content: error.message, icon: 'fail' });
      return null;
    }

    const abortController = new AbortController();
    const fileId = crypto.randomUUID();

    const newFileItem: FileItem = {
      id: fileId,
      file,
      progress: 0,
      status: 'uploading',
      abortController,
    };

    setFileItems((prev) => [...prev, newFileItem]);

    try {
      if (!options.upload) {
        throw new Error('Upload function is not defined');
      }

      // 使用 uploadTiptapVideo 获取完整的上传结果
      const result = await uploadTiptapVideo(
        file,
        (progress: number) => {
          setFileItems((prev) =>
            prev.map((item) =>
              item.id === fileId ? { ...item, progress } : item
            )
          );
        },
        abortController.signal
      );

      if (!abortController.signal.aborted) {
        setFileItems((prev) =>
          prev.map((item) =>
            item.id === fileId
              ? { ...item, status: 'success', result, progress: 100 }
              : item
          )
        );
        options.onSuccess?.(result.url);
        return result;
      }

      return null;
    } catch (error) {
      if (!abortController.signal.aborted) {
        setFileItems((prev) =>
          prev.map((item) =>
            item.id === fileId
              ? { ...item, status: 'error', progress: 0 }
              : item
          )
        );
        options.onError?.(
          error instanceof Error ? error : new Error('上传失败')
        );
      }
      return null;
    }
  };

  const uploadFiles = async (files: File[]): Promise<UploadResult[]> => {
    if (!files || files.length === 0) {
      options.onError?.(new Error('没有选择文件'));
      return [];
    }

    if (options.limit && files.length > options.limit) {
      options.onError?.(new Error(`最多只能上传 ${options.limit} 个视频`));
      return [];
    }

    // Upload all files concurrently
    const uploadPromises = files.map((file) => uploadFile(file));
    const results = await Promise.all(uploadPromises);

    // Filter out null results (failed uploads)
    return results.filter((result): result is UploadResult => result !== null);
  };

  const removeFileItem = (fileId: string) => {
    setFileItems((prev) => {
      const fileToRemove = prev.find((item) => item.id === fileId);
      if (fileToRemove?.abortController) {
        fileToRemove.abortController.abort();
      }
      return prev.filter((item) => item.id !== fileId);
    });
  };

  const clearAllFiles = () => {
    for (const item of fileItems) {
      if (item.abortController) {
        item.abortController.abort();
      }
    }
    setFileItems([]);
  };

  return {
    fileItems,
    uploadFiles,
    removeFileItem,
    clearAllFiles,
  };
}

interface VideoUploadDragAreaProps {
  /**
   * Callback function triggered when files are dropped or selected
   * @param {File[]} files - Array of File objects that were dropped or selected
   */
  onFile: (files: File[]) => void;
  /**
   * Optional child elements to render inside the drag area
   * @optional
   * @default undefined
   */
  children?: React.ReactNode;
}

/**
 * A component that creates a drag-and-drop area for video uploads
 */
const VideoUploadDragArea: React.FC<VideoUploadDragAreaProps> = ({
  onFile,
  children,
}) => {
  const [isDragOver, setIsDragOver] = useState(false);
  const [isDragActive, setIsDragActive] = useState(false);

  const handleDragEnter = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragActive(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (!e.currentTarget.contains(e.relatedTarget as Node)) {
      setIsDragActive(false);
      setIsDragOver(false);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(true);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragActive(false);
    setIsDragOver(false);

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      onFile(files);
    }
  };

  return (
    <button
      aria-label="视频上传拖拽区域"
      className={clsx(
        'tiptap-video-upload-drag-area',
        isDragActive && 'drag-active',
        isDragOver && 'drag-over'
      )}
      onDragEnter={handleDragEnter}
      onDragLeave={handleDragLeave}
      onDragOver={handleDragOver}
      onDrop={handleDrop}
      type="button"
    >
      {children}
    </button>
  );
};

interface VideoUploadPreviewProps {
  /**
   * The file item to preview
   */
  fileItem: FileItem;
  /**
   * Callback to remove this file from upload queue
   */
  onRemove: () => void;
}

/**
 * Component that displays a preview of an uploading video file with progress
 */
const VideoUploadPreview: React.FC<VideoUploadPreviewProps> = ({
  fileItem,
  onRemove,
}) => {
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) {
      return '0 Bytes';
    }
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${Number.parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`;
  };

  return (
    <div className="tiptap-video-upload-preview">
      {fileItem.status === 'uploading' && (
        <div
          className="tiptap-video-upload-progress"
          style={{ width: `${fileItem.progress}%` }}
        />
      )}

      <div className="tiptap-video-upload-preview-content">
        <div className="tiptap-video-upload-file-info">
          <div className="tiptap-video-upload-file-icon">
            <Video />
          </div>
          <div className="tiptap-video-upload-details">
            <span className="tiptap-video-upload-text">
              {fileItem.file.name}
            </span>
            <span className="tiptap-video-upload-subtext">
              {formatFileSize(fileItem.file.size)}
              {fileItem.result?.duration && (
                <span className="ml-2">
                  • {formatDuration(fileItem.result.duration)}
                </span>
              )}
            </span>
          </div>
        </div>
        <div className="tiptap-video-upload-actions">
          {fileItem.status === 'uploading' && (
            <span className="tiptap-video-upload-progress-text">
              {fileItem.progress}%
            </span>
          )}
          <Button
            data-style="ghost"
            onClick={(e) => {
              e.stopPropagation();
              onRemove();
            }}
            type="button"
          >
            <CloseIcon className="tiptap-button-icon" />
          </Button>
        </div>
      </div>
    </div>
  );
};

const DropZoneContent: React.FC<{ maxSize: number }> = ({ maxSize }) => (
  <>
    <div className="tiptap-video-upload-dropzone">
      <div className="tiptap-video-upload-icon-container">
        <Play className="h-8 w-8" />
      </div>
    </div>

    <div className="tiptap-video-upload-content">
      <span className="tiptap-video-upload-text">
        <em>点击上传</em> 或拖拽视频到此处
      </span>
      <span className="tiptap-video-upload-subtext">
        支持 MP4、MOV、AVI、WebM 格式，最大 {maxSize / 1024 / 1024}MB
      </span>
    </div>
  </>
);

export const VideoUploadNode: React.FC<NodeViewProps> = (props) => {
  const { accept, limit, maxSize } = props.node.attrs;
  const inputRef = useRef<HTMLInputElement>(null);
  const extension = props.extension;

  const uploadOptions: UploadOptions = {
    maxSize: maxSize || MAX_VIDEO_SIZE,
    limit: limit || 1,
    accept: accept || 'video/*',
    upload: extension.options.upload || uploadTiptapVideo,
    onSuccess: extension.options.onSuccess,
    onError: extension.options.onError,
  };

  const { fileItems, uploadFiles, removeFileItem, clearAllFiles } =
    useVideoUpload(uploadOptions);

  const handleUpload = useCallback(
    async (files: File[]) => {
      const results = await uploadFiles(files);

      if (results.length > 0) {
        const pos = props.getPos();

        if (isValidPosition(pos)) {
          // 删除占位的上传节点
          props.editor
            .chain()
            .focus()
            .deleteRange({ from: pos, to: pos + 1 })
            .run();

          // 逐个插入自定义视频节点（NodeView 播放器）
          for (const result of results) {
            const attrs: Record<string, unknown> = { src: result.url };
            if (result.poster) {
              attrs.poster = result.poster;
            }
            if (typeof result.duration === 'number') {
              attrs.duration = result.duration;
            }

            // 插入 tiptap 自定义 video 节点（而不是 raw HTML）
            props.editor
              .chain()
              .focus()
              .insertContent({ type: 'video', attrs })
              .run();
          }
        }
      }
    },
    [uploadFiles, props]
  );

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) {
      extension.options.onError?.(new Error('没有选择文件'));
      return;
    }
    handleUpload(Array.from(files));
  };

  const handleClick = () => {
    if (inputRef.current && fileItems.length === 0) {
      inputRef.current.value = '';
      inputRef.current.click();
    }
  };

  const hasFiles = fileItems.length > 0;

  return (
    <NodeViewWrapper
      className="tiptap-video-upload"
      onClick={handleClick}
      tabIndex={0}
    >
      {!hasFiles && (
        <VideoUploadDragArea onFile={handleUpload}>
          <DropZoneContent maxSize={maxSize || MAX_VIDEO_SIZE} />
        </VideoUploadDragArea>
      )}

      {hasFiles && (
        <div className="tiptap-video-upload-previews">
          {fileItems.length > 1 && (
            <div className="tiptap-video-upload-header">
              <span>正在上传 {fileItems.length} 个视频</span>
              <Button
                data-style="ghost"
                onClick={(e) => {
                  e.stopPropagation();
                  clearAllFiles();
                }}
                type="button"
              >
                清除全部
              </Button>
            </div>
          )}
          {fileItems.map((fileItem) => (
            <VideoUploadPreview
              fileItem={fileItem}
              key={fileItem.id}
              onRemove={() => removeFileItem(fileItem.id)}
            />
          ))}
        </div>
      )}

      <input
        accept={accept}
        className="hidden"
        multiple={limit > 1}
        name="file"
        onChange={handleChange}
        onClick={(e: React.MouseEvent<HTMLInputElement>) => e.stopPropagation()}
        ref={inputRef}
        type="file"
      />
    </NodeViewWrapper>
  );
};
