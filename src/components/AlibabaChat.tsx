'use client';

import { useRef, useState } from 'react';

interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
  id: string;
}

export default function AlibabaChat() {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [input, setInput] = useState('');
  const [loading, setLoading] = useState(false);
  const abortControllerRef = useRef<AbortController | null>(null);

  const sendMessage = async () => {
    if (!input.trim() || loading) {
      return;
    }

    const userMessage: ChatMessage = {
      role: 'user',
      content: input,
      id: Date.now().toString(),
    };
    const currentInput = input;
    setMessages((prev) => [...prev, userMessage]);
    setInput('');
    setLoading(true);

    // 创建 AbortController 用于取消请求
    abortControllerRef.current = new AbortController();

    // 添加一个空的助手消息，用于流式更新
    const assistantMessageId = (Date.now() + 1).toString();
    const assistantMessage: ChatMessage = {
      role: 'assistant',
      content: '',
      id: assistantMessageId,
    };
    setMessages((prev) => [...prev, assistantMessage]);

    try {
      const response = await fetch('/api/message', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ message: currentInput }),
        signal: abortControllerRef.current.signal,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      const decoder = new TextDecoder();

      if (reader) {
        let buffer = '';

        while (true) {
          const { done, value } = await reader.read();

          if (done) {
            break;
          }

          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split('\n');
          buffer = lines.pop() || '';

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(6);

              if (data === '[DONE]') {
                break;
              }

              try {
                const parsed = JSON.parse(data);
                if (parsed.content) {
                  setMessages((prev) => {
                    return prev.map((msg) =>
                      msg.id === assistantMessageId
                        ? { ...msg, content: msg.content + parsed.content }
                        : msg
                    );
                  });
                }
              } catch (e) {
                console.error('解析流数据错误:', e);
              }
            }
          }
        }
      }
    } catch (error: any) {
      if (error.name === 'AbortError') {
        console.log('请求被取消');
        return;
      }

      // 更新助手消息为错误信息
      setMessages((prev) => {
        return prev.map((msg) =>
          msg.id === assistantMessageId
            ? { ...msg, content: `请求失败: ${error.message}` }
            : msg
        );
      });
    } finally {
      setLoading(false);
      abortControllerRef.current = null;
    }
  };

  const stopGeneration = () => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      setLoading(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  return (
    <div className="mx-auto max-w-2xl p-4">
      <div className="rounded-lg bg-white shadow-lg">
        <div className="border-b p-4">
          <h2 className="font-semibold text-xl">阿里百炼聊天测试 (流式输出)</h2>
        </div>

        <div className="h-96 space-y-4 overflow-y-auto p-4">
          {messages.map((msg) => (
            <div
              className={`flex ${msg.role === 'user' ? 'justify-end' : 'justify-start'}`}
              key={msg.id}
            >
              <div
                className={`max-w-xs rounded-lg px-4 py-2 lg:max-w-md ${
                  msg.role === 'user'
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-200 text-gray-800'
                }`}
              >
                <div className="whitespace-pre-wrap">{msg.content}</div>
                {msg.role === 'assistant' && loading && msg.content && (
                  <span className="animate-pulse">▊</span>
                )}
              </div>
            </div>
          ))}
          {loading &&
            messages.length > 0 &&
            messages[messages.length - 1].content === '' && (
              <div className="flex justify-start">
                <div className="rounded-lg bg-gray-200 px-4 py-2 text-gray-800">
                  <span className="animate-pulse">正在思考...</span>
                </div>
              </div>
            )}
        </div>

        <div className="border-t p-4">
          <div className="flex space-x-2">
            <input
              className="flex-1 rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={loading}
              onChange={(e) => setInput(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="输入消息... (Enter发送, Shift+Enter换行)"
              type="text"
              value={input}
            />
            {loading ? (
              <button
                className="rounded-md bg-red-500 px-4 py-2 text-white hover:bg-red-600"
                onClick={stopGeneration}
                type="button"
              >
                停止
              </button>
            ) : (
              <button
                className="rounded-md bg-blue-500 px-4 py-2 text-white hover:bg-blue-600 disabled:opacity-50"
                disabled={!input.trim()}
                onClick={sendMessage}
                type="button"
              >
                发送
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
