'use client';

import Cookies from 'js-cookie';
import Script from 'next/script';

import { siteConfigAdmin } from '@/constant/config';

const Footer: React.FC = () => {
  return (
    <Script
      onReady={() => {
        const hina = window.hinaDataStatistic;
        hina.init({
          serverUrl: siteConfigAdmin.serverUrl, // 配置采集上报地址URL（在线申请），查看如下【[采集上报地址获取] 链接
          showLog: false,
          sendType: 'ajax',
          autoTrackConfig: {
            // 是否开启自动点击采集, true表示开启，自动采集 H_WebClick 事件
            clickAutoTrack: false,
            // 是否开启页面停留采集, true表示开启，自动采集 H_WebStay 事件
            stayAutoTrack: false,
            // 是否开启页面浏览采集，auto 表示开启，singlePage 表示单页面开启，false 关闭，可配合手动开启使用
            // 若页面中有锚点设计，需要将该配置设为 false，否则触发锚点会多触发 H_pageview 事件
            pageviewAutoTrack: 'singlePage',
            //  是否开启页面离开采集，true 表示开启，具体配置查看插件集成
            pageLeaveAutoTrack: false,
            addCustomProperty: () => {
              const userRole = Cookies.get('userRole');
              let distinct_id = Cookies.get('teacherId');
              if (Number(userRole) === 1) {
                distinct_id = Cookies.get('parentId');
              }
              return {
                distinct_id,
                ancda_Id: distinct_id,
                userRole,
              };
            },
          },
        });
      }}
      src="https://unicorn-media.ancda.com/production/app/js/hina.min.js"
    />
  );
};

export default Footer;
