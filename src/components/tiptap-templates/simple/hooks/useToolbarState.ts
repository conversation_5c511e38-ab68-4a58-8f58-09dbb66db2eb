import React from 'react';
import { useIsMobile } from '@/hooks/use-mobile';
import type { ToolbarState } from '../types';

const initialState: ToolbarState = {
  mobileView: 'main',
  actionsVisible: false,
  aiMenuVisible: false,
  aiToneMenuVisible: false,
};

export function useToolbarState() {
  const isMobile = useIsMobile();
  const [toolbarState, setToolbarState] = React.useState<ToolbarState>(initialState);

  // 移动端视图切换
  const setMobileView = React.useCallback((view: ToolbarState['mobileView']) => {
    setToolbarState(prev => ({ ...prev, mobileView: view }));
  }, []);

  // 批量更新状态
  const updateToolbarState = React.useCallback((updates: Partial<ToolbarState>) => {
    setToolbarState(prev => ({ ...prev, ...updates }));
  }, []);

  // 切换操作面板
  const toggleActionsVisible = React.useCallback(() => {
    setToolbarState(prev => ({ ...prev, actionsVisible: !prev.actionsVisible }));
  }, []);

  // 切换AI菜单
  const toggleAIMenuVisible = React.useCallback(() => {
    setToolbarState(prev => ({ ...prev, aiMenuVisible: !prev.aiMenuVisible }));
  }, []);

  // 切换AI语气菜单
  const toggleAIToneMenuVisible = React.useCallback(() => {
    setToolbarState(prev => ({ ...prev, aiToneMenuVisible: !prev.aiToneMenuVisible }));
  }, []);

  // 当从移动端切换到桌面端时，重置移动端视图
  React.useEffect(() => {
    if (!isMobile && toolbarState.mobileView !== 'main') {
      setMobileView('main');
    }
  }, [isMobile, toolbarState.mobileView, setMobileView]);

  return {
    toolbarState,
    isMobile,
    setMobileView,
    updateToolbarState,
    toggleActionsVisible,
    toggleAIMenuVisible,
    toggleAIToneMenuVisible,
  };
}