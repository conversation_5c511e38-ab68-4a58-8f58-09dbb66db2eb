import type { Editor } from '@tiptap/core';
import { marked } from 'marked';
import React from 'react';
import { MARKDOWN_TEST_CHUNKS } from '../constants';

export function useMarkdownStream(editor: Editor | null) {
  // Markdown 流式插入函数
  const insertMarkdownStream = React.useCallback(
    (markdownChunk: string) => {
      if (!editor) {
        console.warn('编辑器未初始化');
        return;
      }

      console.log('开始处理 Markdown 片段:', markdownChunk);

      try {
        // 将 Markdown 片段转为 HTML
        const html = marked.parse(markdownChunk);
        console.log('Markdown 转 HTML:', html);

        // 检查是否有内容
        const htmlString = typeof html === 'string' ? html : String(html);
        if (!htmlString?.trim()) {
          console.warn('解析后的 HTML 内容为空');
          return;
        }

        // 插入 HTML 到当前光标位置
        const result = editor.commands.insertContent(htmlString);
        console.log('插入结果:', result);
        console.log('编辑器当前内容:', editor.getJSON());
      } catch (error) {
        console.error('Markdown 解析错误:', error);
        // 错误时插入错误信息
        const errorMessage = `[Markdown 解析失败: ${error instanceof Error ? error.message : '未知错误'}]`;
        try {
          editor.commands.insertContent(errorMessage);
        } catch (insertError) {
          console.error('插入错误信息也失败了:', insertError);
        }
      }
    },
    [editor]
  );

  // Markdown 流式测试函数
  const testMarkdownStream = React.useCallback(() => {
    if (!editor) {
      return;
    }

    let index = 0;
    const interval = setInterval(() => {
      if (index < MARKDOWN_TEST_CHUNKS.length) {
        const chunk = MARKDOWN_TEST_CHUNKS[index];
        if (chunk) {
          try {
            insertMarkdownStream(chunk);
            console.log(`插入 Markdown 片段 ${index + 1}:`, chunk);
          } catch (error) {
            console.error('插入 Markdown 失败:', error);
          }
        }
        index++;
      } else {
        clearInterval(interval);
        console.log('Markdown 流式插入测试完成');
      }
    }, 500); // 每1.5秒插入一个片段

    return () => clearInterval(interval);
  }, [editor, insertMarkdownStream]);

  return {
    insertMarkdownStream,
    testMarkdownStream,
  };
}
