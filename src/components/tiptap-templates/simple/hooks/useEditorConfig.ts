import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@tiptap/core';
import { Highlight } from '@tiptap/extension-highlight';
import { Image } from '@tiptap/extension-image';
import { TaskItem, TaskList } from '@tiptap/extension-list';
import { Subscript } from '@tiptap/extension-subscript';
import { Superscript } from '@tiptap/extension-superscript';
import { TextAlign } from '@tiptap/extension-text-align';
import { Typography } from '@tiptap/extension-typography';
import { Selection } from '@tiptap/extensions';
import { useEditor } from '@tiptap/react';
import { StarterKit } from '@tiptap/starter-kit';
import React from 'react';
import type { ProjectContextStorage } from '@/components/tiptap-extensions/project-context-extension';
import { ProjectContextExtension } from '@/components/tiptap-extensions/project-context-extension';
import { AlertNode } from '@/components/tiptap-node/alert-block/alert-node-extension';
// Tiptap Node Extensions
import { HorizontalRule } from '@/components/tiptap-node/horizontal-rule-node/horizontal-rule-node-extension';
import { ImageUploadNode } from '@/components/tiptap-node/image-upload-node/image-upload-node-extension';
import { ObservationInsertNode } from '@/components/tiptap-node/observation-insert-node';
import { VideoNodeExtension } from '@/components/tiptap-node/video-node/video-node-extension';
import { VideoUploadNode } from '@/components/tiptap-node/video-upload-node/video-upload-node-extension';
import { AIPreviewExtension } from '@/components/tiptap-templates/simple/extensions/ai-preview-extension';
// Upload handlers
import {
  MAX_IMAGE_SIZE,
  MAX_VIDEO_SIZE,
  uploadTiptapImage,
  uploadTiptapVideo,
} from '@/components/tiptap-templates/simple/tiptap-media-upload';

export function useEditorConfig(
  content: JSONContent,
  projectId: string,
  onChange: (content: JSONContent) => void
) {
  const editor = useEditor({
    immediatelyRender: false,
    shouldRerenderOnTransaction: false,
    editorProps: {
      attributes: {
        autocomplete: 'off',
        autocorrect: 'off',
        autocapitalize: 'off',
        'aria-label': 'Main content area, start typing to enter text.',
        class: 'simple-editor',
      },
    },
    extensions: [
      StarterKit.configure({
        horizontalRule: false,
        link: {
          openOnClick: false,
          enableClickSelection: true,
        },
      }),
      HorizontalRule,
      TextAlign.configure({ types: ['heading', 'paragraph'] }),
      TaskList,
      TaskItem.configure({ nested: true }),
      Highlight.configure({ multicolor: true }),
      Image,
      Typography,
      Superscript,
      Subscript,
      Selection,
      ImageUploadNode.configure({
        accept: 'image/*',
        maxSize: MAX_IMAGE_SIZE,
        limit: 3,
        upload: async (
          file: File,
          onProgress?: (event: { progress: number }) => void,
          signal?: AbortSignal
        ) => {
          const result = await uploadTiptapImage(
            file,
            (progress) => onProgress?.({ progress }),
            signal
          );
          return result.url;
        },
        onError: (error) => console.error('Image upload failed:', error),
      }),
      VideoUploadNode.configure({
        accept: 'video/*',
        maxSize: MAX_VIDEO_SIZE,
        limit: 1,
        upload: async (
          file: File,
          onProgress?: (event: { progress: number }) => void,
          signal?: AbortSignal
        ) => {
          const result = await uploadTiptapVideo(
            file,
            (progress) => onProgress?.({ progress }),
            signal
          );
          return result.url;
        },
        onError: (error) => console.error('Video upload failed:', error),
      }),
      VideoNodeExtension,
      AlertNode,
      ObservationInsertNode,
      ProjectContextExtension,
      AIPreviewExtension,
    ],
    content,
    onUpdate({ editor: editorInstance }) {
      console.log('🚀 ~ editor:', editorInstance.getJSON());
      onChange(editorInstance.getJSON());
    },
  });

  // 设置 projectId 到 editor storage
  React.useEffect(() => {
    if (editor && projectId) {
      const storage = editor.storage as unknown as {
        projectContext: ProjectContextStorage;
      };
      if (storage.projectContext) {
        storage.projectContext.projectId = projectId;
      }
    }
  }, [editor, projectId]);

  // 键盘隐藏处理
  const handleHideKeyboard = React.useCallback(() => {
    // 使用 Tiptap API 取消焦点
    editor?.commands.blur();
    // 双重保险：让根 DOM 失焦，促使移动端键盘收起
    const el = editor?.view.dom as HTMLElement | undefined;
    el?.blur();
  }, [editor]);

  return {
    editor,
    handleHideKeyboard,
  };
}
