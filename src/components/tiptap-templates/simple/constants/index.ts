// AI 输出格式指令
export const AI_OUTPUT_INSTRUCTION =
  '\n\n请直接输出内容，不要包含额外的解释，只需要纯文本，结构化的内容请用 markdown 格式输出。';

// AI 内容插入标签
export const AI_CONTENT_TAG = '[INSERT_CONTENT_HERE]';

// AI 语气选项
export const AI_TONE_OPTIONS = [
  { tone: 'formal', text: '正式', desc: '商务、学术场合' },
  { tone: 'casual', text: '轻松', desc: '日常交流' },
  { tone: 'professional', text: '专业', desc: '技术文档' },
  { tone: 'friendly', text: '友好', desc: '亲切自然' },
];

// Markdown 测试内容
export const MARKDOWN_TEST_CHUNKS = [
  '# Tiptap Markdown 流式插入测试',
  '\n\n这是**粗体**和*斜体*文本。',
  '\n\n- 列表项1\n- 列表项2\n- 列表项3',
  '\n\n```javascript\nfunction hello() {\n  console.log("Hello, Markdown!");\n}\n```',
  '\n\n> 这是一个引用块，演示流式插入功能',
  '\n\n[这是一个链接](https://example.com)',
  '\n\n最后一段文本，完成测试。',
];

// Markdown 检测正则表达式模式（模块级别定义，避免重复创建）
const MARKDOWN_PATTERNS = [
  /^#{1,6}\s+/m, // 标题 # ## ###
  /\*\*.*?\*\*/, // 粗体 **text**
  /\*.*?\*/, // 斜体 *text*
  /\[.*?\]\(.*?\)/, // 链接 [text](url)
  /!\[.*?\]\(.*?\)/, // 图片 ![alt](url)
  /^[-*+]\s+/m, // 无序列表
  /^\d+\.\s+/m, // 有序列表
  /^```[\s\S]*?```/m, // 代码块
  /`.*?`/, // 行内代码
  /^>\s+/m, // 引用
  /^\|.*\|.*$/m, // 表格
  /^---+$/m, // 分割线
  /~~.*?~~/, // 删除线 ~~text~~
  /_{2,}.*?_{2,}/, // 下划线粗体 __text__
  /_.*?_/, // 下划线斜体 _text_
];

// 自定义 Markdown 检测函数
export function isMarkdown(text: string): boolean {
  return MARKDOWN_PATTERNS.some((pattern) => pattern.test(text));
}
