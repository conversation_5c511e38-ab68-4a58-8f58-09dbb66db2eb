import type { Editor, JSONContent } from '@tiptap/core';

// 编辑器组件属性类型
export interface EditorProps {
  onChange: (content: JSONContent) => void;
  content: JSONContent;
  projectId: string;
}

// AI 命令类型
export type AICommandType = 
  | 'continue'
  | 'summarize'
  | 'polish'
  | 'expand'
  | 'shorten'
  | 'adjustTone';

// 工具栏状态类型
export interface ToolbarState {
  mobileView: 'main' | 'highlighter' | 'link';
  actionsVisible: boolean;
  aiMenuVisible: boolean;
  aiToneMenuVisible: boolean;
}

// 主工具栏属性类型
export interface MainToolbarProps {
  onHighlighterClick: () => void;
  onLinkClick: () => void;
  onHideKeyboard: () => void;
  isMobile: boolean;
  editor: Editor | null;
  onOpenActions: () => void;
  onOpenAIMenu: () => void;
  aiLoading: boolean;
  onTestMarkdown: () => void;
}

// 移动端工具栏属性类型
export interface MobileToolbarProps {
  type: 'highlighter' | 'link';
  onBack: () => void;
}

// AI语气选项类型
export interface AIToneOption {
  tone: string;
  text: string;
  desc: string;
}