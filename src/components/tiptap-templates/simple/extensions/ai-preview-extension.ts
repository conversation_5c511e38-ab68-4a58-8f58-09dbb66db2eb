import { Extension } from '@tiptap/core';
import { createAIPreviewPlugin } from '@/components/tiptap-templates/simple/plugins/ai-preview-plugin';

export const AIPreviewExtension = Extension.create({
  name: 'aiPreview',

  onCreate() {
    console.log('🎆 AI Preview Extension created successfully!');
  },

  addProseMirrorPlugins() {
    console.log('🔌 AI Preview Extension - Adding ProseMirror plugins...');
    const plugin = createAIPreviewPlugin(this.editor);
    console.log('🔌 AI Preview Plugin created:', plugin);
    return [plugin];
  },
});