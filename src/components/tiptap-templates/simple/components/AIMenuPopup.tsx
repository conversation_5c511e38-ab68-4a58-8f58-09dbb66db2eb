import type { Editor } from '@tiptap/core';
import { Popup } from 'antd-mobile';
import clsx from 'clsx';
import {
  ChevronRight,
  Edit3,
  List,
  MessageSquareText,
  Minimize2,
  ScrollText,
  Sparkles,
} from 'lucide-react';
import React from 'react';
import { AI_TONE_OPTIONS } from '../constants';
import type { AICommandType } from '../types';

interface AIMenuPopupProps {
  visible: boolean;
  onClose: () => void;
  editor: Editor | null;
  aiLoading: boolean;
  aiToneMenuVisible: boolean;
  onToggleAIToneMenu: () => void;
  onAICommand: (
    command: AICommandType,
    options?: Record<string, unknown>
  ) => void;
  onCustomPrompt: (prompt: string) => void;
}

export function AIMenuPopup({
  visible,
  onClose,
  editor,
  aiLoading,
  aiToneMenuVisible,
  onToggleAIToneMenu,
  onAICommand,
  onCustomPrompt,
}: AIMenuPopupProps) {
  const inputRef = React.useRef<HTMLInputElement>(null);

  const handleSendPrompt = () => {
    const input = inputRef.current;
    if (input?.value.trim()) {
      onCustomPrompt(input.value);
      input.value = '';
      onClose();
    }
  };

  return (
    <Popup
      bodyClassName={clsx('pb-safe')}
      onClose={onClose}
      position="bottom"
      showCloseButton
      visible={visible}
    >
      {aiToneMenuVisible ? (
        <div className="px-3 pt-3 pb-8">
          <div className="mb-3 font-medium text-neutral-500">选择语气</div>
          <div className="grid grid-cols-2 gap-3">
            {AI_TONE_OPTIONS.map((item) => (
              <button
                className={clsx(
                  'flex flex-col items-start rounded-xl border border-neutral-200 p-4 text-left',
                  'hover:border-blue-500 hover:bg-blue-50 active:bg-blue-100',
                  'dark:border-neutral-800 dark:hover:border-blue-400 dark:hover:bg-blue-900/20'
                )}
                key={item.tone}
                onClick={() => onAICommand('adjustTone', { tone: item.tone })}
                type="button"
              >
                <span className="font-medium text-neutral-900 dark:text-neutral-100">
                  {item.text}
                </span>
                <span className="text-neutral-500 text-sm dark:text-neutral-400">
                  {item.desc}
                </span>
              </button>
            ))}
          </div>
        </div>
      ) : (
        <div className="px-3 pt-3 pb-8">
          <div className="mb-3 font-medium text-neutral-500">AI 文档助手</div>
          <div className="divide-y divide-neutral-100 overflow-hidden rounded-xl bg-white shadow-sm ring-1 ring-black/5 dark:divide-neutral-800 dark:bg-neutral-900">
            {(() => {
              // 检查是否有选中的文本
              const hasSelection = editor && !editor.state.selection.empty;

              // 基础菜单项（总是显示）
              const baseMenuItems = [
                {
                  icon: Edit3,
                  text: '续写',
                  command: 'continue' as const,
                  description: '继续当前内容的写作',
                },
                {
                  icon: ScrollText,
                  text: '总结',
                  command: 'summarize' as const,
                  description: '生成内容摘要',
                },
              ];

              // 需要选中文本的菜单项
              const textSelectionMenuItems = hasSelection
                ? [
                    {
                      icon: Sparkles,
                      text: '润色',
                      command: 'polish' as const,
                      description: '优化文字表达',
                    },
                    {
                      icon: List,
                      text: '扩写',
                      command: 'expand' as const,
                      description: '丰富内容细节',
                    },
                    {
                      icon: Minimize2,
                      text: '缩写',
                      command: 'shorten' as const,
                      description: '精简内容长度',
                    },
                  ]
                : [];

              // 调整语气菜单项（有特殊处理）
              const toneMenuItem = hasSelection
                ? [
                    {
                      icon: MessageSquareText,
                      text: '调整语气',
                      action: onToggleAIToneMenu,
                      arrow: true,
                    },
                  ]
                : [];

              // 合并所有菜单项
              const allMenuItems = [
                ...baseMenuItems,
                ...textSelectionMenuItems,
                ...toneMenuItem,
              ];

              return allMenuItems.map((item) => (
                <button
                  className={clsx(
                    'flex w-full items-center gap-3 px-4 py-3',
                    'bg-white hover:bg-neutral-50 active:bg-neutral-100 disabled:cursor-not-allowed disabled:opacity-50',
                    'dark:bg-neutral-900 dark:active:bg-neutral-800 dark:hover:bg-neutral-800'
                  )}
                  disabled={aiLoading}
                  key={item.text}
                  onClick={() => {
                    if ('command' in item && item.command) {
                      onAICommand(item.command);
                      onClose();
                    } else if ('action' in item && item.action) {
                      item.action();
                    }
                  }}
                  type="button"
                >
                  <item.icon className="h-5 w-5 text-neutral-500" />
                  <div className="flex-1 text-left">
                    <div className=" text-neutral-900 dark:text-neutral-100">
                      {item.text}
                    </div>
                    {'description' in item && item.description && (
                      <div className="mt-0.5 text-neutral-500 text-xs dark:text-neutral-400">
                        {item.description}
                      </div>
                    )}
                  </div>
                  {'arrow' in item && item.arrow ? (
                    <ChevronRight className="h-4 w-4 text-neutral-400" />
                  ) : null}
                </button>
              ));
            })()}
          </div>

          <div className="mt-4">
            <div className="flex gap-2">
              <input
                className="flex-1 rounded-xl border border-neutral-200 bg-neutral-50 px-3 py-2 text-neutral-900 placeholder-neutral-400 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:border-neutral-800 dark:bg-neutral-800 dark:text-neutral-100 dark:placeholder-neutral-500 dark:focus:ring-blue-400"
                disabled={aiLoading}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    handleSendPrompt();
                  }
                }}
                placeholder="告诉我你想写点什么…"
                ref={inputRef}
                type="text"
              />
              <button
                className={clsx(
                  'rounded-xl bg-blue-500 px-4 py-2 font-medium text-white',
                  'hover:bg-blue-600 active:bg-blue-700 disabled:cursor-not-allowed disabled:opacity-50'
                )}
                disabled={aiLoading}
                onClick={handleSendPrompt}
                type="button"
              >
                发送
              </button>
            </div>
          </div>

          {/* aiLoading && (
            <div className="mt-4 flex items-center justify-center gap-2 text-neutral-500 text-sm">
              <div className="h-4 w-4 animate-spin rounded-full border-2 border-blue-500 border-t-transparent" />
              AI 正在思考中…
            </div>
          ) */}
        </div>
      )}
    </Popup>
  );
}
