import type { Editor } from '@tiptap/core';
import { Popup } from 'antd-mobile';
import clsx from 'clsx';
import { CheckSquare, Code, List, ListOrdered, Quote } from 'lucide-react';
import { MaterialSelectButton } from '@/components/tiptap-node/material-select-node';
import { ObservationInsertButton } from '@/components/tiptap-node/observation-insert-node';
import { PhaseSummaryInsertButton } from '@/components/tiptap-node/phase-summary-insert-node';

interface ActionsPopupProps {
  visible: boolean;
  onClose: () => void;
  editor: Editor | null;
}

export function ActionsPopup({ visible, onClose, editor }: ActionsPopupProps) {
  return (
    <Popup
      bodyClassName={clsx('pb-safe')}
      onClose={onClose}
      position="bottom"
      showCloseButton
      visible={visible}
    >
      <div className="px-4 pt-3 pb-8">
        {/* 顶部标题栏 */}
        <div className="mb-2 flex items-center justify-center">
          <div className="font-semibold text-base text-neutral-900">添加</div>
        </div>

        {/* 分组：基础 */}
        <div className="mt-3 mb-2 font-medium text-neutral-500 text-xs">
          基础
        </div>
        <div className="grid grid-cols-5 gap-6 sm:gap-12">
          {/* H1-H5 */}
          {[
            {
              label: 'H1',
              run: () => editor?.chain().focus().setHeading({ level: 1 }).run(),
            },
            {
              label: 'H2',
              run: () => editor?.chain().focus().setHeading({ level: 2 }).run(),
            },
            {
              label: 'H3',
              run: () => editor?.chain().focus().setHeading({ level: 3 }).run(),
            },
            {
              label: 'H4',
              run: () => editor?.chain().focus().setHeading({ level: 4 }).run(),
            },
            {
              label: 'H5',
              run: () => editor?.chain().focus().setHeading({ level: 5 }).run(),
            },
          ].map((item) => (
            <button
              className="h-12 w-12 rounded-xl bg-neutral-100 font-semibold text-neutral-800 text-sm transition-colors hover:bg-neutral-200 active:bg-neutral-300"
              key={String(item.label)}
              onClick={() => {
                item.run();
                onClose();
              }}
              type="button"
            >
              {item.label}
            </button>
          ))}

          {/* 有序/无序/任务/代码/引用 */}
          {[
            {
              label: <ListOrdered className="mx-auto h-5 w-5" />,
              run: () => editor?.chain().focus().toggleOrderedList().run(),
              key: 'ol',
            },
            {
              label: <List className="mx-auto h-5 w-5" />,
              run: () => editor?.chain().focus().toggleBulletList().run(),
              key: 'ul',
            },
            {
              label: <CheckSquare className="mx-auto h-5 w-5" />,
              run: () => editor?.chain().focus().toggleTaskList().run(),
              key: 'task',
            },
            {
              label: <Code className="mx-auto h-5 w-5" />,
              run: () => editor?.chain().focus().toggleCodeBlock().run(),
              key: 'code',
            },
            {
              label: <Quote className="mx-auto h-5 w-5" />,
              run: () => editor?.chain().focus().toggleBlockquote().run(),
              key: 'quote',
            },
          ].map((item) => (
            <button
              className="h-12 w-12 rounded-xl bg-neutral-100 text-neutral-800 transition-colors hover:bg-neutral-200 active:bg-neutral-300"
              key={item.key}
              onClick={() => {
                item.run();
                onClose();
              }}
              type="button"
            >
              {item.label}
            </button>
          ))}
        </div>

        {/* 分组：常用 */}
        <div className="mt-6 mb-2 font-medium text-neutral-500 text-xs">
          常用
        </div>
        <div className="grid grid-cols-4 gap-3">
          <ObservationInsertButton
            buttonText="插入观察记录"
            editor={editor}
            onClick={onClose}
          />

          <PhaseSummaryInsertButton
            buttonText="插入阶段性总结"
            editor={editor}
            onClick={onClose}
          />

          <MaterialSelectButton
            buttonText="从素材库选择"
            editor={editor}
            onClick={onClose}
          />
        </div>
      </div>
    </Popup>
  );
}
