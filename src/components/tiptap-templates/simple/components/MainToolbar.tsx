import clsx from 'clsx';
import { Keyboard, Plus, Sparkles } from 'lucide-react';
import { BlockquoteButton } from '@/components/tiptap-ui/blockquote-button';
import { CodeBlockButton } from '@/components/tiptap-ui/code-block-button';
import {
  ColorHighlightPopover,
  ColorHighlightPopoverButton,
} from '@/components/tiptap-ui/color-highlight-popover';
import { HeadingDropdownMenu } from '@/components/tiptap-ui/heading-dropdown-menu';
import { ImageUploadButton } from '@/components/tiptap-ui/image-upload-button';
import { LinkButton, LinkPopover } from '@/components/tiptap-ui/link-popover';
import { ListDropdownMenu } from '@/components/tiptap-ui/list-dropdown-menu';
import { MarkButton } from '@/components/tiptap-ui/mark-button';
import { TextAlignButton } from '@/components/tiptap-ui/text-align-button';
// Tiptap UI Components
import { UndoRedoButton } from '@/components/tiptap-ui/undo-redo-button';
import { VideoUploadButton } from '@/components/tiptap-ui/video-upload-button/video-upload-button';
// UI Primitives
import { Button } from '@/components/tiptap-ui-primitive/button';
import { Spacer } from '@/components/tiptap-ui-primitive/spacer';
import {
  ToolbarGroup,
  ToolbarSeparator,
} from '@/components/tiptap-ui-primitive/toolbar';

import type { MainToolbarProps } from '../types';

export function MainToolbar({
  onHighlighterClick,
  onLinkClick,
  onHideKeyboard,
  isMobile,
  editor,
  onOpenActions,
  onOpenAIMenu,
  aiLoading,
  onTestMarkdown,
}: MainToolbarProps) {
  const isFocused = editor?.isFocused ?? false;

  return (
    <>
      <Spacer />
      <ToolbarGroup>
        <UndoRedoButton action="undo" />
        <UndoRedoButton action="redo" />
      </ToolbarGroup>

      {/* 仅在移动端且编辑器聚焦时显示"收起键盘"按钮 */}
      {isMobile && isFocused && (
        <>
          <ToolbarSeparator />
          <ToolbarGroup>
            <Button
              aria-label="收起键盘"
              data-style="ghost"
              onClick={onHideKeyboard}
              title="收起键盘"
            >
              <Keyboard className="tiptap-button-icon" />
            </Button>
          </ToolbarGroup>
        </>
      )}
      <ToolbarGroup>
        <Button
          aria-label="更多操作"
          data-style="ghost"
          onClick={onOpenActions}
          title="更多操作"
        >
          <Plus className="tiptap-button-icon" />
        </Button>
        {/* AI 工具按钮 */}
        <Button
          aria-label="AI 工具"
          className={clsx(aiLoading && 'animate-pulse')}
          data-style="ghost"
          onClick={onOpenAIMenu}
          title="AI 工具"
        >
          <Sparkles
            className={clsx(
              'tiptap-button-icon',
              aiLoading ? '!text-blue-500' : '!text-fuchsia-700'
            )}
          />
        </Button>
      </ToolbarGroup>

      <ToolbarSeparator />

      <ToolbarGroup>
        <HeadingDropdownMenu levels={[1, 2, 3, 4]} portal={isMobile} />
        <ListDropdownMenu
          portal={isMobile}
          types={['bulletList', 'orderedList', 'taskList']}
        />
        <BlockquoteButton />
        <CodeBlockButton />
      </ToolbarGroup>

      <ToolbarSeparator />

      <ToolbarGroup>
        <MarkButton type="bold" />
        <MarkButton type="italic" />
        <MarkButton type="strike" />
        <MarkButton type="code" />
        <MarkButton type="underline" />
        {isMobile ? (
          <ColorHighlightPopoverButton onClick={onHighlighterClick} />
        ) : (
          <ColorHighlightPopover />
        )}
        {isMobile ? <LinkButton onClick={onLinkClick} /> : <LinkPopover />}
      </ToolbarGroup>

      <ToolbarSeparator />

      <ToolbarGroup>
        <MarkButton type="superscript" />
        <MarkButton type="subscript" />
      </ToolbarGroup>

      <ToolbarSeparator />

      <ToolbarGroup>
        <TextAlignButton align="left" />
        <TextAlignButton align="center" />
        <TextAlignButton align="right" />
        <TextAlignButton align="justify" />
      </ToolbarGroup>

      <ToolbarSeparator />

      <ToolbarGroup>
        <ImageUploadButton text="图片" />
        <VideoUploadButton text="视频" />
        {/* Markdown 测试按钮 */}
        <Button
          aria-label="测试 Markdown 流式插入"
          data-style="ghost"
          onClick={onTestMarkdown}
          title="测试 Markdown 流式插入"
        >
          <Sparkles className="tiptap-button-icon" />
        </Button>
      </ToolbarGroup>

      <Spacer />

      {isMobile && <ToolbarSeparator />}
    </>
  );
}
