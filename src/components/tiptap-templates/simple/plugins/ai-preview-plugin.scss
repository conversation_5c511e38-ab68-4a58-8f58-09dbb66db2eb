.ai-preview-widget {
  @apply inline-block relative bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-3 m-1 shadow-sm;
  max-width: 480px;
  min-width: 300px;
  transition: all 0.2s ease-in-out;
  animation: slideInFromRight 0.3s ease-out;

  &:hover {
    @apply shadow-md border-blue-300;
    transform: translateY(-1px);
  }

  .ai-preview-content {
    @apply text-sm text-gray-700 mb-3 leading-relaxed;
    font-family: inherit;
    white-space: pre-wrap;
    word-break: break-word;
    max-height: 200px;
    overflow-y: auto;

    // 自定义滚动条
    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      @apply bg-gray-100 rounded;
    }

    &::-webkit-scrollbar-thumb {
      @apply bg-gray-300 rounded;

      &:hover {
        @apply bg-gray-400;
      }
    }
  }

  .ai-preview-actions {
    @apply flex gap-2 justify-end;
  }

  .ai-preview-btn {
    @apply inline-flex items-center gap-1.5 px-3 py-1.5 text-xs font-medium rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
    border: none;
    cursor: pointer;

    svg {
      @apply w-3 h-3;
    }

    &.ai-preview-accept {
      @apply bg-green-100 text-green-700 hover:bg-green-200 focus:ring-green-500;

      &:hover {
        @apply shadow-sm;
        transform: translateY(-1px);
      }

      &:active {
        transform: translateY(0);
      }
    }

    &.ai-preview-reject {
      @apply bg-gray-100 text-gray-600 hover:bg-gray-200 focus:ring-gray-500;

      &:hover {
        @apply shadow-sm text-gray-700;
        transform: translateY(-1px);
      }

      &:active {
        transform: translateY(0);
      }
    }
  }

  // 添加一个小的指示器箭头
  &::before {
    content: '';
    @apply absolute -left-1 top-3 w-2 h-2 bg-white border-l border-t border-blue-200;
    transform: rotate(-45deg);
  }
}

// 动画定义
@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

// 移动端适配
@media (max-width: 640px) {
  .ai-preview-widget {
    max-width: calc(100vw - 2rem);
    min-width: 280px;
    @apply mx-1 p-2;

    .ai-preview-content {
      @apply text-xs mb-2;
      max-height: 150px;
    }

    .ai-preview-btn {
      @apply px-2 py-1 text-xs gap-1;

      svg {
        @apply w-2.5 h-2.5;
      }
    }
  }
}

// 深色模式支持
@media (prefers-color-scheme: dark) {
  .ai-preview-widget {
    @apply bg-gradient-to-r from-slate-800 to-slate-700 border-slate-600;

    .ai-preview-content {
      @apply text-gray-200;

      &::-webkit-scrollbar-track {
        @apply bg-slate-700;
      }

      &::-webkit-scrollbar-thumb {
        @apply bg-slate-500;

        &:hover {
          @apply bg-slate-400;
        }
      }
    }

    &::before {
      @apply bg-slate-700 border-slate-600;
    }

    &:hover {
      @apply border-slate-500;
    }

    .ai-preview-btn {
      &.ai-preview-accept {
        @apply bg-green-900 text-green-300 hover:bg-green-800;
      }

      &.ai-preview-reject {
        @apply bg-slate-700 text-slate-300 hover:bg-slate-600;
      }
    }
  }
}

// 加载动画效果
.ai-preview-loading {
  .ai-preview-content {
    position: relative;

    &::after {
      content: '';
      @apply absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-60;
      animation: shimmer 1.5s infinite;
    }
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}
