import type { Editor } from '@tiptap/core';
import { Plugin, PluginKey } from '@tiptap/pm/state';
import { Decoration, DecorationSet } from '@tiptap/pm/view';
import React from 'react';
import { createRoot, type Root } from 'react-dom/client';
import { Streamdown } from 'streamdown';

export interface AIPreviewState {
  decorations: DecorationSet;
  previewContent: string | null;
  previewPos: number | null;
  isVisible: boolean;
  reactRoot: Root | null;
}

export const aiPreviewPluginKey = new PluginKey<AIPreviewState>('aiPreview');

// 用于生成唯一 ID 的计数器，确保每次更新都有不同的 key
let decorationCounter = 0;

// 创建预览装饰插件
export function createAIPreviewPlugin(editor: Editor) {
  return new Plugin<AIPreviewState>({
    key: aiPreviewPluginKey,
    state: {
      init(): AIPreviewState {
        return {
          decorations: DecorationSet.empty,
          previewContent: null,
          previewPos: null,
          isVisible: false,
          reactRoot: null,
        };
      },
      apply(tr, value, _oldState, newState) {
        console.log('🔥 AI Preview Plugin - apply called:', {
          transaction: tr,
          currentValue: value,
          hasPreviewMeta: !!tr.getMeta('aiPreview'),
        });

        // 处理自定义 meta 数据
        const previewMeta = tr.getMeta('aiPreview');
        console.log('🔥 previewMeta details:', previewMeta);

        if (previewMeta) {
          if (previewMeta.action === 'show') {
            // 递增计数器，确保唯一 key
            decorationCounter++;

            // 创建预览装饰（直接创建 DOM 元素而不是使用工厂函数，确保内容更新）
            const { element: previewElement, root: reactRoot } =
              createPreviewElement(
                previewMeta.content,
                () => acceptPreview(editor),
                () => rejectPreview(editor)
              );

            const widget = Decoration.widget(previewMeta.pos, previewElement, {
              side: 1, // 放在光标后面
              key: `ai-preview-show-${decorationCounter}-${Date.now()}`,
            });

            return {
              decorations: DecorationSet.create(newState.doc, [widget]),
              previewContent: previewMeta.content,
              previewPos: previewMeta.pos,
              isVisible: true,
              reactRoot,
            };
          }
          if (previewMeta.action === 'hide') {
            console.log('✅ AI Preview Plugin - HIDE action completed');
            console.log('✅ AI Preview Plugin - HIDE action completed');
            // 清理 React Root，防止内存泄漏
            value.reactRoot?.unmount();
            return {
              decorations: DecorationSet.empty,
              previewContent: null,
              previewPos: null,
              isVisible: false,
              reactRoot: null,
            };
          }
          if (previewMeta.action === 'update') {
            // 更新预览内容时，优先通过 React Root 重渲染 Streamdown（支持流式 Markdown）
            if (value.isVisible && value.previewPos !== null) {
              const existingPreviewContent = document.querySelector(
                '.ai-preview-content'
              ) as HTMLElement;
              if (existingPreviewContent && value.reactRoot) {
                // 微弱的透明度变化用于平滑过渡
                existingPreviewContent.style.opacity = '0.8';
                setTimeout(() => {
                  const root = value.reactRoot;
                  if (!root) {
                    return;
                  }
                  root.render(
                    React.createElement(Streamdown, null, previewMeta.content)
                  );
                  existingPreviewContent.style.opacity = '1';
                  // 滚动到底部
                  requestAnimationFrame(() => {
                    existingPreviewContent.scrollTop =
                      existingPreviewContent.scrollHeight;
                  });
                }, 50);

                console.log(
                  '✅ AI Preview Plugin - UPDATE action completed (react re-render):',
                  {
                    contentLength: previewMeta.content?.length,
                    updatedViaReact: true,
                  }
                );

                // 返回更新后的状态，但保持现有的decorations
                return {
                  ...value,
                  previewContent: previewMeta.content,
                };
              }
            }

            // 如果找不到现有预览元素，则回退到重新创建（不应该发生）
            console.warn('⚠️ 未找到现有预览元素，回退到重新创建');
            // 清理旧的 React Root
            value.reactRoot?.unmount();
            decorationCounter++;

            const { element: previewElement, root: reactRoot } =
              createPreviewElement(
                previewMeta.content,
                () => acceptPreview(editor),
                () => rejectPreview(editor)
              );

            const widget = Decoration.widget(
              value.previewPos || previewMeta.pos,
              previewElement,
              {
                side: 1,
                key: `ai-preview-update-${decorationCounter}-${Date.now()}`,
              }
            );

            return {
              ...value,
              decorations: DecorationSet.create(newState.doc, [widget]),
              previewContent: previewMeta.content,
              isVisible: true,
              reactRoot,
            };
          }
        }

        // 保持装饰与文档同步
        if (value.isVisible && value.decorations.find().length > 0) {
          return {
            ...value,
            decorations: value.decorations.map(tr.mapping, tr.doc),
          };
        }

        return value;
      },
    },
    props: {
      decorations(state) {
        return this.getState(state)?.decorations;
      },
    },
  });
}

// 创建预览元素
function createPreviewElement(
  content: string,
  onAccept: () => void | Promise<void>,
  onReject: () => void | Promise<void>
): { element: HTMLElement; root: Root } {
  const preview = document.createElement('div');
  preview.className = 'ai-preview-widget';

  // 添加基础样式
  preview.style.cssText = `
    max-width: 400px;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    margin: 8px 0;
    font-family: system-ui, -apple-system, sans-serif;
    z-index: 1000;
    position: relative;
    opacity: 1;
    transform: translateY(0);
    transition: all 0.2s ease-in-out;
  `;

  // 使用 Streamdown 在 React 根中渲染 Markdown 内容（支持流式更新）

  // 创建内容区域
  const contentDiv = document.createElement('div');
  contentDiv.className = 'ai-preview-content';
  // 在内容区域内创建挂载点，并用 React Root 渲染 Streamdown
  const mount = document.createElement('div');
  contentDiv.appendChild(mount);
  const root = createRoot(mount);
  root.render(React.createElement(Streamdown, null, content));

  // 内容区域样式
  contentDiv.style.cssText = `
    padding: 12px;
    max-height: 200px;
    overflow-y: auto;
    border-bottom: 1px solid #e5e7eb;
    line-height: 1.5;
    color: #374151;
    font-size: 14px;
    white-space: pre-wrap;
    word-wrap: break-word;
    scroll-behavior: smooth;
    transition: opacity 0.1s ease-in-out;
  `;

  // 确保内容能够滚动到底部（使用 requestAnimationFrame 确保 DOM 更新后再滚动）
  requestAnimationFrame(() => {
    contentDiv.scrollTop = contentDiv.scrollHeight;
  });

  // 创建操作按钮区域
  const actionsDiv = document.createElement('div');
  actionsDiv.className = 'ai-preview-actions';
  actionsDiv.style.cssText = `
    display: flex;
    gap: 8px;
    padding: 8px 12px;
    background: #f9fafb;
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
  `;

  // 创建接受按钮
  const acceptBtn = document.createElement('button');
  acceptBtn.className = 'ai-preview-btn ai-preview-accept';
  acceptBtn.type = 'button';
  acceptBtn.innerHTML = `
    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
      <polyline points="20,6 9,17 4,12"></polyline>
    </svg>
    接受
  `;
  acceptBtn.style.cssText = `
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 6px 12px;
    background: #10b981;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: background 0.2s;
  `;
  acceptBtn.onmouseover = () => {
    acceptBtn.style.background = '#059669';
  };
  acceptBtn.onmouseout = () => {
    acceptBtn.style.background = '#10b981';
  };

  // 创建拒绝按钮
  const rejectBtn = document.createElement('button');
  rejectBtn.className = 'ai-preview-btn ai-preview-reject';
  rejectBtn.type = 'button';
  rejectBtn.innerHTML = `
    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
      <line x1="18" y1="6" x2="6" y2="18"></line>
      <line x1="6" y1="6" x2="18" y2="18"></line>
    </svg>
    拒绝
  `;
  rejectBtn.style.cssText = `
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 6px 12px;
    background: #ef4444;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: background 0.2s;
  `;
  rejectBtn.onmouseover = () => {
    rejectBtn.style.background = '#dc2626';
  };
  rejectBtn.onmouseout = () => {
    rejectBtn.style.background = '#ef4444';
  };

  actionsDiv.appendChild(acceptBtn);
  actionsDiv.appendChild(rejectBtn);

  // 组装预览元素
  preview.appendChild(contentDiv);
  preview.appendChild(actionsDiv);

  // 添加事件监听
  acceptBtn.addEventListener('click', (e) => {
    e.preventDefault();
    e.stopPropagation();
    onAccept();
  });

  rejectBtn.addEventListener('click', (e) => {
    e.preventDefault();
    e.stopPropagation();
    onReject();
  });

  // 添加调试信息
  console.log('🎨 创建新的预览元素:', {
    contentLength: content.length,
    contentPreview:
      content.substring(0, 50) + (content.length > 50 ? '...' : ''),
  });

  return { element: preview, root };
}

// 接受预览内容（将 Markdown 转换为 HTML 后插入）
async function acceptPreview(editor: Editor) {
  const pluginState = aiPreviewPluginKey.getState(editor.state);
  if (pluginState?.previewContent && pluginState.previewPos !== null) {
    // 先隐藏预览
    hideAIPreview(editor);

    // 将 Markdown 转为 HTML，避免把纯 Markdown 文本插入文档
    const { marked } = await import('marked');
    const html = await marked.parse(pluginState.previewContent);

    // 检查是否有选中的文本需要替换
    const { from, to, empty } = editor.state.selection;

    if (!empty && from < to) {
      // 如果有选中文本，先删除再插入
      editor
        .chain()
        .focus()
        .deleteRange({ from, to })
        .insertContentAt(from, html, {
          updateSelection: true,
          parseOptions: { preserveWhitespace: 'full' },
        })
        .run();
    } else {
      // 没有选中文本，直接在指定位置插入
      editor
        .chain()
        .focus()
        .insertContentAt(pluginState.previewPos, html, {
          updateSelection: true,
          parseOptions: { preserveWhitespace: 'full' },
        })
        .run();
    }
  }
}

// 拒绝/隐藏预览
function rejectPreview(editor: Editor) {
  hideAIPreview(editor);
}

// 显示 AI 预览
export function showAIPreview(editor: Editor, content: string, pos?: number) {
  const position = pos ?? editor.state.selection.head;

  console.log('🎉 showAIPreview called:', {
    contentLength: content.length,
    contentPreview:
      content.substring(0, 50) + (content.length > 50 ? '...' : ''),
    position,
    editorExists: !!editor,
    viewExists: !!editor.view,
    stateExists: !!editor.state,
  });

  editor.view.dispatch(
    editor.view.state.tr.setMeta('aiPreview', {
      action: 'show',
      content,
      pos: position,
    })
  );

  console.log('🎉 showAIPreview transaction dispatched');
}

// 更新预览内容（流式更新时使用）
export function updateAIPreview(editor: Editor, content: string, pos?: number) {
  console.log('🔄 更新预览内容:', {
    contentLength: content.length,
    contentPreview:
      content.substring(0, 50) + (content.length > 50 ? '...' : ''),
    pos,
  });

  editor.view.dispatch(
    editor.view.state.tr.setMeta('aiPreview', {
      action: 'update',
      content,
      pos,
    })
  );
}

// 隐藏预览
export function hideAIPreview(editor: Editor) {
  editor.view.dispatch(
    editor.view.state.tr.setMeta('aiPreview', {
      action: 'hide',
    })
  );
}

// 检查是否有活跃的预览
export function hasActivePreview(editor: Editor): boolean {
  const pluginState = aiPreviewPluginKey.getState(editor.state);
  const isActive = pluginState?.isVisible ?? false;

  console.log('🔍 hasActivePreview check:', {
    pluginState,
    isVisible: pluginState?.isVisible,
    isActive,
    previewContent: pluginState?.previewContent ? 'exists' : 'null',
    decorationsCount: pluginState?.decorations?.find()?.length ?? 0,
  });

  return isActive;
}

// 获取当前预览状态
export function getPreviewState(editor: Editor): AIPreviewState | null {
  return aiPreviewPluginKey.getState(editor.state) ?? null;
}
