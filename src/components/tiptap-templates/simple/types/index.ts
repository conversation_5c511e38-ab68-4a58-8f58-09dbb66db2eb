import type { Editor, JSONContent } from '@tiptap/core';

export interface EditorProps {
  onChange: (content: JSONContent) => void;
  content: JSONContent;
  projectId: string;
}

export interface AICommandContext {
  editor: Editor;
  selectedText: string;
  fullDocument: string;
  cursorPosition: number;
}

export type MobileViewType = 'main' | 'highlighter' | 'link';

export type AICommandType = 'continue' | 'summarize' | 'polish' | 'expand' | 'shorten' | 'adjustTone';

export interface ToolbarState {
  mobileView: MobileViewType;
  actionsVisible: boolean;
  aiMenuVisible: boolean;
  aiToneMenuVisible: boolean;
}

export interface AIToneOption {
  tone: string;
  text: string;
  desc: string;
}

export interface MainToolbarProps {
  onHighlighterClick: () => void;
  onLinkClick: () => void;
  onHideKeyboard: () => void;
  isMobile: boolean;
  editor: Editor | null;
  onOpenActions: () => void;
  onOpenAIMenu: () => void;
  aiLoading: boolean;
  onTestMarkdown: () => void;
}

export interface MobileToolbarProps {
  type: 'highlighter' | 'link';
  onBack: () => void;
}