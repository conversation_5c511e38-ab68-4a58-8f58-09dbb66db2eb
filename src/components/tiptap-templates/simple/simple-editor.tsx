import { Editor<PERSON>ontent, EditorContext } from '@tiptap/react';
import <PERSON>ript from 'next/script';
import React from 'react';
// --- UI Primitives ---
import { Toolbar } from '@/components/tiptap-ui-primitive/toolbar';

// --- Hooks ---
import { useCursorVisibility } from '@/hooks/use-cursor-visibility';
import { useScrolling } from '@/hooks/use-scrolling';
import { useWindowSize } from '@/hooks/use-window-size';

// --- Styles ---
import './simple-editor.scss';
import './_variables.scss';
import './_keyframe-animations.scss';

import { ActionsPopup } from './components/ActionsPopup';
import { AIMenuPopup } from './components/AIMenuPopup';
// --- Local Components and Hooks ---
import { MainToolbar } from './components/MainToolbar';
import { MobileToolbar } from './components/MobileToolbar';
import { useAICommands } from './hooks/useAICommands';
import { useEditorConfig } from './hooks/useEditorConfig';
import { useMarkdownStream } from './hooks/useMarkdownStream';
import { useToolbarState } from './hooks/useToolbarState';

// --- Styles for Tiptap nodes ---
import '@/components/tiptap-templates/simple/plugins/ai-preview-plugin.scss';
import '@/components/tiptap-node/blockquote-node/blockquote-node.scss';
import '@/components/tiptap-node/code-block-node/code-block-node.scss';
import '@/components/tiptap-node/horizontal-rule-node/horizontal-rule-node.scss';
import '@/components/tiptap-node/list-node/list-node.scss';
import '@/components/tiptap-node/image-node/image-node.scss';
import '@/components/tiptap-node/video-upload-node/video-upload-node.scss';
import '@/components/tiptap-node/heading-node/heading-node.scss';
import '@/components/tiptap-node/paragraph-node/paragraph-node.scss';

import type { EditorProps } from './types';

/**
 * Markdown 流式插入功能说明：
 *
 * 本组件已集成 @tiptap/pm/markdown，支持将后端流式输出的 Markdown 内容插入到编辑器中。
 *
 * 主要功能：
 * 1. insertMarkdownStream(markdownChunk: string) - 将 Markdown 片段插入到编辑器
 * 2. 自动检测内容是否为 Markdown 格式
 * 3. 在 AI 响应中自动使用 Markdown 渲染
 * 4. 提供测试按钮来演示功能
 *
 * 使用方法：
 * - 后端通过 WebSocket 或 SSE 流式发送 Markdown 内容
 * - 前端调用 insertMarkdownStream() 函数插入每个片段
 * - 支持标题、列表、代码块、引用、链接等 Markdown 语法
 */

export function SimpleEditor({ onChange, content, projectId }: EditorProps) {
  const windowSize = useWindowSize();
  const toolbarRef = React.useRef<HTMLDivElement>(null);

  // 禁用默认上下文菜单
  document.addEventListener('contextmenu', (e) => {
    e.preventDefault();
    e.stopPropagation();
    return false;
  });

  // 监听文本选择事件
  // document.addEventListener('selectionchange', () => {
  //   const selection = window.getSelection();
  //   console.log('🚀 ~ selection:', selection);
  //   if (selection && selection.toString().length > 0) {
  //     const range = selection.getRangeAt(0);
  //     const rect = range.getBoundingClientRect();
  //     console.log('🚀 ~ rect:', rect);
  //   }
  // });

  // --- Custom Hooks ---
  const { editor, handleHideKeyboard } = useEditorConfig(
    content,
    projectId,
    onChange
  );
  const {
    toolbarState,
    isMobile,
    setMobileView,
    updateToolbarState,
    toggleActionsVisible,
    toggleAIMenuVisible,
    toggleAIToneMenuVisible,
  } = useToolbarState();
  const { aiLoading, handleAICommand, handleCustomPrompt } =
    useAICommands(editor);
  const { testMarkdownStream } = useMarkdownStream(editor);
  const isScrolling = useScrolling();
  const rect = useCursorVisibility({
    editor,
    overlayHeight: toolbarRef.current?.getBoundingClientRect().height ?? 0,
  });

  return (
    <div className="simple-editor-wrapper">
      <EditorContext.Provider value={{ editor }}>
        <Toolbar
          ref={toolbarRef}
          style={{
            ...(isScrolling && isMobile
              ? { opacity: 0, transition: 'opacity 0.1s ease-in-out' }
              : {}),
            ...(isMobile
              ? {
                  bottom: `calc(100% - ${windowSize.height - rect.y}px)`,
                }
              : {}),
          }}
        >
          {toolbarState.mobileView === 'main' ? (
            <MainToolbar
              aiLoading={aiLoading}
              editor={editor}
              isMobile={isMobile}
              onHideKeyboard={handleHideKeyboard}
              onHighlighterClick={() => setMobileView('highlighter')}
              onLinkClick={() => setMobileView('link')}
              onOpenActions={toggleActionsVisible}
              onOpenAIMenu={toggleAIMenuVisible}
              onTestMarkdown={testMarkdownStream}
            />
          ) : (
            <MobileToolbar
              onBack={() => setMobileView('main')}
              type={
                toolbarState.mobileView === 'highlighter'
                  ? 'highlighter'
                  : 'link'
              }
            />
          )}
        </Toolbar>

        <EditorContent
          className="simple-editor-content"
          editor={editor}
          role="presentation"
        />

        <ActionsPopup
          editor={editor}
          onClose={toggleActionsVisible}
          visible={toolbarState.actionsVisible}
        />

        <AIMenuPopup
          aiLoading={aiLoading}
          aiToneMenuVisible={toolbarState.aiToneMenuVisible}
          editor={editor}
          onAICommand={handleAICommand}
          onClose={() => {
            toggleAIMenuVisible();
            updateToolbarState({ aiToneMenuVisible: false });
          }}
          onCustomPrompt={handleCustomPrompt}
          onToggleAIToneMenu={toggleAIToneMenuVisible}
          visible={toolbarState.aiMenuVisible}
        />
      </EditorContext.Provider>
      <Script
        onLoad={() => {
          const { eruda } = window as any;
          eruda.init();
          eruda.position({ x: 20, y: 240 });
        }}
        src="//cdn.jsdelivr.net/npm/eruda"
      />
    </div>
  );
}
