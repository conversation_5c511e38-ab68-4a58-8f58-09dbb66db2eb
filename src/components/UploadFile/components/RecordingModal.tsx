import clsx from 'clsx';
import { AlertCircle, Check, Mic, Pause, Play, Square, X } from 'lucide-react';
import type React from 'react';
import { useEffect, useRef, useState } from 'react';

interface RecordingModalProps {
  onClose: () => void;
  onComplete: (audioBlob: Blob, duration: number) => void;
}

const RecordingModal: React.FC<RecordingModalProps> = ({
  onClose,
  onComplete,
}) => {
  const [isRecording, setIsRecording] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [amplitudeValues, setAmplitudeValues] = useState(new Array(20).fill(2));
  const [permissionError, setPermissionError] = useState<string | null>(null);
  const [isRequestingPermission, setIsRequestingPermission] = useState(false);

  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const streamRef = useRef<MediaStream | null>(null);

  // 格式化时间
  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // 开始录音
  const startRecording = async () => {
    setIsRequestingPermission(true);
    setPermissionError(null);

    try {
      // 检查浏览器是否支持 getUserMedia
      if (!navigator.mediaDevices?.getUserMedia) {
        throw new Error('您的浏览器不支持录音功能，请使用现代浏览器');
      }

      // 请求麦克风权限
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      streamRef.current = stream;

      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, {
          type: 'audio/wav',
        });
        const url = URL.createObjectURL(audioBlob);
        setAudioUrl(url);
      };

      // 每秒收集数据
      mediaRecorder.start(1000);
      setIsRecording(true);
      setIsPaused(false);
      setRecordingTime(0);

      // 开始计时
      timerRef.current = setInterval(() => {
        setRecordingTime((prev) => prev + 1);
      }, 1000);

      // 模拟音频振幅动画
      const amplitudeInterval = setInterval(() => {
        if (!isPaused) {
          setAmplitudeValues(
            new Array(20).fill(0).map(() => Math.random() * 40 + 2)
          );
        }
      }, 100);

      // 清理振幅动画的定时器
      return () => {
        clearInterval(amplitudeInterval);
      };
    } catch (error: unknown) {
      setIsRequestingPermission(false);

      console.error('获取麦克风权限失败：', error);
      // 设置错误信息
      if (
        error instanceof Error &&
        (error.name === 'NotAllowedError' ||
          error.name === 'PermissionDeniedError')
      ) {
        setPermissionError('麦克风权限被拒绝，请在浏览器设置中允许访问麦克风');
      } else {
        setPermissionError(
          error instanceof Error ? error.message : '录音初始化失败，请重试'
        );
      }
      return;
    } finally {
      setIsRequestingPermission(false);
    }
  };

  // 暂停录音
  const pauseRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      if (isPaused) {
        mediaRecorderRef.current.resume();
        setIsPaused(false);
        timerRef.current = setInterval(() => {
          setRecordingTime((prev) => prev + 1);
        }, 1000);
      } else {
        mediaRecorderRef.current.pause();
        setIsPaused(true);
        if (timerRef.current) {
          clearInterval(timerRef.current);
        }
      }
    }
  };

  // 停止录音
  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
      setIsPaused(false);

      if (timerRef.current) {
        clearInterval(timerRef.current);
      }

      // 停止所有音轨
      if (streamRef.current) {
        for (const track of streamRef.current.getTracks()) {
          track.stop();
        }
      }
    }
  };

  // 播放录音
  const playRecording = () => {
    if (audioRef.current && audioUrl) {
      if (isPlaying) {
        audioRef.current.pause();
        setIsPlaying(false);
      } else {
        audioRef.current.play();
        setIsPlaying(true);
      }
    }
  };

  // 确认录音
  const confirmRecording = () => {
    if (audioChunksRef.current.length > 0) {
      const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/wav' });
      onComplete(audioBlob, recordingTime);
    }
  };

  // 监听音频播放结束
  useEffect(() => {
    const audioElement = audioRef.current;

    const handleEnded = () => {
      setIsPlaying(false);
    };

    if (audioElement) {
      audioElement.addEventListener('ended', handleEnded);
    }

    return () => {
      if (audioElement) {
        audioElement.removeEventListener('ended', handleEnded);
      }
    };
  }, [audioUrl]);

  // 组件卸载时清理资源
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }

      if (streamRef.current) {
        for (const track of streamRef.current.getTracks()) {
          track.stop();
        }
      }

      if (audioUrl) {
        URL.revokeObjectURL(audioUrl);
      }
    };
  }, [audioUrl]);

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="mx-4 w-full max-w-md rounded-lg bg-white shadow-lg">
        {/* 模态框头部 */}
        <div className="flex items-center justify-between border-b p-4">
          <h3 className="font-medium text-lg">录音</h3>
          <button
            className="text-gray-400 hover:text-gray-600"
            onClick={onClose}
            type="button"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* 模态框内容 */}
        <div className="p-6">
          {/* 录音波形图 */}
          <div className="mb-6 flex h-24 items-end justify-center space-x-1">
            {amplitudeValues.map((height, index) => (
              <div
                className={clsx(
                  'w-1 rounded-t bg-green-500 transition-all duration-100',
                  isRecording && !isPaused ? 'opacity-100' : 'opacity-50'
                )}
                key={`amplitude-${Date.now()}-${height}-${index}`}
                style={{ height: `${height}px` }}
              />
            ))}
          </div>

          {/* 录音时间 */}
          <div className="mb-6 text-center font-medium text-2xl">
            {formatTime(recordingTime)}
          </div>

          {/* 错误提示 */}
          {permissionError && (
            <div className="mb-6 flex items-start rounded-lg bg-red-50 p-3 text-red-600">
              <AlertCircle className="mt-0.5 mr-2 h-5 w-5 flex-shrink-0" />
              <p>{permissionError}</p>
            </div>
          )}

          {/* 录音控制按钮 */}
          <div className="flex items-center justify-center space-x-6">
            {isRecording || audioUrl ? (
              isRecording ? (
                // 录音中的控制按钮
                <>
                  <button
                    className="flex h-12 w-12 items-center justify-center rounded-full bg-gray-200 text-gray-700 transition-colors hover:bg-gray-300"
                    onClick={pauseRecording}
                    type="button"
                  >
                    {isPaused ? (
                      <Play className="h-6 w-6" />
                    ) : (
                      <Pause className="h-6 w-6" />
                    )}
                  </button>
                  <button
                    className="flex h-16 w-16 items-center justify-center rounded-full bg-red-500 text-white transition-colors hover:bg-red-600"
                    onClick={stopRecording}
                    type="button"
                  >
                    <Square className="h-6 w-6" />
                  </button>
                </>
              ) : (
                audioUrl && (
                  // 录音完成后的控制按钮
                  <>
                    <button
                      className="flex h-12 w-12 items-center justify-center rounded-full bg-gray-200 text-gray-700 transition-colors hover:bg-gray-300"
                      onClick={playRecording}
                      type="button"
                    >
                      {isPlaying ? (
                        <Pause className="h-6 w-6" />
                      ) : (
                        <Play className="h-6 w-6" />
                      )}
                    </button>
                    <button
                      className="flex h-16 w-16 items-center justify-center rounded-full bg-green-500 text-white transition-colors hover:bg-green-600"
                      onClick={confirmRecording}
                      type="button"
                    >
                      <Check className="h-8 w-8" />
                    </button>
                  </>
                )
              )
            ) : (
              // 开始录音按钮
              <button
                className={clsx(
                  'flex h-16 w-16 items-center justify-center rounded-full text-white transition-colors',
                  isRequestingPermission
                    ? 'cursor-not-allowed bg-gray-400'
                    : 'bg-red-500 hover:bg-red-600'
                )}
                disabled={isRequestingPermission}
                onClick={startRecording}
                type="button"
              >
                {isRequestingPermission ? (
                  <div className="h-8 w-8 animate-spin rounded-full border-4 border-white border-t-transparent" />
                ) : (
                  <Mic className="h-8 w-8" />
                )}
              </button>
            )}
          </div>

          {/* 音频元素（隐藏） */}
          {audioUrl && (
            <audio className="hidden" controls ref={audioRef} src={audioUrl}>
              <track kind="captions" label="中文" src="" />
            </audio>
          )}
        </div>

        {/* 模态框底部 */}
        <div className="border-t p-4 text-gray-500 text-sm">
          {(() => {
            if (isRecording) {
              return '点击方块图标停止录音';
            }
            if (audioUrl) {
              return '点击播放按钮试听，确认无误后点击对勾确认';
            }
            if (isRecording || audioUrl) {
              return null;
            }
            return '点击麦克风图标开始录音';
          })()}
        </div>
      </div>
    </div>
  );
};

export default RecordingModal;
