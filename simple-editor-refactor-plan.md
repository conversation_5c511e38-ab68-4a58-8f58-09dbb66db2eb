# SimpleEditor 组件重构方案

## 当前问题分析

1. **组件过于庞大**：一个文件800+行，包含太多职责
2. **逻辑混杂**：UI渲染、AI处理、编辑器配置、事件处理都在一个组件中
3. **状态管理复杂**：多个相关联的状态散布在组件中
4. **可读性差**：函数过长，嵌套过深
5. **可测试性差**：逻辑耦合严重，难以单独测试

## 重构目标

- 拆分组件职责，遵循单一职责原则
- 提取可复用的自定义 hooks
- 简化主组件逻辑
- 提高代码可读性和可维护性
- 便于单元测试

## 重构方案

### 1. 状态管理层重构

#### 创建自定义 Hooks

**src/components/tiptap-templates/simple/hooks/useEditorConfig.ts**
```ts
// 编辑器配置和初始化逻辑
export function useEditorConfig(content: JSONContent, projectId: string, onChange: (content: JSONContent) => void)
```

**src/components/tiptap-templates/simple/hooks/useAICommands.ts**
```ts
// AI 命令处理逻辑
export function useAICommands(editor: Editor | null)
```

**src/components/tiptap-templates/simple/hooks/useMarkdownStream.ts**
```ts
// Markdown 流式处理逻辑
export function useMarkdownStream(editor: Editor | null)
```

**src/components/tiptap-templates/simple/hooks/useToolbarState.ts**
```ts
// 工具栏状态管理
export function useToolbarState()
```

### 2. 组件拆分

#### 工具栏组件

**src/components/tiptap-templates/simple/components/MainToolbar.tsx**
```tsx
// 主工具栏组件
export function MainToolbar({ editor, onOpenActions, onOpenAIMenu, ... })
```

**src/components/tiptap-templates/simple/components/MobileToolbar.tsx**
```tsx
// 移动端工具栏
export function MobileToolbar({ type, onBack })
```

**src/components/tiptap-templates/simple/components/ToolbarGroups/index.ts**
```tsx
// 工具栏按钮分组
export { FormattingGroup } from './FormattingGroup'
export { HeadingGroup } from './HeadingGroup'
export { MediaGroup } from './MediaGroup'
```

#### 弹窗组件

**src/components/tiptap-templates/simple/components/ActionsPopup.tsx**
```tsx
// 操作面板弹窗
export function ActionsPopup({ visible, onClose, editor })
```

**src/components/tiptap-templates/simple/components/AIMenuPopup.tsx**
```tsx
// AI 菜单弹窗
export function AIMenuPopup({ visible, onClose, onAICommand, aiLoading })
```

**src/components/tiptap-templates/simple/components/AIToneMenu.tsx**
```tsx
// AI 语气调整菜单
export function AIToneMenu({ onSelectTone })
```

### 3. 业务逻辑层重构

#### AI 服务层

**src/components/tiptap-templates/simple/services/aiService.ts**
```ts
// AI 命令处理服务
class AICommandService {
  async executeCommand(command: string, context: CommandContext): Promise<void>
  async executeCustomPrompt(prompt: string, context: PromptContext): Promise<void>
}
```

**src/components/tiptap-templates/simple/services/markdownService.ts**
```ts
// Markdown 处理服务
class MarkdownStreamService {
  insertMarkdownStream(editor: Editor, markdownChunk: string): void
  testMarkdownStream(editor: Editor): void
}
```

#### 工具函数

**src/components/tiptap-templates/simple/utils/editorUtils.ts**
```ts
// 编辑器工具函数
export function getSelectionInfo(editor: Editor)
export function insertContentAtPosition(editor: Editor, content: string, position: number)
export function handleKeyboardHide(editor: Editor)
```

**src/components/tiptap-templates/simple/utils/contentUtils.ts**
```ts
// 内容处理工具函数
export function buildAIQuery(command: string, selectedText: string, fullDocument: string)
export function detectMarkdownContent(content: string): boolean
```

### 4. 类型定义

**src/components/tiptap-templates/simple/types/index.ts**
```ts
// 统一的类型定义
export interface EditorProps {
  onChange: (content: JSONContent) => void
  content: JSONContent
  projectId: string
}

export interface AICommandContext {
  editor: Editor
  selectedText: string
  fullDocument: string
  cursorPosition: number
}

export type MobileViewType = 'main' | 'highlighter' | 'link'
export type AICommandType = 'continue' | 'summarize' | 'polish' | 'expand' | 'shorten' | 'adjustTone'
```

### 5. 常量提取

**src/components/tiptap-templates/simple/constants/index.ts**
```ts
// 常量定义
export const AI_OUTPUT_INSTRUCTION = '\n\n请直接输出内容，不要包含额外的解释...'
export const AI_CONTENT_TAG = '[AI_CONTENT]'
```

### 6. 重构后的主组件结构

**src/components/tiptap-templates/simple/simple-editor.tsx**
```tsx
export function SimpleEditor({ onChange, content, projectId }: EditorProps) {
  // 使用自定义 hooks
  const editor = useEditorConfig(content, projectId, onChange)
  const { executeAICommand, aiLoading } = useAICommands(editor)
  const { insertMarkdownStream, testMarkdownStream } = useMarkdownStream(editor)
  const { toolbarState, setToolbarState } = useToolbarState()
  
  // 简化的事件处理
  const handleHideKeyboard = useCallback(() => {
    handleKeyboardHide(editor)
  }, [editor])
  
  return (
    <div className="simple-editor-wrapper">
      <EditorContext.Provider value={{ editor }}>
        <MainToolbar
          editor={editor}
          toolbarState={toolbarState}
          onOpenActions={() => setToolbarState(prev => ({ ...prev, actionsVisible: true }))}
          onOpenAIMenu={() => setToolbarState(prev => ({ ...prev, aiMenuVisible: true }))}
          onHideKeyboard={handleHideKeyboard}
          aiLoading={aiLoading}
          onTestMarkdown={testMarkdownStream}
        />
        
        <EditorContent className="simple-editor-content" editor={editor} />
        
        <ActionsPopup
          visible={toolbarState.actionsVisible}
          onClose={() => setToolbarState(prev => ({ ...prev, actionsVisible: false }))}
          editor={editor}
        />
        
        <AIMenuPopup
          visible={toolbarState.aiMenuVisible}
          onClose={() => setToolbarState(prev => ({ ...prev, aiMenuVisible: false }))}
          onAICommand={executeAICommand}
          aiLoading={aiLoading}
        />
      </EditorContext.Provider>
    </div>
  )
}
```

## 实施步骤

1. **第一步**：创建类型定义和常量文件
2. **第二步**：提取和重构自定义 hooks
3. **第三步**：拆分工具栏相关组件
4. **第四步**：拆分弹窗组件
5. **第五步**：创建服务层和工具函数
6. **第六步**：重构主组件，整合所有拆分的部分
7. **第七步**：添加单元测试
8. **第八步**：优化样式和文档

## 预期收益

- **可维护性提升**：每个文件职责单一，易于理解和修改
- **可复用性增强**：hooks 和组件可在其他地方复用
- **可测试性改善**：拆分后的函数和组件更容易编写单元测试
- **开发效率提高**：团队成员可以并行开发不同的模块
- **Bug 率降低**：逻辑清晰，减少意外的副作用

## 注意事项

- 保持向后兼容性，不改变组件的外部接口
- 分步骤实施，确保每一步都能正常工作
- 添加适当的 TypeScript 类型检查
- 保留原有的样式和交互体验
- 考虑性能影响，避免不必要的重渲染