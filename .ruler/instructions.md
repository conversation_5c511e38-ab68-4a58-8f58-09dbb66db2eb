# Ruler Instructions

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Common commands

- npm run dev            # start development server
- npm run build          # build production bundles
- npm run start          # run production build locally
- npm run lint           # run boime checks
- npm run format         # auto-format code (b<PERSON><PERSON> <PERSON>er)
- npm run check-types    # run TypeScript type check
- npm test               # run unit tests (Jest)
- npm run test:e2e       # run Playwright integration/E2E tests
  
## Single-test invocation

- npm test -- <path_or_pattern>  # run specific Jest test file or suite

## High-level architecture

- Next.js 14 project with both App Router (`src/app/`) and legacy Page Router (`src/pages/`).
- UI components:
  - Global/shared: `src/components/`
  - Route-specific: alongside pages in `src/app/...`, e.g. `src/app/feature/components/`
- API:
  - Client-side wrappers in `src/api/`
  - Next.js API routes in `src/app/api/`
- Styling and assets:
  - Tailwind CSS configured via `tailwind.config.js`
  - Global styles in `src/styles/`
  - Public assets in `public/`
- Utilities:
  - `src/lib/` for core helpers (fetch, logger, templates)
  - `src/utils/` for miscellaneous utility functions
- State management:
  - Zustand/Jotai stores in `src/store/`
- Monorepo tooling and config:
  - Linting: ESLint + Biome (`biome.json`)
  - Formatting: Prettier
  - Git hooks: Husky + lint-staged
  - Commit lint: Commitlint + Commitizen

## 界面和交互要求

1. **简洁清晰**：界面干净，信息明确，避免视觉杂乱。
2. **层次分明**：通过对比突出重点，内容结构清晰。
3. **一致性强**：风格、布局统一，操作逻辑一致。
4. **易于识别**：图标、文字直观，用户快速理解功能。
5. **响应式设计**：适配多设备，内容在各屏幕上都清晰。
6. **高效操作**：减少步骤，提供快捷方式和自动化功能。
7. **及时反馈**：操作后有动画或提示，指导用户行为。
8. **可控性好**：支持撤销、修改，导航路径清晰。
9. **情感化设计**：增加趣味性，提供友好和舒适体验。

## 输出要求

* 图标使用lucide-react,UI组件库使用antd-mobile
* 页面样式完全使用 tailwind css，除非不得已才使用自定义css，不要写内联 CSS 样式
* 不要使用蓝紫渐变
* 使用 clsx 来处理页面内部的 css 交互逻辑
* 确保代码简洁高效，注重性能和可维护性
* 对于超感官极简主义风格，必须精确控制每个像素和微妙的交互反馈
* 对于新表现主义数据可视化风格，必须将数据以视觉化方式融入设计
* 请按照我给你的功能需求，页面尽可能模拟真实的用户数据来展示，包括用户姓名和头像，图片使用 unslash
* 代码应当优雅且符合最佳实践，遵守设计规范，UI应体现出对细节的极致追求
